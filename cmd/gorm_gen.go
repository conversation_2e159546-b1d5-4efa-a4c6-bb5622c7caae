package main

import (
	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

const MODELS_DIR = "./internal/models/"

const QUERY_DIR = "./internal/models/query/"

const (
	tagKeySerializer = "serializer"

	tagValueProtoJson = "proto_json"
)

func main() {
	genMysqlModel()
	genPGModel()
}

// https://gorm.io/gen/database_to_structs.html#Generate-From-Sql
func genMysqlModel() {
	g := gen.NewGenerator(gen.Config{
		OutPath:      QUERY_DIR,
		ModelPkgPath: MODELS_DIR,
		Mode:         gen.WithDefaultQuery | gen.WithQueryInterface,
		// 表字段默认值与模型结构体字段零值不一致的字段, 在插入数据时需要赋值该字段值为零值的, 结构体字段须是指针类型才能成功, 即`FieldCoverable:true`配置下生成的结构体字段.
		// 因为在插入时遇到字段为零值的会被GORM赋予默认值. 如字段`age`表默认值为10, 即使你显式设置为0最后也会被GORM设为10提交.
		// 如果该字段没有上面提到的插入时赋零值的特殊需要, 则字段为非指针类型使用起来会比较方便.
		FieldCoverable: true, // generate pointer when field has default value, to fix problem zero value cannot be assign: https://gorm.io/docs/create.html#Default-Values
		// if you want the nullable field generation property to be pointer type, set FieldNullable true
		FieldNullable: true, // generate pointer when field is nullable
	})
	gormdb, _ := gorm.Open(mysql.Open("moego_developer_240310_eff7a0dc:G0MxI7NM_jX_f7Ky73vnrwej97xg1tly@tcp(mysql.t2.moego.dev:40107)/moe_grooming"))
	g.UseDB(gormdb) // reuse your gorm db

	//从当前数据库中生成所有表的结构
	g.GenerateModel("moe_grooming_service",
		gen.FieldType("inactive", "*bool"),
		gen.FieldTypeReg(".*filter$", "*bool"),
		gen.FieldTypeReg("^is.*", "*bool"),
		gen.FieldTypeReg("require_dedicated_.*", "*bool"),
		gen.FieldType("prerequisite_class_ids", "[]int64"),
		getSerializerTag("prerequisite_class_ids", "json"),
		gen.FieldType("additional_service_rule", "*offeringpb.AdditionalServiceRule"),
		getTag("additional_service_rule", tagKeySerializer, tagValueProtoJson),
	)
	g.GenerateModel("moe_grooming_service_category")
	g.GenerateModel("moe_grooming_service_breed_binding")
	g.GenerateModel("moe_grooming_service_coat_binding")
	g.GenerateModel("moe_grooming_service_location",
		gen.FieldType("is_deleted", "*bool"))
	g.GenerateModel("moe_grooming_addon_applicable_service",
		gen.FieldType("available_for_all_services", "*bool"))
	g.GenerateModel("moe_grooming_customer_services",
		gen.FieldType("service_type", "int32"),
	)
	g.GenerateModel("moe_auto_rollover",
		gen.FieldType("enabled", "*bool"))
	g.GenerateModel("moe_service_pet_code_filter",
		gen.FieldType("pet_code_list", "[]int64"),
		getSerializerTag("pet_code_list", "json"),
	)
	// 生成代码
	g.Execute()
}

func genPGModel() {
	g := gen.NewGenerator(gen.Config{
		OutPath:      QUERY_DIR,
		ModelPkgPath: MODELS_DIR,
		Mode:         gen.WithDefaultQuery | gen.WithQueryInterface,
		// if you want the nullable field generation property to be pointer type, set FieldNullable true
		FieldNullable: true, // generate pointer when field is nullable
		// 表字段默认值与模型结构体字段零值不一致的字段, 在插入数据时需要赋值该字段值为零值的, 结构体字段须是指针类型才能成功, 即`FieldCoverable:true`配置下生成的结构体字段.
		// 因为在插入时遇到字段为零值的会被GORM赋予默认值. 如字段`age`表默认值为10, 即使你显式设置为0最后也会被GORM设为10提交.
		// 如果该字段没有上面提到的插入时赋零值的特殊需要, 则字段为非指针类型使用起来会比较方便.
		FieldCoverable: true, // generate pointer when field has default value, to fix problem zero value cannot be assign: https://gorm.io/docs/create.html#Default-Values
	})
	gormdb, _ := gorm.Open(postgres.Open("postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_offering?sslmode=disable"))
	g.UseDB(gormdb) // reuse your gorm db

	g.ApplyBasic(
		// 从当前数据库中生成所有表的结构
		g.GenerateModel("lodging_type",
			gen.FieldIgnore("max_pet_total_weight"), // 废弃字段
			gen.FieldType("photo", "[]string"),
			getSerializerTag("photo", "json"),
			getNoAutoUpdateTimeTag("updated_at"),
			gen.FieldType("allowed_pet_size_list", "pq.Int64Array"),
			getTypeTag("allowed_pet_size_list", "bigint[]"),
			gen.FieldType("type", "v1.LodgingUnitType"),
			gen.FieldType("source", "v1.LodgingTypeModel_Source"),
		),
		g.GenerateModel("lodging_unit", getNoAutoUpdateTimeTag("updated_at")),
		g.GenerateModel("evaluation",
			gen.FieldType("available_business_id_list", "pq.Int64Array"),
			getTypeTag("available_business_id_list", "bigint[]"),
			gen.FieldType("service_item_type_list", "pq.Int32Array"),
			getTypeTag("service_item_type_list", "int[]"),
			gen.FieldType("allowed_lodging_list", "pq.Int64Array"),
			getTypeTag("allowed_lodging_list", "bigint[]"),
			gen.FieldType("allowed_staff_list", "pq.Int64Array"),
			getTypeTag("allowed_staff_list", "bigint[]"),
			gen.FieldType("source", "offeringModelsV1.EvaluationModel_Source"),
		),
		g.GenerateModel("service_staff_availability", getNoAutoUpdateTimeTag("updated_at")),
		g.GenerateModel("service_staff_override_rule", getNoAutoUpdateTimeTag("updated_at")),
		g.GenerateModel("vaccine_requirement",
			gen.FieldType("service_item_type", "offeringpb.ServiceItemType"),
			getDefaultTag("service_item_type", "0"),
			getSerializerTag("service_item_type", "proto_enum"),
		),
		g.GenerateModel("service_bundle_sale_mapping", getNoAutoUpdateTimeTag("updated_at")),
		g.GenerateModel("discount_setting",
			gen.FieldType("apply_sequence", "[]offeringModelsV2.RuleType"),
			getSerializerTag("apply_sequence", "json"),
		),
		g.GenerateModel("pricing_rule_record",
			gen.FieldType("rule_type", "offeringModelsV2.RuleType"),
			gen.FieldType("selected_boarding_services", "[]int64"),
			getSerializerTag("selected_boarding_services", "json"),
			gen.FieldType("selected_daycare_services", "[]int64"),
			getSerializerTag("selected_daycare_services", "json"),
			gen.FieldType("rule_apply_type", "offeringModelsV2.RuleApplyType"),
			gen.FieldType("rule_configuration", "*offeringModelsV2.PricingRuleConfiguration"),
			gen.FieldType("source", "offeringModelsV2.Source"),
			getTag("rule_configuration", tagKeySerializer, tagValueProtoJson),
			gen.FieldType("selected_grooming_services", "[]int64"),
			getSerializerTag("selected_grooming_services", "json"),
			gen.FieldType("selected_addon_services", "[]int64"),
			getSerializerTag("selected_addon_services", "json"),
		),
		g.GenerateModel("playgroup", getNoAutoUpdateTimeTag("updated_at")),
		g.GenerateModel("evaluation_pet_breed_filter",
			gen.FieldType("pet_type", "customerpb.PetType"),
			gen.FieldType("breed_names", "pq.StringArray"),
			getTypeTag("breed_names", "text[]"),
		),
		g.GenerateModel("evaluation_location_override"),
		g.GenerateModel("customize_care_type",
			gen.FieldType("service_item_type", "offeringpb.ServiceItemType"),
		),
	)

	// 生成代码
	g.Execute()
}

func getTag(columnName, tagKey string, tagValues ...string) gen.ModelOpt {
	return gen.FieldGORMTag(columnName, func(tag field.GormTag) field.GormTag {
		tag.Set(tagKey, tagValues...)
		return tag
	})
}

func getSerializerTag(columnName string, serializer string) gen.ModelOpt {
	return gen.FieldGORMTag(columnName, func(tag field.GormTag) field.GormTag {
		tag.Set("serializer", serializer)
		return tag
	})
}

func getNoAutoUpdateTimeTag(columnName string) gen.ModelOpt {
	return gen.FieldGORMTag(columnName, func(tag field.GormTag) field.GormTag {
		tag.Set("autoUpdateTime", "false")
		return tag
	})
}

func getTypeTag(columnName string, typeName string) gen.ModelOpt {
	return gen.FieldGORMTag(columnName, func(tag field.GormTag) field.GormTag {
		tag.Set("type", typeName)
		return tag
	})
}

func getDefaultTag(columnName string, defaultValue string) gen.ModelOpt {
	return gen.FieldGORMTag(columnName, func(tag field.GormTag) field.GormTag {
		tag.Set("default", defaultValue)
		return tag
	})
}
