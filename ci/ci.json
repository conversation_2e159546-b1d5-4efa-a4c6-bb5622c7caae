{"$schema": "https://moego.s3.us-west-2.amazonaws.com/ops/github-actions/ci-json-schema.json", "service_name": "moego-svc-offering", "language": {"type": "go", "version": "1.24"}, "install": {"commands": ["go mod download"]}, "lint": {"commands": ["golangci-lint run -v --allow-parallel-runners --timeout 10m"]}, "test": {"commands": ["mkdir -p output", "go test -coverprofile output/cover.out -coverpkg ./internal/service/... -v ./internal/... 2>&1 | go-junit-report -set-exit-code -iocopy -out output/report.xml", "gocov convert output/cover.out | gocov-xml > output/cover.xml"], "report": "./output/report.xml", "coverage": "./output/cover.xml", "coverage_gate": 30}, "build": {"commands": ["CGO_ENABLED=0 go build -o moego-svc-offering"], "build_image": [{"dockerfile": "ci/Dockerfile", "context": "."}]}, "deploy": {"type": "service"}}