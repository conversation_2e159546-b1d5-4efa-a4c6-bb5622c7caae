create table moe_grooming.moe_service_pet_code_filter
(
    id bigint primary key auto_increment,
    service_id bigint not null default 0,
    is_whitelist boolean not null default true,
    is_all_pet_code boolean not null default true,
    pet_code_list json not null default (json_array()) comment 'json array of pet code, only valid when is_all_pet_code is false',
    unique index idx_service_id(service_id)
);
