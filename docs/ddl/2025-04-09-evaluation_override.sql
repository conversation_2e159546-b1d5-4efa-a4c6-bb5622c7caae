create table evaluation_location_override
(
    id            bigserial,
    evaluation_id bigint    default 0                 not null,
    business_id   bigint    default 0                 not null,
    price         decimal(20, 4)                      null,
    duration      int                                 null,
    created_at    timestamp default CURRENT_TIMESTAMP not null,
    updated_at    timestamp default CURRENT_TIMESTAMP not null,
    deleted_at    timestamp
);

create unique index uni_location_override on evaluation_location_override (evaluation_id, business_id);