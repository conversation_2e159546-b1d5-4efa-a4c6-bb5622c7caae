create table pricing_rule
(
    id                        bigserial
        primary key,
    company_id                bigint                              not null,
    apply_service_type        smallint  default 0                 not null,
    apply_service_item_type   smallint  default 0                 not null,
    rule_name                 varchar(50)                         not null,
    is_all_service_applicable boolean                             not null,
    selected_services         jsonb     default '[]'::jsonb,
    rule_configuration        jsonb     default '{}'::jsonb,
    is_active                 boolean                             not null,
    updated_by                bigint                              not null,
    created_at                timestamp default CURRENT_TIMESTAMP not null,
    updated_at                timestamp default CURRENT_TIMESTAMP not null,
    deleted_at                timestamp
);

create index idx_company_id
    on pricing_rule (company_id, apply_service_type, apply_service_item_type, is_active);

create unique index idx_pricing_rule_name
    on pricing_rule (company_id, rule_name);