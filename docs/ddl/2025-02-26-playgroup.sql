create table playgroup
(
    id               bigserial
        constraint playgroup_pk
            primary key,
    company_id       bigint                   default 0                            not null,
    name             varchar(20)              default ''::character varying        not null,
    color_code       varchar(7)               default ''::character varying not null,
    max_pet_capacity integer                  default 0                            not null,
    sort             integer                  default 0                            not null,
    description      varchar(100)             default ''::character varying        not null,
    created_at       timestamp with time zone default CURRENT_TIMESTAMP            not null,
    updated_at       timestamp with time zone default CURRENT_TIMESTAMP            not null,
    created_by       bigint                   default 0                            not null,
    updated_by       bigint                   default 0                            not null,
    deleted_at       timestamp with time zone,
    deleted_by       bigint                   default 0                            not null
);

comment on column public.playgroup.company_id is 'company id';

comment on column public.playgroup.color_code is 'playgroup color code';

comment on column public.playgroup.max_pet_capacity is 'max pet capacity';

comment on column public.playgroup.sort is 'Playgroup list sort. Start with 1 and put the smallest first';

create index playgroup_company_id_index
    on public.playgroup (company_id);

