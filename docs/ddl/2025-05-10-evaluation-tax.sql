alter table public.evaluation
  ADD COLUMN tax_id bigint default 0 not null,
  ADD COLUMN created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  ADD COLUMN updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  ADD COLUMN deleted_at timestamp;

ALTER TABLE `moe_grooming`.`moe_grooming_service`
    ADD COLUMN `is_evaluation_required` TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'whether evaluation is required',
    ADD COLUMN `is_evaluation_required_for_ob` TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'whether evaluation is required before online booking',
    ADD COLUMN `evaluation_id` BIGINT default 0 not null  COMMENT 'related evaluation service id';