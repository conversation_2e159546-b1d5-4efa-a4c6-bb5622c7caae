-- Description: Create tables for class instance, session and activity log
-- group_class_instance
CREATE TABLE group_class_instance
(
    id                    BIGSERIAL PRIMARY KEY,
    created_at            TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at            TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at            TIMESTAMP WITH TIME ZONE,
    company_id            BIGINT                   NOT NULL DEFAULT 0,
    business_id           BIGINT                   NOT NULL DEFAULT 0,
    service_id            BIGINT                   NOT NULL DEFAULT 0,
    name                  VARCHAR(100)             NOT NULL DEFAULT '' :: CHARACTER VARYING,
    staff_id              BIGINT                   NOT NULL DEFAULT 0,
    price                 NUMERIC(10, 4)           NOT NULL DEFAULT 0,
    tax_id                BIGINT                   NOT NULL DEFAULT 0,
    currency_code         TEXT                     NOT NULL DEFAULT 'USD',
    start_time            TIMESTAMP WITH TIME ZONE NOT NULL,
    time_zone             TEXT NOT NULL DEFAULT 'America/Los_Angeles',
    capacity              BIGINT                   NOT NULL DEFAULT 0,
    occurrence            TEXT NOT NULL DEFAULT '',
    status                TEXT NOT NULL DEFAULT 'STATUS_UNSPECIFIED'
);

CREATE INDEX idx_group_class_instance_business_id ON group_class_instance(business_id);
CREATE INDEX idx_group_class_instance_company_id ON group_class_instance(company_id);
CREATE INDEX idx_group_class_instance_status ON group_class_instance(status);

COMMENT
    ON COLUMN group_class_instance.company_id IS 'Company ID';
COMMENT
    ON COLUMN group_class_instance.business_id IS 'Business ID';
COMMENT
    ON COLUMN group_class_instance.service_id IS 'alias group_class_id';
COMMENT
    ON COLUMN group_class_instance.staff_id IS 'Trainer ID';
COMMENT
    ON COLUMN group_class_instance.occurrence IS 'json::models/offering/v1/group_class_models/group_class_instance.occurrence';
COMMENT
    ON COLUMN group_class_instance.status IS 'enum:models/offering/v1/group_class_models/group_class_instance.status'


-- group_class_session
CREATE TABLE group_class_session
(
    id                  BIGSERIAL PRIMARY KEY,
    created_at          TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at          TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at          TIMESTAMP WITH TIME ZONE,
    company_id          BIGINT                   NOT NULL DEFAULT 0,
    business_id         BIGINT                   NOT NULL DEFAULT 0,
    instance_id BIGINT                   NOT NULL DEFAULT 0,
    start_time          TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_min        BIGINT                   NOT NULL DEFAULT 0,
    is_modified         BOOLEAN                  NOT NULL DEFAULT FALSE
);

CREATE INDEX idx_group_class_session_instance_id ON group_class_session (instance_id);

COMMENT
    ON COLUMN group_class_session.company_id IS 'Company ID';
COMMENT
    ON COLUMN group_class_session.start_time IS 'Session start time';
COMMENT
    ON COLUMN group_class_session.duration_min IS 'Duration of sessions in minutes';
COMMENT
    ON COLUMN group_class_session.is_modified IS 'Manually modified flag';

CREATE TABLE event
(
    id              BIGSERIAL PRIMARY KEY,
    created_at      TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at      TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    message_type    TEXT NOT NULL DEFAULT 'TYPE_UNSPECIFIED',
    reference_id    TEXT NOT NULL DEFAULT '',
    payload         TEXT NOT NULL DEFAULT '{}',
    status          TEXT NOT NULL DEFAULT '',
    retry_times     BIGINT NOT NULL DEFAULT 0
);

CREATE INDEX idx_event_reference_id ON event (reference_id);
CREATE INDEX idx_event_status ON event (status);


-- session_activity_log
-- CREATE TABLE session_activity_log
-- (
--     id                  BIGSERIAL PRIMARY KEY,
--     created_at          TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
--     updated_at          TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
--     deleted_at          TIMESTAMP WITH TIME ZONE,
--     company_id          BIGINT                   NOT NULL DEFAULT 0,
--     business_id         BIGINT                   NOT NULL DEFAULT 0,
--     class_session_id BIGINT                   NOT NULL DEFAULT 0,
--     operator            BIGINT                   NOT NULL DEFAULT 0,
--     before_data         JSONB                             DEFAULT json_object('{}'),
--     after_data          JSONB                             DEFAULT json_object('{}')
-- );

-- CREATE INDEX idx_log_operator ON session_activity_log (operator);
-- CREATE INDEX idx_log_company_training_session ON session_activity_log (company_id, class_session_id);
-- CREATE INDEX idx_log_business_training_session ON session_activity_log (business_id, class_session_id);

-- COMMENT
--     ON COLUMN session_activity_log.company_id IS 'Company ID';
-- COMMENT
--     ON COLUMN session_activity_log.class_session_id IS 'Group class session ID';
-- COMMENT
--     ON COLUMN session_activity_log.operator IS 'Staff ID';
-- COMMENT
--     ON COLUMN session_activity_log.before_data IS 'Session start time before edit';
-- COMMENT
--     ON COLUMN session_activity_log.after_data IS 'Session start time after edit';