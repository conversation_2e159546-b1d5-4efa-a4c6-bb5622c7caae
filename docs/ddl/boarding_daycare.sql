alter table moe_grooming.moe_grooming_service add column images json default ('[]');
alter table moe_grooming.moe_grooming_service add column service_item_type int not null default 1 comment '1 - grooming, 2 - boarding, 3 - daycare';
alter table moe_grooming.moe_grooming_service add column price_unit int not null default 1 comment '1 - per session, 2 - per night, 3 - per hour';
alter table moe_grooming.moe_grooming_service add column add_to_commission bool not null default true;
alter table moe_grooming.moe_grooming_service add column can_tip bool not null default true;
alter table moe_grooming.moe_grooming_service add column require_dedicated_staff bool not null default true;
alter table moe_grooming.moe_grooming_service add column require_dedicated_lodging bool not null default false;
alter table moe_grooming.moe_grooming_service add column lodging_filter bool not null default false;
alter table moe_grooming.moe_grooming_service add column allowed_lodging_list json default ('[]') comment 'allowed lodging id list, only when require_dedicated_lodging and lodging_filter is true';
alter table moe_grooming.moe_grooming_service add column service_filter bool not null default false comment 'only for add-on';
alter table moe_grooming.moe_grooming_service add column allowed_pet_size_list json default ('[]') comment 'allowed pet size list, only when service_filter is true';
alter table moe_grooming.moe_grooming_service add column pet_size_filter bool not null default false;
alter table moe_grooming.moe_grooming_service add column max_duration int not null default 0 comment 'max duration in minutes, only for daycare';

alter table moe_grooming.moe_grooming_service_location add max_duration int default null comment 'max duration in minutes, only for daycare';

alter table moe_grooming.moe_grooming_service_category add column service_item_type int not null default 1 comment '1 - grooming, 2 - boarding, 3 - daycare';

create table moe_grooming.moe_grooming_addon_applicable_service (
    id bigint auto_increment primary key,
    company_id bigint not null default 0,
    addon_id bigint not null default 0 comment 'moe_grooming_service.id',
    service_item_type int not null default 0 comment '1 - grooming, 2 - boarding, 3 - daycare',
    available_for_all_services bool not null default false comment 'false for all service in service_item_type, true for specific service',
    available_service_id_list json default ('[]') comment 'customized applicable service list, only when service_filter is true',
    created_at timestamp default current_timestamp,
    updated_at timestamp default current_timestamp,
    unique key idx_cid_aid_type (company_id, addon_id, service_item_type)
);

CREATE TABLE public.lodging_type (
     id bigint NOT NULL,
     company_id bigint DEFAULT 0 NOT NULL,
     description character varying(2048) DEFAULT ''::character varying NOT NULL,
     name character varying(500) DEFAULT ''::character varying NOT NULL,
     photo jsonb DEFAULT '[]'::jsonb NOT NULL,
     max_pet_num integer DEFAULT 0 NOT NULL,
     max_pet_total_weight integer DEFAULT 0 NOT NULL,
     created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
     created_by bigint DEFAULT 0 NOT NULL,
     updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
     updated_by bigint DEFAULT 0 NOT NULL,
     deleted_at timestamp with time zone,
     deleted_by bigint DEFAULT 0 NOT NULL
);

CREATE SEQUENCE public.lodging_type_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE public.lodging_type_id_seq OWNED BY public.lodging_type.id;
ALTER TABLE ONLY public.lodging_type ALTER COLUMN id SET DEFAULT nextval('public.lodging_type_id_seq'::regclass);


CREATE TABLE public.lodging_unit (
     id bigint NOT NULL,
     company_id bigint DEFAULT 0 NOT NULL,
     business_id bigint DEFAULT 0 NOT NULL,
     lodging_type_id bigint DEFAULT 0 NOT NULL,
     name character varying(500) DEFAULT ''::character varying NOT NULL,
     created_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
     created_by bigint DEFAULT 0 NOT NULL,
     updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
     updated_by bigint DEFAULT 0 NOT NULL,
     deleted_at timestamp with time zone,
     deleted_by bigint DEFAULT 0 NOT NULL
);

CREATE SEQUENCE public.lodging_unit_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER SEQUENCE public.lodging_unit_id_seq OWNED BY public.lodging_unit.id;

ALTER TABLE ONLY public.lodging_unit ALTER COLUMN id SET DEFAULT nextval('public.lodging_unit_id_seq'::regclass);

ALTER TABLE ONLY public.lodging_unit
    ADD CONSTRAINT lodging_unit_pk PRIMARY KEY (id);

CREATE UNIQUE INDEX idx_business_type_name ON public.lodging_unit USING btree (business_id, lodging_type_id, name) WHERE (deleted_at IS NULL);
