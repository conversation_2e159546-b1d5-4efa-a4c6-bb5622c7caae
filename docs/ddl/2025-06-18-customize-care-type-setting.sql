--service setting care type:
CREATE TABLE "public"."customize_care_type" (
                                                "id" bigserial PRIMARY KEY,
                                                "name" VARCHAR (150) NOT NULL,
                                                company_id BIGINT NOT NULL,
                                                service_item_type SMALLINT DEFAULT 0 NOT NULL,
                                                sort INT NOT NULL DEFAULT '0',
                                                updated_by BIGINT NOT NULL,
                                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
                                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

CREATE UNIQUE INDEX idx_company_id ON customize_care_type (company_id, service_item_type);
CREATE UNIQUE INDEX idx_name ON customize_care_type (company_id, "name");