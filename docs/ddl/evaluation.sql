CREATE TABLE evaluation (
                            id BIGSERIAL PRIMARY KEY,
                            name <PERSON><PERSON><PERSON><PERSON>(128) NOT NULL,
                            company_id BIGINT NOT NULL DEFAULT 0,
                            available_for_all_business BOOLEAN DEFAULT TRUE,
                            available_business_id_list BIGINT[] DEFAULT array[]::BIGINT[],
                            service_item_type_list INT[] DEFAULT array[]::INT[],
                            price DECIMAL(10, 4) DEFAULT 0,
                            duration INT DEFAULT 0,
                            color_code VARCHAR(10) DEFAULT '#000000',
                            is_active BOOLEAN DEFAULT TRUE
);

create unique index on evaluation idx_evaluation_cid (company_id);

alter table public.evaluation
    add lodging_filter boolean default false not null;
alter table public.evaluation
    add allowed_lodging_list bigint[] default ARRAY []::bigint[] not null
    comment on column public.evaluation.allowed_lodging_list is 'allowed lodging id list, only when require_dedicated_lodging and lodging_filter is true';
alter table public.evaluation
    add description varchar(2000) default '' not null;
update public.evaluation set description = 'To secure services, we kindly request scheduling an on-site evaluation to determine suitability for our facility.' where description = '';

alter table public.evaluation
    add online_booking_alias varchar(128);
comment on column public.evaluation.online_booking_alias is 'name shown in ob flow';

alter table public.evaluation
    add is_online_book_available boolean default true not null;
drop index public.idx_evaluation_cid;
create index idx_evaluation_cid on public.evaluation (company_id);

alter table public.evaluation
    add is_all_staff boolean default true not null;
alter table public.evaluation
    add allowed_staff_list bigint[] default ARRAY []::bigint[] not null;
comment on column public.evaluation.allowed_staff_list is 'allowed staff id list, only when is_all_staff is true';
alter table public.evaluation
    add allow_staff_auto_assign boolean default false not null;
comment on column public.evaluation.allow_staff_auto_assign is 'whether to support automatic allocation of staff';