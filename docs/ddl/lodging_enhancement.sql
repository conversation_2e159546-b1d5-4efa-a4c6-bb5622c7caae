alter table public.lodging_type
    add pet_size_filter bool default false not null;
comment on column public.lodging_type.pet_size_filter is 'if need pet size filter';
alter table public.lodging_type
    add allowed_pet_size_list bigint[] default ARRAY []::bigint[] not null;
comment on column public.lodging_type.allowed_pet_size_list is 'allowed pet size list, only when service_filter is true';
alter table public.lodging_type
    add type SMALLINT default 1 not null;
comment on column public.lodging_type.type is 'lodging 类型：1-Room/kennel type；2-Area type'
