# moego-svc-offering

server for offerings management, include:
- service
- add-on
- package
- retail
- lodging

## Development

1. init project

    ```bash
    make init
    ```

2. Update Dependencies

    ```bash
    go mod tidy
    ```

3. Update GoPrivate library

    ```bash
    export GOPRIVATE=github.com/MoeGolibrary
    ```

4. project structure
   当前项目的目录结构：

   ```
   ├── Makefile
   ├── README.md
   ├── ci
   │   ├── Dockerfile
   │   ├── ci.yaml
   │   └── lint.sh
   ├── config
   │   └── config.go
   ├── config.yaml
   ├── docs
   │   └── ddl
   ├── go.mod
   ├── go.sum
   ├── hooks
   │   └── pre-commit
   ├── internal
   │   ├── constant
   │   ├── models
   │   ├── repository
   │   ├── resource
   │   │   ├── db.go
   │   │   └── timezone.go
   │   ├── server
   │   │   └── server.go
   │   ├── service
   │   │   ├── converter
   │   │   └── health_service.go
   │   └── utils
   └── main.go
   
其中:
- `ci` 目录下是ci的配置文件，包括Dockerfile、ci.yaml、lint.sh，每个服务按照自己需要修改其中的服务名
- `config` 目录下是配置文件的结构体定义，以及配置文件的读取
- `docs` 目录下是数据库的ddl 和 dml 文件，用来做记录
- `internal` 是项目的主要代码目录，其中包括了:
    - `constant` 常量的定义
    - `models` 数据库表的结构体定义
    - `repository` 数据库的操作，包括了增删改查，以及一些复合查询
    - `resource` 资源的定义，目前主要包括了数据库连接、时区等，每个服务可以依照自己的需求增加
    - `server` 服务启动的入口
    - `service` GRPC 接口的实现
    - `utils` 目录下是工具类的定义
- 
5. How to generate models files
- Run the following command in the project root directory to generate model files:
    ```bash
    go run cmd/gorm_gen.go
    ```
- During the generation process, an error message like "expected ';', found '-'" may appear. You can safely ignore it as it does not affect the result. 😄 
- If you have specific field mapping requirements, you can modify the cmd/gorm_gen.go file.

## Other lib

1. mockgen

   First, install mockgen:

    ```bash
    go install go.uber.org/mock/mockgen@latest
    ```
   
    Then, generate mock file, for example:
    
     ```bash
    mockgen -package mocks -destination internal/mocks/mock_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceRepository
    ```
    
    Reference: [mockgen](https://github.com/uber-go/mock)
2. moego-api-definition

   To get the latest moego-api-definitions, use the following command:

    ```bash
    go get github.com/MoeGolibrary/moego-api-definitions
    ```

    Then, generate special branch, for example:
     ```bash
    go get github.com/MoeGolibrary/moego-api-definitions@feature-xxx
    ```