package converter

import (
	"time"

	offeringsvcmodels "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func ConverterCreatePlaygroupRequestToDO(request *offeringsvcpb.CreatePlaygroupRequest) *do.PlaygroupDO {
	if request == nil {
		return nil
	}
	return &do.PlaygroupDO{
		CompanyID:      request.CompanyId,
		Name:           request.Playgroup.Name,
		ColorCode:      request.Playgroup.ColorCode,
		MaxPetCapacity: request.Playgroup.MaxPetCapacity,
		Description:    request.Playgroup.Description,
		OperateId:      request.StaffId,
	}
}

func ConverterCreatePlaygroupDOToModel(playgroupDO *do.PlaygroupDO) *models.Playgroup {
	if playgroupDO == nil {
		return nil
	}
	now := time.Now()
	return &models.Playgroup{
		CompanyID:      playgroupDO.CompanyID,
		Name:           playgroupDO.Name,
		ColorCode:      playgroupDO.ColorCode,
		MaxPetCapacity: playgroupDO.MaxPetCapacity,
		Description:    playgroupDO.Description,
		CreatedBy:      playgroupDO.OperateId,
		UpdatedBy:      playgroupDO.OperateId,
		CreatedAt:      &now,
		UpdatedAt:      &now,
	}
}

func ConverterUpdatePlaygroupRequestToDO(request *offeringsvcpb.UpdatePlaygroupRequest) *do.PlaygroupDO {
	if request == nil {
		return nil
	}
	return &do.PlaygroupDO{
		ID:             request.Playgroup.Id,
		CompanyID:      request.CompanyId,
		Name:           request.Playgroup.Name,
		ColorCode:      request.Playgroup.ColorCode,
		MaxPetCapacity: request.Playgroup.MaxPetCapacity,
		Description:    request.Playgroup.Description,
		OperateId:      request.StaffId,
	}
}

func ConverterPlaygroupModelToDO(playgroup *models.Playgroup) *do.PlaygroupDO {
	if playgroup == nil {
		return nil
	}
	return &do.PlaygroupDO{
		ID:             playgroup.ID,
		CompanyID:      playgroup.CompanyID,
		Name:           playgroup.Name,
		ColorCode:      playgroup.ColorCode,
		MaxPetCapacity: playgroup.MaxPetCapacity,
		Description:    playgroup.Description,
		Sort:           playgroup.Sort,
	}
}

func ConverterPlaygroupDOToPB(playgroupDO *do.PlaygroupDO) *offeringsvcmodels.PlaygroupModel {
	if playgroupDO == nil {
		return nil
	}
	return &offeringsvcmodels.PlaygroupModel{
		Id:             playgroupDO.ID,
		Name:           playgroupDO.Name,
		ColorCode:      playgroupDO.ColorCode,
		MaxPetCapacity: playgroupDO.MaxPetCapacity,
		Description:    playgroupDO.Description,
		Sort:           playgroupDO.Sort,
	}
}

func ConverterPlaygroupModelListToDO(playgroups []*models.Playgroup) []*do.PlaygroupDO {
	if playgroups == nil {
		return nil
	}
	playgroupDOs := make([]*do.PlaygroupDO, 0)
	for _, playgroup := range playgroups {
		playgroupDOs = append(playgroupDOs, ConverterPlaygroupModelToDO(playgroup))
	}
	return playgroupDOs
}

func ConverterPlaygroupDOListToPB(playgroupDOs []*do.PlaygroupDO) []*offeringsvcmodels.PlaygroupModel {
	if playgroupDOs == nil {
		return nil
	}
	playgroupPBModels := make([]*offeringsvcmodels.PlaygroupModel, 0)
	for _, playgroupDO := range playgroupDOs {
		playgroupPBModels = append(playgroupPBModels, ConverterPlaygroupDOToPB(playgroupDO))
	}
	return playgroupPBModels
}

func ConverterUpdatePlaygroupDOToUpdateOpt(playgroupDO *do.PlaygroupDO) *models.PlaygroupUpdateOpt {
	if playgroupDO == nil {
		return nil
	}
	now := time.Now()
	return &models.PlaygroupUpdateOpt{
		Name:           &playgroupDO.Name,
		ColorCode:      &playgroupDO.ColorCode,
		MaxPetCapacity: &playgroupDO.MaxPetCapacity,
		Description:    &playgroupDO.Description,
		UpdatedBy:      &playgroupDO.OperateId,
		UpdatedAt:      &now,
	}
}
