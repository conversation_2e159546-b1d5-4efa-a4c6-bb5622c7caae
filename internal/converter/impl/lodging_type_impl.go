// Code generated by github.com/jmattheis/goverter, DO NOT EDIT.
//go:build !goverter

package impl

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

type LodgingTypeConverter struct{}

func (c *LodgingTypeConverter) ConvertUpdateByIdToUpdateOpt(source *do.LodgingTypeUpdateByIDOpt) *do.LodgingTypeUpdateOpt {
	var pDoLodgingTypeUpdateOpt *do.LodgingTypeUpdateOpt
	if source != nil {
		var doLodgingTypeUpdateOpt do.LodgingTypeUpdateOpt
		if (*source).Description != nil {
			xstring := *(*source).Description
			doLodgingTypeUpdateOpt.Description = &xstring
		}
		if (*source).Name != nil {
			xstring2 := *(*source).Name
			doLodgingTypeUpdateOpt.Name = &xstring2
		}
		if (*source).PhotoList != nil {
			doLodgingTypeUpdateOpt.PhotoList = make([]string, len((*source).PhotoList))
			for i := 0; i < len((*source).PhotoList); i++ {
				doLodgingTypeUpdateOpt.PhotoList[i] = (*source).PhotoList[i]
			}
		}
		if (*source).MaxPetNum != nil {
			xint32 := *(*source).MaxPetNum
			doLodgingTypeUpdateOpt.MaxPetNum = &xint32
		}
		if (*source).UpdatedBy != nil {
			xint64 := *(*source).UpdatedBy
			doLodgingTypeUpdateOpt.UpdatedBy = &xint64
		}
		if (*source).AllowedPetSizeList != nil {
			doLodgingTypeUpdateOpt.AllowedPetSizeList = make([]int64, len((*source).AllowedPetSizeList))
			for j := 0; j < len((*source).AllowedPetSizeList); j++ {
				doLodgingTypeUpdateOpt.AllowedPetSizeList[j] = (*source).AllowedPetSizeList[j]
			}
		}
		if (*source).PetSizeFilter != nil {
			xbool := *(*source).PetSizeFilter
			doLodgingTypeUpdateOpt.PetSizeFilter = &xbool
		}
		if (*source).Type != nil {
			offeringpbLodgingUnitType := c.offeringpbLodgingUnitTypeToOfferingpbLodgingUnitType(*(*source).Type)
			doLodgingTypeUpdateOpt.Type = &offeringpbLodgingUnitType
		}
		if (*source).Sort != nil {
			xint322 := *(*source).Sort
			doLodgingTypeUpdateOpt.Sort = &xint322
		}
		pDoLodgingTypeUpdateOpt = &doLodgingTypeUpdateOpt
	}
	return pDoLodgingTypeUpdateOpt
}
func (c *LodgingTypeConverter) offeringpbLodgingUnitTypeToOfferingpbLodgingUnitType(source v1.LodgingUnitType) v1.LodgingUnitType {
	var offeringpbLodgingUnitType v1.LodgingUnitType
	switch source {
	case v1.LodgingUnitType_AREA:
		offeringpbLodgingUnitType = v1.LodgingUnitType_AREA
	case v1.LodgingUnitType_LODGING_UNIT_TYPE_UNSPECIFIED:
		offeringpbLodgingUnitType = v1.LodgingUnitType_LODGING_UNIT_TYPE_UNSPECIFIED
	case v1.LodgingUnitType_ROOM:
		offeringpbLodgingUnitType = v1.LodgingUnitType_ROOM
	default: // ignored
	}
	return offeringpbLodgingUnitType
}
