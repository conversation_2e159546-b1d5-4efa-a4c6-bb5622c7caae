// Code generated by github.com/jmattheis/goverter, DO NOT EDIT.
//go:build !goverter

package impl

import (
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"time"
)

type LodgingUnitConverterImpl struct{}

func (c *LodgingUnitConverterImpl) ConvertUpdateByIdToUpdateOpt(source *do.LodgingUnitUpdateByIDOpt) *do.LodgingUnitUpdateOpt {
	var pDoLodgingUnitUpdateOpt *do.LodgingUnitUpdateOpt
	if source != nil {
		var doLodgingUnitUpdateOpt do.LodgingUnitUpdateOpt
		if (*source).Name != nil {
			xstring := *(*source).Name
			doLodgingUnitUpdateOpt.Name = &xstring
		}
		if (*source).UpdatedBy != nil {
			xint64 := *(*source).UpdatedBy
			doLodgingUnitUpdateOpt.UpdatedBy = &xint64
		}
		if (*source).CameraId != nil {
			xint642 := *(*source).CameraId
			doLodgingUnitUpdateOpt.CameraId = &xint642
		}
		doLodgingUnitUpdateOpt.UpdatedAt = c.pTimeTimeToPTimeTime((*source).UpdatedAt)
		if (*source).Sort != nil {
			xint32 := *(*source).Sort
			doLodgingUnitUpdateOpt.Sort = &xint32
		}
		pDoLodgingUnitUpdateOpt = &doLodgingUnitUpdateOpt
	}
	return pDoLodgingUnitUpdateOpt
}
func (c *LodgingUnitConverterImpl) ConvertUpdateOptDoToModel(source *do.LodgingUnitUpdateOpt) *models.LodgingUnitUpdateOpt {
	var pModelsLodgingUnitUpdateOpt *models.LodgingUnitUpdateOpt
	if source != nil {
		var modelsLodgingUnitUpdateOpt models.LodgingUnitUpdateOpt
		if (*source).Name != nil {
			xstring := *(*source).Name
			modelsLodgingUnitUpdateOpt.Name = &xstring
		}
		modelsLodgingUnitUpdateOpt.UpdatedAt = c.pTimeTimeToPTimeTime((*source).UpdatedAt)
		if (*source).UpdatedBy != nil {
			xint64 := *(*source).UpdatedBy
			modelsLodgingUnitUpdateOpt.UpdatedBy = &xint64
		}
		if (*source).CameraId != nil {
			xint642 := *(*source).CameraId
			modelsLodgingUnitUpdateOpt.CameraId = &xint642
		}
		if (*source).Sort != nil {
			xint32 := *(*source).Sort
			modelsLodgingUnitUpdateOpt.Sort = &xint32
		}
		pModelsLodgingUnitUpdateOpt = &modelsLodgingUnitUpdateOpt
	}
	return pModelsLodgingUnitUpdateOpt
}
func (c *LodgingUnitConverterImpl) ConverterCreateRequestToDO(source *v1.CreateLodgingUnitRequest) *do.LodgingUnitDO {
	var pDoLodgingUnitDO *do.LodgingUnitDO
	if source != nil {
		var doLodgingUnitDO do.LodgingUnitDO
		doLodgingUnitDO.CompanyID = (*source).CompanyId
		doLodgingUnitDO.BusinessID = (*source).BusinessId
		doLodgingUnitDO.LodgingTypeID = (*source).LodgingTypeId
		doLodgingUnitDO.Name = (*source).Name
		doLodgingUnitDO.CreatedBy = (*source).TokenStaffId
		if (*source).CameraId != nil {
			doLodgingUnitDO.CameraId = *(*source).CameraId
		}
		pDoLodgingUnitDO = &doLodgingUnitDO
	}
	return pDoLodgingUnitDO
}
func (c *LodgingUnitConverterImpl) ConverterDOToPO(source *do.LodgingUnitDO) *models.LodgingUnit {
	var pModelsLodgingUnit *models.LodgingUnit
	if source != nil {
		var modelsLodgingUnit models.LodgingUnit
		modelsLodgingUnit.ID = (*source).ID
		modelsLodgingUnit.CompanyID = (*source).CompanyID
		modelsLodgingUnit.BusinessID = (*source).BusinessID
		modelsLodgingUnit.LodgingTypeID = (*source).LodgingTypeID
		modelsLodgingUnit.Name = (*source).Name
		modelsLodgingUnit.CreatedBy = (*source).CreatedBy
		modelsLodgingUnit.CameraID = (*source).CameraId
		modelsLodgingUnit.Sort = (*source).Sort
		pModelsLodgingUnit = &modelsLodgingUnit
	}
	return pModelsLodgingUnit
}
func (c *LodgingUnitConverterImpl) ConverterDoToPB(source *do.LodgingUnitDO) *v11.LodgingUnitModel {
	var pOfferingpbLodgingUnitModel *v11.LodgingUnitModel
	if source != nil {
		var offeringpbLodgingUnitModel v11.LodgingUnitModel
		offeringpbLodgingUnitModel.CompanyId = (*source).CompanyID
		offeringpbLodgingUnitModel.BusinessId = (*source).BusinessID
		offeringpbLodgingUnitModel.Id = (*source).ID
		offeringpbLodgingUnitModel.Name = (*source).Name
		offeringpbLodgingUnitModel.LodgingTypeId = (*source).LodgingTypeID
		offeringpbLodgingUnitModel.CameraId = (*source).CameraId
		offeringpbLodgingUnitModel.Sort = (*source).Sort
		pOfferingpbLodgingUnitModel = &offeringpbLodgingUnitModel
	}
	return pOfferingpbLodgingUnitModel
}
func (c *LodgingUnitConverterImpl) ConverterPOToDO(source *models.LodgingUnit) *do.LodgingUnitDO {
	var pDoLodgingUnitDO *do.LodgingUnitDO
	if source != nil {
		var doLodgingUnitDO do.LodgingUnitDO
		doLodgingUnitDO.ID = (*source).ID
		doLodgingUnitDO.CompanyID = (*source).CompanyID
		doLodgingUnitDO.BusinessID = (*source).BusinessID
		doLodgingUnitDO.LodgingTypeID = (*source).LodgingTypeID
		doLodgingUnitDO.Name = (*source).Name
		doLodgingUnitDO.CreatedBy = (*source).CreatedBy
		doLodgingUnitDO.CameraId = (*source).CameraID
		doLodgingUnitDO.Sort = (*source).Sort
		pDoLodgingUnitDO = &doLodgingUnitDO
	}
	return pDoLodgingUnitDO
}
func (c *LodgingUnitConverterImpl) ConverterUpdateRequestToUpdateOpt(source *v1.UpdateLodgingUnitRequest) *do.LodgingUnitUpdateOpt {
	var pDoLodgingUnitUpdateOpt *do.LodgingUnitUpdateOpt
	if source != nil {
		var doLodgingUnitUpdateOpt do.LodgingUnitUpdateOpt
		if (*source).Name != nil {
			xstring := *(*source).Name
			doLodgingUnitUpdateOpt.Name = &xstring
		}
		pInt64 := (*source).TokenStaffId
		doLodgingUnitUpdateOpt.UpdatedBy = &pInt64
		if (*source).CameraId != nil {
			xint64 := *(*source).CameraId
			doLodgingUnitUpdateOpt.CameraId = &xint64
		}
		pDoLodgingUnitUpdateOpt = &doLodgingUnitUpdateOpt
	}
	return pDoLodgingUnitUpdateOpt
}
func (c *LodgingUnitConverterImpl) pTimeTimeToPTimeTime(source *time.Time) *time.Time {
	var pTimeTime *time.Time
	if source != nil {
		var timeTime time.Time
		_ = (*source)
		pTimeTime = &timeTime
	}
	return pTimeTime
}
