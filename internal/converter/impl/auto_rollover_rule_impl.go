// Code generated by github.com/jmattheis/goverter, DO NOT EDIT.
//go:build !goverter

package impl

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

type AutoRolloverRuleConverter struct{}

func (c *AutoRolloverRuleConverter) ConvertDBModelToDO(source *models.MoeAutoRollover) *do.AutoRolloverRule {
	var pDoAutoRolloverRule *do.AutoRolloverRule
	if source != nil {
		var doAutoRolloverRule do.AutoRolloverRule
		doAutoRolloverRule.ServiceId = (*source).ServiceID
		if (*source).Enabled != nil {
			doAutoRolloverRule.Enabled = *(*source).Enabled
		}
		doAutoRolloverRule.AfterMinute = (*source).AfterMinute
		doAutoRolloverRule.TargetServiceId = (*source).TargetServiceID
		pDoAutoRolloverRule = &doAutoRolloverRule
	}
	return pDoAutoRolloverRule
}
func (c *AutoRolloverRuleConverter) ConvertDOToPB(source *do.AutoRolloverRule) *v1.AutoRolloverRuleModel {
	var pOfferingpbAutoRolloverRuleModel *v1.AutoRolloverRuleModel
	if source != nil {
		var offeringpbAutoRolloverRuleModel v1.AutoRolloverRuleModel
		offeringpbAutoRolloverRuleModel.Enabled = (*source).Enabled
		offeringpbAutoRolloverRuleModel.AfterMinute = (*source).AfterMinute
		offeringpbAutoRolloverRuleModel.TargetServiceId = (*source).TargetServiceId
		offeringpbAutoRolloverRuleModel.ServiceId = (*source).ServiceId
		pOfferingpbAutoRolloverRuleModel = &offeringpbAutoRolloverRuleModel
	}
	return pOfferingpbAutoRolloverRuleModel
}
