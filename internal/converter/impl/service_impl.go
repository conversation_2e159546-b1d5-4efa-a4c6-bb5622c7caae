// Code generated by github.com/jmattheis/goverter, DO NOT EDIT.
//go:build !goverter

package impl

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	interface1 "github.com/MoeGolibrary/moego-svc-offering/internal/converter/interface"
	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

type ServiceConverter struct{}

func (c *ServiceConverter) ConvertAddonAvailabilityToDO(source *v1.ServiceModel) *do.AddonAvailability {
	var pDoAddonAvailability *do.AddonAvailability
	if source != nil {
		var doAddonAvailability do.AddonAvailability
		pBool := (*source).ServiceFilter
		doAddonAvailability.ServiceFilter = &pBool
		if (*source).ServiceFilterList != nil {
			doAddonAvailability.ServiceFilterList = make([]do.AddonAvailabilityRule, len((*source).ServiceFilterList))
			for i := 0; i < len((*source).ServiceFilterList); i++ {
				doAddonAvailability.ServiceFilterList[i] = c.ConvertServiceFilterToDO((*source).ServiceFilterList[i])
			}
		}
		pDoAddonAvailability = &doAddonAvailability
	}
	return pDoAddonAvailability
}
func (c *ServiceConverter) ConvertAutoRolloverDef(source *v1.AutoRolloverRuleDef) *do.AutoRolloverRule {
	var pDoAutoRolloverRule *do.AutoRolloverRule
	if source != nil {
		var doAutoRolloverRule do.AutoRolloverRule
		doAutoRolloverRule.Enabled = (*source).Enabled
		if (*source).AfterMinute != nil {
			doAutoRolloverRule.AfterMinute = *(*source).AfterMinute
		}
		if (*source).TargetServiceId != nil {
			doAutoRolloverRule.TargetServiceId = *(*source).TargetServiceId
		}
		pDoAutoRolloverRule = &doAutoRolloverRule
	}
	return pDoAutoRolloverRule
}
func (c *ServiceConverter) ConvertAvailabilityToDO(source v1.CreateServiceDef) do.Availability {
	var doAvailability do.Availability
	pBool := source.IsAllLocation
	doAvailability.IsAllLocation = &pBool
	if source.AvailableBusinessIdList != nil {
		doAvailability.AvailableBusinessIdList = make([]int64, len(source.AvailableBusinessIdList))
		for i := 0; i < len(source.AvailableBusinessIdList); i++ {
			doAvailability.AvailableBusinessIdList[i] = source.AvailableBusinessIdList[i]
		}
	}
	if source.LocationOverrideList != nil {
		doAvailability.LocationOverrideList = make([]do.BusinessOverrideRule, len(source.LocationOverrideList))
		for j := 0; j < len(source.LocationOverrideList); j++ {
			doAvailability.LocationOverrideList[j] = c.pOfferingpbLocationOverrideRuleToDoBusinessOverrideRule(source.LocationOverrideList[j])
		}
	}
	pBool2 := source.RequireDedicatedLodging
	doAvailability.RequireDedicatedLodging = &pBool2
	pBool3 := source.LodgingFilter
	doAvailability.LodgingFilter = &pBool3
	if source.CustomizedLodgings != nil {
		doAvailability.CustomizedLodgings = make([]int64, len(source.CustomizedLodgings))
		for k := 0; k < len(source.CustomizedLodgings); k++ {
			doAvailability.CustomizedLodgings[k] = source.CustomizedLodgings[k]
		}
	}
	pBool4 := source.RequireDedicatedStaff
	doAvailability.RequireDedicatedStaff = &pBool4
	pBool5 := source.AvailableForAllStaff
	doAvailability.AvailableForAllStaff = &pBool5
	if source.AvailableStaffIdList != nil {
		doAvailability.AvailableStaffIdList = make([]int64, len(source.AvailableStaffIdList))
		for l := 0; l < len(source.AvailableStaffIdList); l++ {
			doAvailability.AvailableStaffIdList[l] = source.AvailableStaffIdList[l]
		}
	}
	pBool6 := source.BreedFilter
	doAvailability.BreedFilter = &pBool6
	if source.CustomizedBreed != nil {
		doAvailability.CustomizedBreed = make([]do.PetTypeAndBreedAvailabilityRule, len(source.CustomizedBreed))
		for m := 0; m < len(source.CustomizedBreed); m++ {
			doAvailability.CustomizedBreed[m] = c.pOfferingpbCustomizedBreedToDoPetTypeAndBreedAvailabilityRule(source.CustomizedBreed[m])
		}
	}
	pBool7 := source.WeightFilter
	doAvailability.WeightFilter = &pBool7
	pBool8 := source.PetSizeFilter
	doAvailability.PetSizeFilter = &pBool8
	if source.CustomizedPetSizes != nil {
		doAvailability.CustomizedPetSizes = make([]int64, len(source.CustomizedPetSizes))
		for n := 0; n < len(source.CustomizedPetSizes); n++ {
			doAvailability.CustomizedPetSizes[n] = source.CustomizedPetSizes[n]
		}
	}
	doAvailability.AvailableMinWeight = interface1.GetMinWeight(source.WeightRange)
	doAvailability.AvailableMaxWeight = interface1.GetMaxWeight(source.WeightRange)
	pBool9 := source.CoatFilter
	doAvailability.CoatFilter = &pBool9
	if source.CustomizedCoat != nil {
		doAvailability.CustomizedCoat = make([]int64, len(source.CustomizedCoat))
		for o := 0; o < len(source.CustomizedCoat); o++ {
			doAvailability.CustomizedCoat[o] = source.CustomizedCoat[o]
		}
	}
	doAvailability.PetCodeFilter = c.pOfferingpbCreateServiceDef_PetCodeFilterToPDoPetCodeAvailability(source.PetCodeFilter)
	if source.BundleServiceIds != nil {
		doAvailability.BundleServiceIds = make([]int64, len(source.BundleServiceIds))
		for p := 0; p < len(source.BundleServiceIds); p++ {
			doAvailability.BundleServiceIds[p] = source.BundleServiceIds[p]
		}
	}
	return doAvailability
}
func (c *ServiceConverter) ConvertAvailabilityToUpdateOpt(source v1.UpdateServiceDef) *do.Availability {
	var doAvailability do.Availability
	pBool := source.IsAllLocation
	doAvailability.IsAllLocation = &pBool
	if source.AvailableBusinessIdList != nil {
		doAvailability.AvailableBusinessIdList = make([]int64, len(source.AvailableBusinessIdList))
		for i := 0; i < len(source.AvailableBusinessIdList); i++ {
			doAvailability.AvailableBusinessIdList[i] = source.AvailableBusinessIdList[i]
		}
	}
	if source.LocationOverrideList != nil {
		doAvailability.LocationOverrideList = make([]do.BusinessOverrideRule, len(source.LocationOverrideList))
		for j := 0; j < len(source.LocationOverrideList); j++ {
			doAvailability.LocationOverrideList[j] = c.pOfferingpbLocationOverrideRuleToDoBusinessOverrideRule(source.LocationOverrideList[j])
		}
	}
	pBool2 := source.RequireDedicatedLodging
	doAvailability.RequireDedicatedLodging = &pBool2
	pBool3 := source.LodgingFilter
	doAvailability.LodgingFilter = &pBool3
	if source.CustomizedLodgings != nil {
		doAvailability.CustomizedLodgings = make([]int64, len(source.CustomizedLodgings))
		for k := 0; k < len(source.CustomizedLodgings); k++ {
			doAvailability.CustomizedLodgings[k] = source.CustomizedLodgings[k]
		}
	}
	pBool4 := source.RequireDedicatedStaff
	doAvailability.RequireDedicatedStaff = &pBool4
	pBool5 := source.AvailableForAllStaff
	doAvailability.AvailableForAllStaff = &pBool5
	if source.AvailableStaffIdList != nil {
		doAvailability.AvailableStaffIdList = make([]int64, len(source.AvailableStaffIdList))
		for l := 0; l < len(source.AvailableStaffIdList); l++ {
			doAvailability.AvailableStaffIdList[l] = source.AvailableStaffIdList[l]
		}
	}
	pBool6 := source.BreedFilter
	doAvailability.BreedFilter = &pBool6
	if source.CustomizedBreed != nil {
		doAvailability.CustomizedBreed = make([]do.PetTypeAndBreedAvailabilityRule, len(source.CustomizedBreed))
		for m := 0; m < len(source.CustomizedBreed); m++ {
			doAvailability.CustomizedBreed[m] = c.pOfferingpbCustomizedBreedToDoPetTypeAndBreedAvailabilityRule(source.CustomizedBreed[m])
		}
	}
	pBool7 := source.WeightFilter
	doAvailability.WeightFilter = &pBool7
	pBool8 := source.PetSizeFilter
	doAvailability.PetSizeFilter = &pBool8
	if source.CustomizedPetSizes != nil {
		doAvailability.CustomizedPetSizes = make([]int64, len(source.CustomizedPetSizes))
		for n := 0; n < len(source.CustomizedPetSizes); n++ {
			doAvailability.CustomizedPetSizes[n] = source.CustomizedPetSizes[n]
		}
	}
	doAvailability.AvailableMinWeight = interface1.GetMinWeight(source.WeightRange)
	doAvailability.AvailableMaxWeight = interface1.GetMaxWeight(source.WeightRange)
	pBool9 := source.CoatFilter
	doAvailability.CoatFilter = &pBool9
	if source.CustomizedCoat != nil {
		doAvailability.CustomizedCoat = make([]int64, len(source.CustomizedCoat))
		for o := 0; o < len(source.CustomizedCoat); o++ {
			doAvailability.CustomizedCoat[o] = source.CustomizedCoat[o]
		}
	}
	doAvailability.PetCodeFilter = c.pOfferingpbUpdateServiceDef_PetCodeFilterToPDoPetCodeAvailability(source.PetCodeFilter)
	if source.BundleServiceIds != nil {
		doAvailability.BundleServiceIds = make([]int64, len(source.BundleServiceIds))
		for p := 0; p < len(source.BundleServiceIds); p++ {
			doAvailability.BundleServiceIds[p] = source.BundleServiceIds[p]
		}
	}
	return &doAvailability
}
func (c *ServiceConverter) ConvertCreateDefToDO(source *v1.CreateServiceDef) *do.ServiceDO {
	var pDoServiceDO *do.ServiceDO
	if source != nil {
		var doServiceDO do.ServiceDO
		doServiceDO.Name = (*source).Name
		if (*source).Description != nil {
			doServiceDO.Description = *(*source).Description
		}
		if (*source).CategoryId != nil {
			doServiceDO.CategoryId = *(*source).CategoryId
		}
		if (*source).ColorCode != nil {
			xstring := *(*source).ColorCode
			doServiceDO.ColorCode = &xstring
		}
		doServiceDO.Inactive = (*source).Inactive
		if (*source).Images != nil {
			doServiceDO.Images = make([]string, len((*source).Images))
			for i := 0; i < len((*source).Images); i++ {
				doServiceDO.Images[i] = (*source).Images[i]
			}
		}
		doServiceDO.Availability = c.ConvertAvailabilityToDO((*source))
		doServiceDO.Price = (*source).Price
		doServiceDO.PriceUnit = c.offeringpbServicePriceUnitToOfferingpbServicePriceUnit((*source).PriceUnit)
		if (*source).Type != nil {
			doServiceDO.ServiceType = c.offeringpbServiceTypeToOfferingpbServiceType(*(*source).Type)
		}
		doServiceDO.TaxId = (*source).TaxId
		pInt32 := (*source).Duration
		doServiceDO.Duration = &pInt32
		if (*source).MaxDuration != nil {
			xint32 := *(*source).MaxDuration
			doServiceDO.MaxDuration = &xint32
		}
		doServiceDO.Commission = c.offeringpbCreateServiceDefToDoCommission((*source))
		if (*source).ServiceItemType != nil {
			doServiceDO.ServiceItemType = c.offeringpbServiceItemTypeToOfferingpbServiceItemType(*(*source).ServiceItemType)
		}
		doServiceDO.AddonAvailability = c.offeringpbCreateServiceDefToPDoAddonAvailability((*source))
		doServiceDO.AutoRolloverRule = c.ConvertAutoRolloverDef((*source).AutoRolloverRule)
		if (*source).BundleServiceIds != nil {
			doServiceDO.BundleServiceIds = make([]int64, len((*source).BundleServiceIds))
			for j := 0; j < len((*source).BundleServiceIds); j++ {
				doServiceDO.BundleServiceIds[j] = (*source).BundleServiceIds[j]
			}
		}
		if (*source).Source != nil {
			doServiceDO.Source = c.offeringpbServiceModel_SourceToOfferingpbServiceModel_Source(*(*source).Source)
		}
		if (*source).NumSessions != nil {
			doServiceDO.NumSessions = *(*source).NumSessions
		}
		if (*source).DurationSessionMin != nil {
			doServiceDO.DurationSessionMin = *(*source).DurationSessionMin
		}
		if (*source).Capacity != nil {
			doServiceDO.Capacity = *(*source).Capacity
		}
		doServiceDO.IsRequirePrerequisiteClass = (*source).IsRequirePrerequisiteClass
		if (*source).PrerequisiteClassIds != nil {
			doServiceDO.PrerequisiteClassIds = make([]int64, len((*source).PrerequisiteClassIds))
			for k := 0; k < len((*source).PrerequisiteClassIds); k++ {
				doServiceDO.PrerequisiteClassIds[k] = (*source).PrerequisiteClassIds[k]
			}
		}
		if (*source).IsEvaluationRequired != nil {
			doServiceDO.IsEvaluationRequired = *(*source).IsEvaluationRequired
		}
		if (*source).IsEvaluationRequiredForOb != nil {
			doServiceDO.IsEvaluationRequiredForOb = *(*source).IsEvaluationRequiredForOb
		}
		if (*source).EvaluationId != nil {
			doServiceDO.EvaluationId = *(*source).EvaluationId
		}
		doServiceDO.AdditionalServiceRule = c.pOfferingpbAdditionalServiceRuleToPOfferingpbAdditionalServiceRule((*source).AdditionalServiceRule)
		pDoServiceDO = &doServiceDO
	}
	return pDoServiceDO
}
func (c *ServiceConverter) ConvertLocationOverrideRuleModelListToDO(source []models.MoeGroomingServiceLocation) []do.BusinessOverrideRule {
	var doBusinessOverrideRuleList []do.BusinessOverrideRule
	if source != nil {
		doBusinessOverrideRuleList = make([]do.BusinessOverrideRule, len(source))
		for i := 0; i < len(source); i++ {
			doBusinessOverrideRuleList[i] = c.modelsMoeGroomingServiceLocationToDoBusinessOverrideRule(source[i])
		}
	}
	return doBusinessOverrideRuleList
}
func (c *ServiceConverter) ConvertLocationOverrideRuleToUpdateOpt(source *v1.LocationOverrideRule) do.LocationOverrideRuleUpdateOpt {
	var doLocationOverrideRuleUpdateOpt do.LocationOverrideRuleUpdateOpt
	if source != nil {
		var doLocationOverrideRuleUpdateOpt2 do.LocationOverrideRuleUpdateOpt
		doLocationOverrideRuleUpdateOpt2.BusinessId = (*source).BusinessId
		if (*source).Price != nil {
			xfloat64 := *(*source).Price
			doLocationOverrideRuleUpdateOpt2.Price = &xfloat64
		}
		if (*source).TaxId != nil {
			xint64 := *(*source).TaxId
			doLocationOverrideRuleUpdateOpt2.TaxId = &xint64
		}
		if (*source).Duration != nil {
			xint32 := *(*source).Duration
			doLocationOverrideRuleUpdateOpt2.Duration = &xint32
		}
		doLocationOverrideRuleUpdateOpt = doLocationOverrideRuleUpdateOpt2
	}
	return doLocationOverrideRuleUpdateOpt
}
func (c *ServiceConverter) ConvertServiceDOToPBModel(source *do.ServiceDO) *v1.ServiceModel {
	var pOfferingpbServiceModel *v1.ServiceModel
	if source != nil {
		var offeringpbServiceModel v1.ServiceModel
		offeringpbServiceModel.ServiceId = (*source).ServiceID
		offeringpbServiceModel.Name = (*source).Name
		offeringpbServiceModel.Description = (*source).Description
		offeringpbServiceModel.Inactive = (*source).Inactive
		if (*source).Images != nil {
			offeringpbServiceModel.Images = make([]string, len((*source).Images))
			for i := 0; i < len((*source).Images); i++ {
				offeringpbServiceModel.Images[i] = (*source).Images[i]
			}
		}
		offeringpbServiceModel.CategoryId = (*source).CategoryId
		if (*source).ColorCode != nil {
			offeringpbServiceModel.ColorCode = *(*source).ColorCode
		}
		if (*source).Availability.LocationOverrideList != nil {
			offeringpbServiceModel.LocationOverrideList = make([]*v1.LocationOverrideRule, len((*source).Availability.LocationOverrideList))
			for j := 0; j < len((*source).Availability.LocationOverrideList); j++ {
				offeringpbServiceModel.LocationOverrideList[j] = c.doBusinessOverrideRuleToPOfferingpbLocationOverrideRule((*source).Availability.LocationOverrideList[j])
			}
		}
		if (*source).Availability.RequireDedicatedStaff != nil {
			offeringpbServiceModel.RequireDedicatedStaff = *(*source).Availability.RequireDedicatedStaff
		}
		offeringpbServiceModel.ServiceItemType = c.offeringpbServiceItemTypeToOfferingpbServiceItemType((*source).ServiceItemType)
		offeringpbServiceModel.Price = (*source).Price
		offeringpbServiceModel.PriceUnit = c.offeringpbServicePriceUnitToOfferingpbServicePriceUnit((*source).PriceUnit)
		offeringpbServiceModel.TaxId = (*source).TaxId
		if (*source).Duration != nil {
			offeringpbServiceModel.Duration = *(*source).Duration
		}
		if (*source).Commission.AddToCommissionBase != nil {
			offeringpbServiceModel.AddToCommissionBase = *(*source).Commission.AddToCommissionBase
		}
		if (*source).Commission.CanTip != nil {
			offeringpbServiceModel.CanTip = *(*source).Commission.CanTip
		}
		if (*source).Availability.IsAllLocation != nil {
			offeringpbServiceModel.IsAllLocation = *(*source).Availability.IsAllLocation
		}
		offeringpbServiceModel.AvailableBusinessIdList = interface1.GetAvailableBusinessIdList((*source).Availability)
		if (*source).Availability.BreedFilter != nil {
			offeringpbServiceModel.BreedFilter = *(*source).Availability.BreedFilter
		}
		if (*source).Availability.CustomizedBreed != nil {
			offeringpbServiceModel.CustomizedBreed = make([]*v1.CustomizedBreed, len((*source).Availability.CustomizedBreed))
			for k := 0; k < len((*source).Availability.CustomizedBreed); k++ {
				offeringpbServiceModel.CustomizedBreed[k] = c.doPetTypeAndBreedAvailabilityRuleToPOfferingpbCustomizedBreed((*source).Availability.CustomizedBreed[k])
			}
		}
		if (*source).Availability.PetSizeFilter != nil {
			offeringpbServiceModel.PetSizeFilter = *(*source).Availability.PetSizeFilter
		}
		if (*source).Availability.CustomizedPetSizes != nil {
			offeringpbServiceModel.CustomizedPetSizes = make([]int64, len((*source).Availability.CustomizedPetSizes))
			for l := 0; l < len((*source).Availability.CustomizedPetSizes); l++ {
				offeringpbServiceModel.CustomizedPetSizes[l] = (*source).Availability.CustomizedPetSizes[l]
			}
		}
		if (*source).Availability.WeightFilter != nil {
			offeringpbServiceModel.WeightFilter = *(*source).Availability.WeightFilter
		}
		offeringpbServiceModel.WeightRange = interface1.GetWeightRange((*source).Availability)
		if (*source).Availability.CoatFilter != nil {
			offeringpbServiceModel.CoatFilter = *(*source).Availability.CoatFilter
		}
		if (*source).Availability.CustomizedCoat != nil {
			offeringpbServiceModel.CustomizedCoat = make([]int64, len((*source).Availability.CustomizedCoat))
			for m := 0; m < len((*source).Availability.CustomizedCoat); m++ {
				offeringpbServiceModel.CustomizedCoat[m] = (*source).Availability.CustomizedCoat[m]
			}
		}
		if (*source).Availability.RequireDedicatedLodging != nil {
			offeringpbServiceModel.RequireDedicatedLodging = *(*source).Availability.RequireDedicatedLodging
		}
		if (*source).Availability.LodgingFilter != nil {
			offeringpbServiceModel.LodgingFilter = *(*source).Availability.LodgingFilter
		}
		if (*source).Availability.CustomizedLodgings != nil {
			offeringpbServiceModel.CustomizedLodgings = make([]int64, len((*source).Availability.CustomizedLodgings))
			for n := 0; n < len((*source).Availability.CustomizedLodgings); n++ {
				offeringpbServiceModel.CustomizedLodgings[n] = (*source).Availability.CustomizedLodgings[n]
			}
		}
		var pBool *bool
		if (*source).AddonAvailability != nil {
			pBool = (*source).AddonAvailability.ServiceFilter
		}
		if pBool != nil {
			offeringpbServiceModel.ServiceFilter = *pBool
		}
		var pDoAddonAvailabilityRuleList *[]do.AddonAvailabilityRule
		if (*source).AddonAvailability != nil {
			pDoAddonAvailabilityRuleList = &(*source).AddonAvailability.ServiceFilterList
		}
		if pDoAddonAvailabilityRuleList != nil {
			var pOfferingpbServiceFilterList []*v1.ServiceFilter
			if (*pDoAddonAvailabilityRuleList) != nil {
				pOfferingpbServiceFilterList = make([]*v1.ServiceFilter, len((*pDoAddonAvailabilityRuleList)))
				for o := 0; o < len((*pDoAddonAvailabilityRuleList)); o++ {
					pOfferingpbServiceFilterList[o] = c.doAddonAvailabilityRuleToPOfferingpbServiceFilter((*pDoAddonAvailabilityRuleList)[o])
				}
			}
			offeringpbServiceModel.ServiceFilterList = pOfferingpbServiceFilterList
		}
		offeringpbServiceModel.Type = c.offeringpbServiceTypeToOfferingpbServiceType((*source).ServiceType)
		if (*source).MaxDuration != nil {
			offeringpbServiceModel.MaxDuration = *(*source).MaxDuration
		}
		offeringpbServiceModel.AutoRolloverRule = c.pDoAutoRolloverRuleToPOfferingpbAutoRolloverRule((*source).AutoRolloverRule)
		timestamppbTimestamp := interface1.TimeStampToPBTime((*source).CreateTime)
		offeringpbServiceModel.CreateTime = &timestamppbTimestamp
		timestamppbTimestamp2 := interface1.TimeStampToPBTime((*source).UpdateTime)
		offeringpbServiceModel.UpdateTime = &timestamppbTimestamp2
		offeringpbServiceModel.IsDeleted = (*source).IsDeleted
		offeringpbServiceModel.CompanyId = (*source).CompanyId
		if (*source).Availability.AvailableStaffIdList != nil {
			offeringpbServiceModel.AvailableStaffIdList = make([]int64, len((*source).Availability.AvailableStaffIdList))
			for p := 0; p < len((*source).Availability.AvailableStaffIdList); p++ {
				offeringpbServiceModel.AvailableStaffIdList[p] = (*source).Availability.AvailableStaffIdList[p]
			}
		}
		if (*source).Availability.AvailableForAllStaff != nil {
			offeringpbServiceModel.AvailableForAllStaff = *(*source).Availability.AvailableForAllStaff
		}
		offeringpbServiceModel.PetCodeFilter = c.pDoPetCodeAvailabilityToPOfferingpbServiceModel_PetCodeFilter((*source).Availability.PetCodeFilter)
		if (*source).BundleServiceIds != nil {
			offeringpbServiceModel.BundleServiceIds = make([]int64, len((*source).BundleServiceIds))
			for q := 0; q < len((*source).BundleServiceIds); q++ {
				offeringpbServiceModel.BundleServiceIds[q] = (*source).BundleServiceIds[q]
			}
		}
		offeringpbServiceModel.Source = c.offeringpbServiceModel_SourceToOfferingpbServiceModel_Source((*source).Source)
		offeringpbServiceModel.NumSessions = (*source).NumSessions
		offeringpbServiceModel.DurationSessionMin = (*source).DurationSessionMin
		offeringpbServiceModel.Capacity = (*source).Capacity
		offeringpbServiceModel.IsRequirePrerequisiteClass = (*source).IsRequirePrerequisiteClass
		if (*source).PrerequisiteClassIds != nil {
			offeringpbServiceModel.PrerequisiteClassIds = make([]int64, len((*source).PrerequisiteClassIds))
			for r := 0; r < len((*source).PrerequisiteClassIds); r++ {
				offeringpbServiceModel.PrerequisiteClassIds[r] = (*source).PrerequisiteClassIds[r]
			}
		}
		offeringpbServiceModel.IsEvaluationRequired = (*source).IsEvaluationRequired
		offeringpbServiceModel.IsEvaluationRequiredForOb = (*source).IsEvaluationRequiredForOb
		offeringpbServiceModel.EvaluationId = (*source).EvaluationId
		offeringpbServiceModel.AdditionalServiceRule = c.pOfferingpbAdditionalServiceRuleToPOfferingpbAdditionalServiceRule((*source).AdditionalServiceRule)
		pOfferingpbServiceModel = &offeringpbServiceModel
	}
	return pOfferingpbServiceModel
}
func (c *ServiceConverter) ConvertServiceFilterToDO(source *v1.ServiceFilter) do.AddonAvailabilityRule {
	var doAddonAvailabilityRule do.AddonAvailabilityRule
	if source != nil {
		var doAddonAvailabilityRule2 do.AddonAvailabilityRule
		pOfferingpbServiceItemType := c.offeringpbServiceItemTypeToOfferingpbServiceItemType((*source).ServiceItemType)
		doAddonAvailabilityRule2.ServiceItemType = &pOfferingpbServiceItemType
		if (*source).AvailableForAllServices != nil {
			xbool := *(*source).AvailableForAllServices
			doAddonAvailabilityRule2.AvailableForAllServices = &xbool
		}
		if (*source).AvailableServiceIdList != nil {
			doAddonAvailabilityRule2.AvailableServiceIdList = make([]int64, len((*source).AvailableServiceIdList))
			for i := 0; i < len((*source).AvailableServiceIdList); i++ {
				doAddonAvailabilityRule2.AvailableServiceIdList[i] = (*source).AvailableServiceIdList[i]
			}
		}
		doAddonAvailabilityRule = doAddonAvailabilityRule2
	}
	return doAddonAvailabilityRule
}
func (c *ServiceConverter) ConvertUpdateDefToUpdateOpt(source *v1.UpdateServiceDef) *do.ServiceUpdateOpt {
	var pDoServiceUpdateOpt *do.ServiceUpdateOpt
	if source != nil {
		var doServiceUpdateOpt do.ServiceUpdateOpt
		doServiceUpdateOpt.ServiceID = (*source).ServiceId
		pString := (*source).Name
		doServiceUpdateOpt.Name = &pString
		pString2 := (*source).Description
		doServiceUpdateOpt.Description = &pString2
		pInt64 := (*source).CategoryId
		doServiceUpdateOpt.CategoryId = &pInt64
		pString3 := (*source).ColorCode
		doServiceUpdateOpt.ColorCode = &pString3
		pBool := (*source).Inactive
		doServiceUpdateOpt.Inactive = &pBool
		if (*source).Images != nil {
			doServiceUpdateOpt.Images = make([]string, len((*source).Images))
			for i := 0; i < len((*source).Images); i++ {
				doServiceUpdateOpt.Images[i] = (*source).Images[i]
			}
		}
		doServiceUpdateOpt.Availability = c.ConvertAvailabilityToUpdateOpt((*source))
		pFloat64 := (*source).Price
		doServiceUpdateOpt.Price = &pFloat64
		pOfferingpbServicePriceUnit := c.offeringpbServicePriceUnitToOfferingpbServicePriceUnit((*source).PriceUnit)
		doServiceUpdateOpt.PriceUnit = &pOfferingpbServicePriceUnit
		pInt642 := (*source).TaxId
		doServiceUpdateOpt.TaxId = &pInt642
		pInt32 := (*source).Duration
		doServiceUpdateOpt.Duration = &pInt32
		doServiceUpdateOpt.Commission = c.offeringpbUpdateServiceDefToPDoCommission((*source))
		doServiceUpdateOpt.AddonAvailability = c.offeringpbUpdateServiceDefToPDoAddonAvailability((*source))
		if (*source).MaxDuration != nil {
			xint32 := *(*source).MaxDuration
			doServiceUpdateOpt.MaxDuration = &xint32
		}
		doServiceUpdateOpt.AutoRolloverRule = c.ConvertAutoRolloverDef((*source).AutoRolloverRule)
		if (*source).BundleServiceIds != nil {
			doServiceUpdateOpt.BundleServiceIds = make([]int64, len((*source).BundleServiceIds))
			for j := 0; j < len((*source).BundleServiceIds); j++ {
				doServiceUpdateOpt.BundleServiceIds[j] = (*source).BundleServiceIds[j]
			}
		}
		if (*source).Source != nil {
			offeringpbServiceModel_Source := c.offeringpbServiceModel_SourceToOfferingpbServiceModel_Source(*(*source).Source)
			doServiceUpdateOpt.Source = &offeringpbServiceModel_Source
		}
		if (*source).NumSessions != nil {
			xint322 := *(*source).NumSessions
			doServiceUpdateOpt.NumSessions = &xint322
		}
		if (*source).DurationSessionMin != nil {
			xint323 := *(*source).DurationSessionMin
			doServiceUpdateOpt.DurationSessionMin = &xint323
		}
		if (*source).Capacity != nil {
			xint324 := *(*source).Capacity
			doServiceUpdateOpt.Capacity = &xint324
		}
		pBool2 := (*source).IsRequirePrerequisiteClass
		doServiceUpdateOpt.IsRequirePrerequisiteClass = &pBool2
		if (*source).PrerequisiteClassIds != nil {
			doServiceUpdateOpt.PrerequisiteClassIds = make([]int64, len((*source).PrerequisiteClassIds))
			for k := 0; k < len((*source).PrerequisiteClassIds); k++ {
				doServiceUpdateOpt.PrerequisiteClassIds[k] = (*source).PrerequisiteClassIds[k]
			}
		}
		if (*source).IsEvaluationRequired != nil {
			xbool := *(*source).IsEvaluationRequired
			doServiceUpdateOpt.IsEvaluationRequired = &xbool
		}
		if (*source).IsEvaluationRequiredForOb != nil {
			xbool2 := *(*source).IsEvaluationRequiredForOb
			doServiceUpdateOpt.IsEvaluationRequiredForOb = &xbool2
		}
		if (*source).EvaluationId != nil {
			xint64 := *(*source).EvaluationId
			doServiceUpdateOpt.EvaluationID = &xint64
		}
		doServiceUpdateOpt.AdditionalServiceRule = c.pOfferingpbAdditionalServiceRuleToPOfferingpbAdditionalServiceRule((*source).AdditionalServiceRule)
		pDoServiceUpdateOpt = &doServiceUpdateOpt
	}
	return pDoServiceUpdateOpt
}
func (c *ServiceConverter) doAddonAvailabilityRuleToPOfferingpbServiceFilter(source do.AddonAvailabilityRule) *v1.ServiceFilter {
	var offeringpbServiceFilter v1.ServiceFilter
	if source.ServiceItemType != nil {
		offeringpbServiceFilter.ServiceItemType = c.offeringpbServiceItemTypeToOfferingpbServiceItemType(*source.ServiceItemType)
	}
	if source.AvailableForAllServices != nil {
		xbool := *source.AvailableForAllServices
		offeringpbServiceFilter.AvailableForAllServices = &xbool
	}
	if source.AvailableServiceIdList != nil {
		offeringpbServiceFilter.AvailableServiceIdList = make([]int64, len(source.AvailableServiceIdList))
		for i := 0; i < len(source.AvailableServiceIdList); i++ {
			offeringpbServiceFilter.AvailableServiceIdList[i] = source.AvailableServiceIdList[i]
		}
	}
	return &offeringpbServiceFilter
}
func (c *ServiceConverter) doBusinessOverrideRuleToPOfferingpbLocationOverrideRule(source do.BusinessOverrideRule) *v1.LocationOverrideRule {
	var offeringpbLocationOverrideRule v1.LocationOverrideRule
	offeringpbLocationOverrideRule.BusinessId = source.BusinessId
	if source.Price != nil {
		xfloat64 := *source.Price
		offeringpbLocationOverrideRule.Price = &xfloat64
	}
	if source.TaxID != nil {
		xint64 := *source.TaxID
		offeringpbLocationOverrideRule.TaxId = &xint64
	}
	if source.Duration != nil {
		xint32 := *source.Duration
		offeringpbLocationOverrideRule.Duration = &xint32
	}
	if source.MaxDuration != nil {
		xint322 := *source.MaxDuration
		offeringpbLocationOverrideRule.MaxDuration = &xint322
	}
	return &offeringpbLocationOverrideRule
}
func (c *ServiceConverter) doPetTypeAndBreedAvailabilityRuleToPOfferingpbCustomizedBreed(source do.PetTypeAndBreedAvailabilityRule) *v1.CustomizedBreed {
	var offeringpbCustomizedBreed v1.CustomizedBreed
	if source.PetTypeID != nil {
		offeringpbCustomizedBreed.PetTypeId = *source.PetTypeID
	}
	if source.Breeds != nil {
		offeringpbCustomizedBreed.Breeds = make([]string, len(source.Breeds))
		for i := 0; i < len(source.Breeds); i++ {
			offeringpbCustomizedBreed.Breeds[i] = source.Breeds[i]
		}
	}
	if source.IsAll != nil {
		xbool := *source.IsAll
		offeringpbCustomizedBreed.IsAll = &xbool
	}
	return &offeringpbCustomizedBreed
}
func (c *ServiceConverter) modelsMoeGroomingServiceLocationToDoBusinessOverrideRule(source models.MoeGroomingServiceLocation) do.BusinessOverrideRule {
	var doBusinessOverrideRule do.BusinessOverrideRule
	doBusinessOverrideRule.BusinessId = interface1.Int32ToInt64(source.BusinessID)
	if source.Price != nil {
		xfloat64 := *source.Price
		doBusinessOverrideRule.Price = &xfloat64
	}
	if source.TaxID != nil {
		xint64 := interface1.Int32ToInt64(*source.TaxID)
		doBusinessOverrideRule.TaxID = &xint64
	}
	if source.Duration != nil {
		xint32 := *source.Duration
		doBusinessOverrideRule.Duration = &xint32
	}
	if source.MaxDuration != nil {
		xint322 := *source.MaxDuration
		doBusinessOverrideRule.MaxDuration = &xint322
	}
	return doBusinessOverrideRule
}
func (c *ServiceConverter) offeringpbCreateServiceDefToDoCommission(source v1.CreateServiceDef) do.Commission {
	var doCommission do.Commission
	pBool := source.AddToCommissionBase
	doCommission.AddToCommissionBase = &pBool
	pBool2 := source.CanTip
	doCommission.CanTip = &pBool2
	return doCommission
}
func (c *ServiceConverter) offeringpbCreateServiceDefToPDoAddonAvailability(source v1.CreateServiceDef) *do.AddonAvailability {
	var doAddonAvailability do.AddonAvailability
	if source.ServiceFilter != nil {
		xbool := *source.ServiceFilter
		doAddonAvailability.ServiceFilter = &xbool
	}
	if source.ServiceFilterList != nil {
		doAddonAvailability.ServiceFilterList = make([]do.AddonAvailabilityRule, len(source.ServiceFilterList))
		for i := 0; i < len(source.ServiceFilterList); i++ {
			doAddonAvailability.ServiceFilterList[i] = c.ConvertServiceFilterToDO(source.ServiceFilterList[i])
		}
	}
	return &doAddonAvailability
}
func (c *ServiceConverter) offeringpbDateTypeToOfferingpbDateType(source v1.DateType) v1.DateType {
	var offeringpbDateType v1.DateType
	switch source {
	case v1.DateType_DATE_POINT:
		offeringpbDateType = v1.DateType_DATE_POINT
	case v1.DateType_DATE_TYPE_UNSPECIFIED:
		offeringpbDateType = v1.DateType_DATE_TYPE_UNSPECIFIED
	case v1.DateType_EVERY_DAY_EXCEPT_CHECKIN_DAY:
		offeringpbDateType = v1.DateType_EVERY_DAY_EXCEPT_CHECKIN_DAY
	case v1.DateType_EVERY_DAY_EXCEPT_CHECKOUT_DAY:
		offeringpbDateType = v1.DateType_EVERY_DAY_EXCEPT_CHECKOUT_DAY
	case v1.DateType_EVERY_DAY_INCLUDE_CHECKOUT_DAY:
		offeringpbDateType = v1.DateType_EVERY_DAY_INCLUDE_CHECKOUT_DAY
	case v1.DateType_FIRST_DAY:
		offeringpbDateType = v1.DateType_FIRST_DAY
	case v1.DateType_LAST_DAY:
		offeringpbDateType = v1.DateType_LAST_DAY
	case v1.DateType_SPECIFIC_DATE:
		offeringpbDateType = v1.DateType_SPECIFIC_DATE
	default: // ignored
	}
	return offeringpbDateType
}
func (c *ServiceConverter) offeringpbServiceItemTypeToOfferingpbServiceItemType(source v1.ServiceItemType) v1.ServiceItemType {
	var offeringpbServiceItemType v1.ServiceItemType
	switch source {
	case v1.ServiceItemType_BOARDING:
		offeringpbServiceItemType = v1.ServiceItemType_BOARDING
	case v1.ServiceItemType_DAYCARE:
		offeringpbServiceItemType = v1.ServiceItemType_DAYCARE
	case v1.ServiceItemType_DOG_WALKING:
		offeringpbServiceItemType = v1.ServiceItemType_DOG_WALKING
	case v1.ServiceItemType_EVALUATION:
		offeringpbServiceItemType = v1.ServiceItemType_EVALUATION
	case v1.ServiceItemType_GROOMING:
		offeringpbServiceItemType = v1.ServiceItemType_GROOMING
	case v1.ServiceItemType_GROUP_CLASS:
		offeringpbServiceItemType = v1.ServiceItemType_GROUP_CLASS
	case v1.ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED:
		offeringpbServiceItemType = v1.ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
	default: // ignored
	}
	return offeringpbServiceItemType
}
func (c *ServiceConverter) offeringpbServiceModel_SourceToOfferingpbServiceModel_Source(source v1.ServiceModel_Source) v1.ServiceModel_Source {
	var offeringpbServiceModel_Source v1.ServiceModel_Source
	switch source {
	case v1.ServiceModel_ENTERPRISE_HUB:
		offeringpbServiceModel_Source = v1.ServiceModel_ENTERPRISE_HUB
	case v1.ServiceModel_MOEGO_PLATFORM:
		offeringpbServiceModel_Source = v1.ServiceModel_MOEGO_PLATFORM
	case v1.ServiceModel_SOURCE_UNSPECIFIED:
		offeringpbServiceModel_Source = v1.ServiceModel_SOURCE_UNSPECIFIED
	default: // ignored
	}
	return offeringpbServiceModel_Source
}
func (c *ServiceConverter) offeringpbServicePriceUnitToOfferingpbServicePriceUnit(source v1.ServicePriceUnit) v1.ServicePriceUnit {
	var offeringpbServicePriceUnit v1.ServicePriceUnit
	switch source {
	case v1.ServicePriceUnit_PER_DAY:
		offeringpbServicePriceUnit = v1.ServicePriceUnit_PER_DAY
	case v1.ServicePriceUnit_PER_HOUR:
		offeringpbServicePriceUnit = v1.ServicePriceUnit_PER_HOUR
	case v1.ServicePriceUnit_PER_NIGHT:
		offeringpbServicePriceUnit = v1.ServicePriceUnit_PER_NIGHT
	case v1.ServicePriceUnit_PER_SESSION:
		offeringpbServicePriceUnit = v1.ServicePriceUnit_PER_SESSION
	case v1.ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED:
		offeringpbServicePriceUnit = v1.ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED
	default: // ignored
	}
	return offeringpbServicePriceUnit
}
func (c *ServiceConverter) offeringpbServiceTypeToOfferingpbServiceType(source v1.ServiceType) v1.ServiceType {
	var offeringpbServiceType v1.ServiceType
	switch source {
	case v1.ServiceType_ADDON:
		offeringpbServiceType = v1.ServiceType_ADDON
	case v1.ServiceType_SERVICE:
		offeringpbServiceType = v1.ServiceType_SERVICE
	case v1.ServiceType_SERVICE_TYPE_UNSPECIFIED:
		offeringpbServiceType = v1.ServiceType_SERVICE_TYPE_UNSPECIFIED
	default: // ignored
	}
	return offeringpbServiceType
}
func (c *ServiceConverter) offeringpbUpdateServiceDefToPDoAddonAvailability(source v1.UpdateServiceDef) *do.AddonAvailability {
	var doAddonAvailability do.AddonAvailability
	pBool := source.ServiceFilter
	doAddonAvailability.ServiceFilter = &pBool
	if source.ServiceFilterList != nil {
		doAddonAvailability.ServiceFilterList = make([]do.AddonAvailabilityRule, len(source.ServiceFilterList))
		for i := 0; i < len(source.ServiceFilterList); i++ {
			doAddonAvailability.ServiceFilterList[i] = c.ConvertServiceFilterToDO(source.ServiceFilterList[i])
		}
	}
	return &doAddonAvailability
}
func (c *ServiceConverter) offeringpbUpdateServiceDefToPDoCommission(source v1.UpdateServiceDef) *do.Commission {
	var doCommission do.Commission
	pBool := source.AddToCommissionBase
	doCommission.AddToCommissionBase = &pBool
	pBool2 := source.CanTip
	doCommission.CanTip = &pBool2
	return &doCommission
}
func (c *ServiceConverter) pDoAutoRolloverRuleToPOfferingpbAutoRolloverRule(source *do.AutoRolloverRule) *v1.AutoRolloverRule {
	var pOfferingpbAutoRolloverRule *v1.AutoRolloverRule
	if source != nil {
		var offeringpbAutoRolloverRule v1.AutoRolloverRule
		offeringpbAutoRolloverRule.Enabled = (*source).Enabled
		offeringpbAutoRolloverRule.AfterMinute = (*source).AfterMinute
		offeringpbAutoRolloverRule.TargetServiceId = (*source).TargetServiceId
		pOfferingpbAutoRolloverRule = &offeringpbAutoRolloverRule
	}
	return pOfferingpbAutoRolloverRule
}
func (c *ServiceConverter) pDoPetCodeAvailabilityToPOfferingpbServiceModel_PetCodeFilter(source *do.PetCodeAvailability) *v1.ServiceModel_PetCodeFilter {
	var pOfferingpbServiceModel_PetCodeFilter *v1.ServiceModel_PetCodeFilter
	if source != nil {
		var offeringpbServiceModel_PetCodeFilter v1.ServiceModel_PetCodeFilter
		offeringpbServiceModel_PetCodeFilter.IsWhiteList = (*source).IsWhiteList
		offeringpbServiceModel_PetCodeFilter.IsAllPetCode = (*source).IsAllPetCode
		if (*source).PetCodeIds != nil {
			offeringpbServiceModel_PetCodeFilter.PetCodeIds = make([]int64, len((*source).PetCodeIds))
			for i := 0; i < len((*source).PetCodeIds); i++ {
				offeringpbServiceModel_PetCodeFilter.PetCodeIds[i] = (*source).PetCodeIds[i]
			}
		}
		pOfferingpbServiceModel_PetCodeFilter = &offeringpbServiceModel_PetCodeFilter
	}
	return pOfferingpbServiceModel_PetCodeFilter
}
func (c *ServiceConverter) pOfferingpbAdditionalServiceRuleToPOfferingpbAdditionalServiceRule(source *v1.AdditionalServiceRule) *v1.AdditionalServiceRule {
	var pOfferingpbAdditionalServiceRule *v1.AdditionalServiceRule
	if source != nil {
		var offeringpbAdditionalServiceRule v1.AdditionalServiceRule
		offeringpbAdditionalServiceRule.Enable = (*source).Enable
		offeringpbAdditionalServiceRule.MinStayLength = (*source).MinStayLength
		if (*source).ApplyRules != nil {
			offeringpbAdditionalServiceRule.ApplyRules = make([]*v1.AdditionalServiceRule_ApplyRule, len((*source).ApplyRules))
			for i := 0; i < len((*source).ApplyRules); i++ {
				offeringpbAdditionalServiceRule.ApplyRules[i] = c.pOfferingpbAdditionalServiceRule_ApplyRuleToPOfferingpbAdditionalServiceRule_ApplyRule((*source).ApplyRules[i])
			}
		}
		pOfferingpbAdditionalServiceRule = &offeringpbAdditionalServiceRule
	}
	return pOfferingpbAdditionalServiceRule
}
func (c *ServiceConverter) pOfferingpbAdditionalServiceRule_ApplyRuleToPOfferingpbAdditionalServiceRule_ApplyRule(source *v1.AdditionalServiceRule_ApplyRule) *v1.AdditionalServiceRule_ApplyRule {
	var pOfferingpbAdditionalServiceRule_ApplyRule *v1.AdditionalServiceRule_ApplyRule
	if source != nil {
		var offeringpbAdditionalServiceRule_ApplyRule v1.AdditionalServiceRule_ApplyRule
		offeringpbAdditionalServiceRule_ApplyRule.ServiceId = (*source).ServiceId
		offeringpbAdditionalServiceRule_ApplyRule.DateType = c.offeringpbDateTypeToOfferingpbDateType((*source).DateType)
		offeringpbAdditionalServiceRule_ApplyRule.QuantityPerDay = (*source).QuantityPerDay
		pOfferingpbAdditionalServiceRule_ApplyRule = &offeringpbAdditionalServiceRule_ApplyRule
	}
	return pOfferingpbAdditionalServiceRule_ApplyRule
}
func (c *ServiceConverter) pOfferingpbCreateServiceDef_PetCodeFilterToPDoPetCodeAvailability(source *v1.CreateServiceDef_PetCodeFilter) *do.PetCodeAvailability {
	var pDoPetCodeAvailability *do.PetCodeAvailability
	if source != nil {
		var doPetCodeAvailability do.PetCodeAvailability
		doPetCodeAvailability.IsWhiteList = (*source).IsWhiteList
		doPetCodeAvailability.IsAllPetCode = (*source).IsAllPetCode
		if (*source).PetCodeIds != nil {
			doPetCodeAvailability.PetCodeIds = make([]int64, len((*source).PetCodeIds))
			for i := 0; i < len((*source).PetCodeIds); i++ {
				doPetCodeAvailability.PetCodeIds[i] = (*source).PetCodeIds[i]
			}
		}
		pDoPetCodeAvailability = &doPetCodeAvailability
	}
	return pDoPetCodeAvailability
}
func (c *ServiceConverter) pOfferingpbCustomizedBreedToDoPetTypeAndBreedAvailabilityRule(source *v1.CustomizedBreed) do.PetTypeAndBreedAvailabilityRule {
	var doPetTypeAndBreedAvailabilityRule do.PetTypeAndBreedAvailabilityRule
	if source != nil {
		var doPetTypeAndBreedAvailabilityRule2 do.PetTypeAndBreedAvailabilityRule
		pInt64 := (*source).PetTypeId
		doPetTypeAndBreedAvailabilityRule2.PetTypeID = &pInt64
		if (*source).IsAll != nil {
			xbool := *(*source).IsAll
			doPetTypeAndBreedAvailabilityRule2.IsAll = &xbool
		}
		if (*source).Breeds != nil {
			doPetTypeAndBreedAvailabilityRule2.Breeds = make([]string, len((*source).Breeds))
			for i := 0; i < len((*source).Breeds); i++ {
				doPetTypeAndBreedAvailabilityRule2.Breeds[i] = (*source).Breeds[i]
			}
		}
		doPetTypeAndBreedAvailabilityRule = doPetTypeAndBreedAvailabilityRule2
	}
	return doPetTypeAndBreedAvailabilityRule
}
func (c *ServiceConverter) pOfferingpbLocationOverrideRuleToDoBusinessOverrideRule(source *v1.LocationOverrideRule) do.BusinessOverrideRule {
	var doBusinessOverrideRule do.BusinessOverrideRule
	if source != nil {
		var doBusinessOverrideRule2 do.BusinessOverrideRule
		doBusinessOverrideRule2.BusinessId = (*source).BusinessId
		if (*source).Price != nil {
			xfloat64 := *(*source).Price
			doBusinessOverrideRule2.Price = &xfloat64
		}
		if (*source).TaxId != nil {
			xint64 := *(*source).TaxId
			doBusinessOverrideRule2.TaxID = &xint64
		}
		if (*source).Duration != nil {
			xint32 := *(*source).Duration
			doBusinessOverrideRule2.Duration = &xint32
		}
		if (*source).MaxDuration != nil {
			xint322 := *(*source).MaxDuration
			doBusinessOverrideRule2.MaxDuration = &xint322
		}
		doBusinessOverrideRule = doBusinessOverrideRule2
	}
	return doBusinessOverrideRule
}
func (c *ServiceConverter) pOfferingpbUpdateServiceDef_PetCodeFilterToPDoPetCodeAvailability(source *v1.UpdateServiceDef_PetCodeFilter) *do.PetCodeAvailability {
	var pDoPetCodeAvailability *do.PetCodeAvailability
	if source != nil {
		var doPetCodeAvailability do.PetCodeAvailability
		doPetCodeAvailability.IsWhiteList = (*source).IsWhiteList
		doPetCodeAvailability.IsAllPetCode = (*source).IsAllPetCode
		if (*source).PetCodeIds != nil {
			doPetCodeAvailability.PetCodeIds = make([]int64, len((*source).PetCodeIds))
			for i := 0; i < len((*source).PetCodeIds); i++ {
				doPetCodeAvailability.PetCodeIds[i] = (*source).PetCodeIds[i]
			}
		}
		pDoPetCodeAvailability = &doPetCodeAvailability
	}
	return pDoPetCodeAvailability
}
