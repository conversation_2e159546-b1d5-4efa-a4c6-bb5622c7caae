package converter

import (
	"github.com/lib/pq"
	"github.com/samber/lo"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func ConvertEvaluationDefToDO(source *offeringModelsV1.EvaluationDef) *do.EvaluationDO {
	// 因为 proto3 的默认行为是对“零值”进行优化，它会省略空值，而不是序列化它们
	// 如果请求方传递了一个空数组或空切片，服务端收到的是 nil 而不是一个空的切片
	// 这个接口是覆盖更新，所以统一转为空的切片
	if source.CustomizedLodgingIds == nil {
		source.CustomizedLodgingIds = []int64{}
	}

	result := &do.EvaluationDO{
		Name:                    source.Name,
		AvailableForAllBusiness: source.AvailableForAllBusiness,
		AvailableBusinessIds:    source.AvailableBusinessIds,
		ServiceItemTypes:        source.ServiceItemTypes,
		Price:                   lo.FromPtrOr(source.Price, 0),
		Duration:                source.Duration,
		ColorCode:               source.ColorCode,
		IsActive:                source.IsActive,
		LodgingFilter:           source.LodgingFilter,
		CustomizedLodgingIds:    source.CustomizedLodgingIds,
		Description:             source.Description,
		OnlineBookingAlias:      source.AliasForOnlineBooking,
		IsAllStaff:              lo.FromPtrOr(source.IsAllStaff, true),
		AllowedStaffList:        source.AllowedStaffList,
		AllowStaffAutoAssign:    lo.FromPtrOr(source.AllowStaffAutoAssign, false),
		IsResettable:            lo.FromPtrOr(source.IsResettable, false),
		ResetIntervalDays:       lo.FromPtrOr(source.ResetIntervalDays, 0),
		TaxId:                   lo.FromPtrOr(source.TaxId, 0),
		Source:                  source.Source,
	}
	if source.BreedFilterConfig != nil {
		result.BreedFilter = source.BreedFilterConfig.BreedFilter
		result.PetBreedFilters = ConvertPetBreedFilterPBToDO(source.BreedFilterConfig.Filters)
	}
	return result
}

func ConvertPetBreedFilterPBToDO(filters []*offeringModelsV1.PetBreedFilter) []do.PetBreedFilter {
	return lo.Map(filters, func(filter *offeringModelsV1.PetBreedFilter, _ int) do.PetBreedFilter {
		return do.PetBreedFilter{
			PetType:    filter.PetType,
			IsAllBreed: filter.IsAllBreed,
			BreedNames: filter.BreedNames,
		}
	})
}

func ConvertPetBreedFilterModelToDO(filters []models.EvaluationPetBreedFilter) []do.PetBreedFilter {
	return lo.Map(filters, func(filter models.EvaluationPetBreedFilter, _ int) do.PetBreedFilter {
		return do.PetBreedFilter{
			PetType:    filter.PetType,
			IsAllBreed: filter.IsAllBreed,
			BreedNames: filter.BreedNames,
		}
	})
}

func ConvertPetBreedFilterDOToPB(filters []do.PetBreedFilter) []*offeringModelsV1.PetBreedFilter {
	return lo.Map(filters, func(filter do.PetBreedFilter, _ int) *offeringModelsV1.PetBreedFilter {
		return &offeringModelsV1.PetBreedFilter{
			PetType:    filter.PetType,
			IsAllBreed: filter.IsAllBreed,
			BreedNames: filter.BreedNames,
		}
	})
}

func ConvertEvaluationDOToDBModel(source *do.EvaluationDO) models.Evaluation {
	if source == nil {
		return models.Evaluation{}
	}

	model := &models.Evaluation{
		ID:                      source.Id,
		Name:                    source.Name,
		CompanyID:               source.CompanyId,
		AvailableForAllBusiness: lo.ToPtr(source.AvailableForAllBusiness),
		AvailableBusinessIDList: source.AvailableBusinessIds,
		ServiceItemTypeList:     serviceItemTypeListToPqInt32Array(source.ServiceItemTypes),
		Price:                   &source.Price,
		Duration:                &source.Duration,
		ColorCode:               &source.ColorCode,
		IsActive:                source.IsActive,
		LodgingFilter:           source.LodgingFilter,
		AllowedLodgingList:      source.CustomizedLodgingIds,
		Description:             source.Description,
		OnlineBookingAlias:      source.OnlineBookingAlias,
		IsAllStaff:              lo.ToPtr(source.IsAllStaff),
		AllowedStaffList:        source.AllowedStaffList,
		AllowStaffAutoAssign:    source.AllowStaffAutoAssign,
		IsResettable:            source.IsResettable,
		ResetIntervalDays:       source.ResetIntervalDays,
		TaxID:                   source.TaxId,
		Source:                  lo.FromPtrOr(source.Source, offeringModelsV1.EvaluationModel_SOURCE_UNSPECIFIED),
	}

	// Initialize with empty array if nil
	if model.AllowedStaffList == nil {
		model.AllowedStaffList = []int64{}
	}

	return *model
}

func ConvertEvaluationDBModelToDO(source *models.Evaluation) *do.EvaluationDO {
	return &do.EvaluationDO{
		Id:                      source.ID,
		Name:                    source.Name,
		CompanyId:               source.CompanyID,
		AvailableForAllBusiness: lo.FromPtrOr(source.AvailableForAllBusiness, false),
		AvailableBusinessIds:    source.AvailableBusinessIDList,
		ServiceItemTypes:        pqInt32ArrayToServiceItemTypeList(source.ServiceItemTypeList),
		Price:                   lo.FromPtrOr(source.Price, 0),
		Duration:                lo.FromPtrOr(source.Duration, 0),
		ColorCode:               lo.FromPtrOr(source.ColorCode, ""),
		IsActive:                source.IsActive,
		LodgingFilter:           source.LodgingFilter,
		CustomizedLodgingIds:    source.AllowedLodgingList,
		Description:             source.Description,
		OnlineBookingAlias:      source.OnlineBookingAlias,
		IsOBAvailable:           lo.FromPtrOr(source.IsOnlineBookAvailable, false),
		IsAllStaff:              lo.FromPtrOr(source.IsAllStaff, false),
		AllowedStaffList:        source.AllowedStaffList,
		AllowStaffAutoAssign:    source.AllowStaffAutoAssign,
		IsResettable:            source.IsResettable,
		ResetIntervalDays:       source.ResetIntervalDays,
		BreedFilter:             source.BreedFilter,
		TaxId:                   source.TaxID,
		Source:                  offeringModelsV1.EvaluationModel_Source(source.Source).Enum(),
	}
}

func ConvertEvaluationDOToPBModel(source *do.EvaluationDO) *offeringModelsV1.EvaluationModel {
	return &offeringModelsV1.EvaluationModel{
		Id:                      source.Id,
		AvailableForAllBusiness: source.AvailableForAllBusiness,
		AvailableBusinessIds:    source.AvailableBusinessIds,
		ServiceItemTypes:        source.ServiceItemTypes,
		Price:                   source.Price,
		Duration:                source.Duration,
		ColorCode:               source.ColorCode,
		Name:                    source.Name,
		IsActive:                source.IsActive,
		LodgingFilter:           source.LodgingFilter,
		CustomizedLodgingIds:    source.CustomizedLodgingIds,
		Description:             source.Description,
		AliasForOnlineBooking:   lo.FromPtrOr(source.OnlineBookingAlias, source.Name),
		IsAllStaff:              source.IsAllStaff,
		AllowedStaffList:        source.AllowedStaffList,
		AllowStaffAutoAssign:    source.AllowStaffAutoAssign,
		IsResettable:            source.IsResettable,
		ResetIntervalDays:       source.ResetIntervalDays,
		CompanyId:               source.CompanyId,
		BreedFilter:             source.BreedFilter,
		BreedFilters:            ConvertPetBreedFilterDOToPB(source.PetBreedFilters),
		TaxId:                   source.TaxId,
		Source: lo.FromPtrOr(source.Source,
			offeringModelsV1.EvaluationModel_SOURCE_UNSPECIFIED),
	}
}

func ConvertEvaluationDOToUpdateOpt(source *do.EvaluationDO) models.EvaluationUpdateOpt {
	if source == nil {
		return models.EvaluationUpdateOpt{}
	}

	model := models.EvaluationUpdateOpt{
		Name:                    lo.ToPtr(source.Name),
		AvailableForAllBusiness: lo.ToPtr(source.AvailableForAllBusiness),
		AvailableBusinessIdList: lo.ToPtr(pq.Int64Array(source.AvailableBusinessIds)),
		ServiceItemTypeList:     lo.ToPtr(serviceItemTypeListToPqInt32Array(source.ServiceItemTypes)),
		Price:                   lo.ToPtr(source.Price),
		Duration:                lo.ToPtr(source.Duration),
		ColorCode:               lo.ToPtr(source.ColorCode),
		IsActive:                lo.ToPtr(source.IsActive),
		LodgingFilter:           lo.ToPtr(source.LodgingFilter),
		CustomizedLodgingIds:    lo.ToPtr(pq.Int64Array(source.CustomizedLodgingIds)),
		Description:             lo.ToPtr(source.Description),
		OnlineBookingAlias:      source.OnlineBookingAlias,
		IsAllStaff:              lo.ToPtr(source.IsAllStaff),
		AllowStaffAutoAssign:    lo.ToPtr(source.AllowStaffAutoAssign),
		IsResettable:            lo.ToPtr(source.IsResettable),
		ResetIntervalDays:       lo.ToPtr(source.ResetIntervalDays),
		TaxId:                   lo.ToPtr(source.TaxId),
	}
	if source.Source != nil {
		model.Source = lo.ToPtr(int16(*source.Source))
	}
	// Handle AllowedStaffList separately
	if source.AllowedStaffList == nil {
		model.AllowedStaffList = lo.ToPtr(pq.Int64Array([]int64{}))
	} else {
		model.AllowedStaffList = lo.ToPtr(pq.Int64Array(source.AllowedStaffList))
	}

	return model
}

func ConvertEvaluationDOToBriefView(source *do.EvaluationDO) *offeringModelsV1.EvaluationBriefView {
	return &offeringModelsV1.EvaluationBriefView{
		Id:                       source.Id,
		Price:                    source.Price,
		Duration:                 source.Duration,
		ColorCode:                source.ColorCode,
		Name:                     source.Name,
		Description:              source.Description,
		AliasForOnlineBooking:    lo.FromPtrOr(source.OnlineBookingAlias, source.Name),
		IsOnlineBookingAvailable: source.IsOBAvailable,
		IsAllStaff:               source.IsAllStaff,
		AllowedStaffList:         source.AllowedStaffList,
		AllowStaffAutoAssign:     source.AllowStaffAutoAssign,
		IsActive:                 source.IsActive,
		BreedFilter:              source.BreedFilter,
		BreedFilters:             ConvertPetBreedFilterDOToPB(source.PetBreedFilters),
		TaxId:                    source.TaxId,
	}
}

func serviceItemTypeListToPqInt32Array(source []offeringModelsV1.ServiceItemType) pq.Int32Array {
	var pqInt32Array pq.Int32Array
	if source != nil {
		pqInt32Array = make(pq.Int32Array, len(source))
		for i := 0; i < len(source); i++ {
			pqInt32Array[i] = int32(source[i])
		}
	}
	return pqInt32Array
}

func pqInt32ArrayToServiceItemTypeList(source pq.Int32Array) []offeringModelsV1.ServiceItemType {
	var serviceItemTypeList []offeringModelsV1.ServiceItemType
	if source != nil {
		serviceItemTypeList = make([]offeringModelsV1.ServiceItemType, len(source))
		for i := 0; i < len(source); i++ {
			serviceItemTypeList[i] = offeringModelsV1.ServiceItemType(source[i])
		}
	}
	return serviceItemTypeList
}
