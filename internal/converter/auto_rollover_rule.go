package converter

import (
	offeringmodel "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter/impl"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

var autoRolloverRuleConverter = &impl.AutoRolloverRuleConverter{}

func ConvertAutoRolloverRuleDBModelToDO(source *models.MoeAutoRollover) *do.AutoRolloverRule {
	if source == nil {
		return do.DefaultAutoRolloverRule
	}
	return autoRolloverRuleConverter.ConvertDBModelToDO(source)
}

func ConvertAutoRolloverRuleDOToPB(source *do.AutoRolloverRule) *offeringmodel.AutoRolloverRuleModel {
	return autoRolloverRuleConverter.ConvertDOToPB(source)
}
