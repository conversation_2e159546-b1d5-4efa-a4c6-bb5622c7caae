package converter

import (
	"errors"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func ConvertCareTypeDOsToViews(careTypeDOs []*do.CustomizeCareTypeDO) ([]*offeringpb.CustomizeCareTypeView, error) {
	if len(careTypeDOs) == 0 {
		return nil, errors.New("careTypeDOs is empty")
	}

	result := make([]*offeringpb.CustomizeCareTypeView, 0, len(careTypeDOs))
	for _, careType := range careTypeDOs {
		result = append(result, &offeringpb.CustomizeCareTypeView{
			Name:            careType.Name,
			ServiceItemType: careType.ServiceItemType,
			Sort:            careType.Sort,
		})
	}
	return result, nil
}

func ConvertCustomizeCareTypesToDOs(careTypes []*models.CustomizeCareType) ([]*do.CustomizeCareTypeDO, error) {
	if len(careTypes) == 0 {
		return nil, nil
	}

	result := make([]*do.CustomizeCareTypeDO, 0, len(careTypes))
	for _, item := range careTypes {
		result = append(result, &do.CustomizeCareTypeDO{
			ID:              item.ID,
			Name:            item.Name,
			CompanyID:       item.CompanyID,
			ServiceItemType: item.ServiceItemType,
			Sort:            item.Sort,
			UpdatedBy:       item.UpdatedBy,
			CreatedAt:       item.CreatedAt,
			UpdatedAt:       item.UpdatedAt,
		})
	}
	return result, nil
}
