package _interface

import (
	pbModels "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

// goverter:converter
// goverter:output:file ../impl/lodging_unit_impl.go
// goverter:output:package impl
// goverter:ignoreMissing
// goverter:matchIgnoreCase
// goverter:ignoreUnexported
// goverter:useZeroValueOnPointerInconsistency
type LodgingUnitConverter interface {
	// goverter:map TokenStaffId CreatedBy
	ConverterCreateRequestToDO(source *offeringV1.CreateLodgingUnitRequest) *do.LodgingUnitDO
	// goverter:map TokenStaffId UpdatedBy
	ConverterUpdateRequestToUpdateOpt(source *offeringV1.UpdateLodgingUnitRequest) *do.LodgingUnitUpdateOpt
	ConverterDoToPB(source *do.LodgingUnitDO) *pbModels.LodgingUnitModel
	ConverterDOToPO(source *do.LodgingUnitDO) *models.LodgingUnit
	ConverterPOToDO(source *models.LodgingUnit) *do.LodgingUnitDO
	ConvertUpdateOptDoToModel(source *do.LodgingUnitUpdateOpt) *models.LodgingUnitUpdateOpt
	ConvertUpdateByIdToUpdateOpt(source *do.LodgingUnitUpdateByIDOpt) *do.LodgingUnitUpdateOpt
}

//go:generate go run github.com/jmattheis/goverter/cmd/goverter gen ./
