package _interface

import (
	offeringmodel "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

// goverter:converter
// goverter:name AutoRolloverRuleConverter
// goverter:output:file ../impl/auto_rollover_rule_impl.go
// goverter:output:package impl
// goverter:useZeroValueOnPointerInconsistency
// goverter:matchIgnoreCase
// goverter:ignoreUnexported
type AutoRolloverRuleConverter interface {
	ConvertDBModelToDO(source *models.MoeAutoRollover) *do.AutoRolloverRule

	ConvertDOToPB(source *do.AutoRolloverRule) *offeringmodel.AutoRolloverRuleModel
}

//go:generate go run github.com/jmattheis/goverter/cmd/goverter gen ./
