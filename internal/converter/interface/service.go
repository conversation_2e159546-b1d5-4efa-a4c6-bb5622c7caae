package _interface

import (
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"

	_ "github.com/jmattheis/goverter"
)

// goverter:converter
// goverter:name ServiceConverter
// goverter:output:file ../impl/service_impl.go
// goverter:output:package impl
// goverter:useZeroValueOnPointerInconsistency
// goverter:matchIgnoreCase
// goverter:ignoreUnexported
// goverter:extend Int32ToInt64
// goverter:extend TimeStampToPBTime
// goverter:enum:unknown @ignore
type ServiceConverter interface {
	// goverter:map . Availability
	// goverter:map Type ServiceType
	// goverter:map . Commission
	// goverter:ignore ServiceID
	// goverter:map . AddonAvailability
	// goverter:ignore CreateTime
	// goverter:ignore UpdateTime
	// goverter:ignore IsDeleted
	// goverter:ignore CompanyId
	ConvertCreateDefToDO(source *offeringpb.CreateServiceDef) *do.ServiceDO

	// goverter:map WeightRange AvailableMinWeight | GetMinWeight
	// goverter:map WeightRange AvailableMaxWeight | GetMaxWeight
	// goverter:ignore StaffOverrideList
	ConvertAvailabilityToDO(source offeringpb.CreateServiceDef) do.Availability

	// goverter:ignore ServiceId
	ConvertAutoRolloverDef(source *offeringpb.AutoRolloverRuleDef) *do.AutoRolloverRule

	// goverter:autoMap Availability
	// goverter:autoMap Commission
	// goverter:autoMap AddonAvailability
	// goverter:map Availability AvailableBusinessIdList | GetAvailableBusinessIdList
	// goverter:map Availability WeightRange | GetWeightRange
	// goverter:map ServiceType Type
	// goverter:map BundleServiceIds BundleServiceIds
	// goverter:ignore LocationStaffOverrideList
	ConvertServiceDOToPBModel(source *do.ServiceDO) *offeringpb.ServiceModel

	ConvertAddonAvailabilityToDO(source *offeringpb.ServiceModel) *do.AddonAvailability

	ConvertServiceFilterToDO(source *offeringpb.ServiceFilter) do.AddonAvailabilityRule

	ConvertLocationOverrideRuleModelListToDO(source []models.MoeGroomingServiceLocation) []do.BusinessOverrideRule

	// goverter:map . Availability
	// goverter:map . Commission
	// goverter:map . AddonAvailability
	// goverter:map BundleServiceIds BundleServiceIds
	ConvertUpdateDefToUpdateOpt(source *offeringpb.UpdateServiceDef) *do.ServiceUpdateOpt

	// goverter:map WeightRange AvailableMinWeight | GetMinWeight
	// goverter:map WeightRange AvailableMaxWeight | GetMaxWeight
	// goverter:ignore StaffOverrideList
	ConvertAvailabilityToUpdateOpt(source offeringpb.UpdateServiceDef) *do.Availability

	ConvertLocationOverrideRuleToUpdateOpt(source *offeringpb.LocationOverrideRule) do.LocationOverrideRuleUpdateOpt
}

func GetMinWeight(weightRange []float64) *float64 {
	if len(weightRange) != 2 {
		return nil
	}
	return proto.Float64(weightRange[0])
}

func GetMaxWeight(weightRange []float64) *float64 {
	if len(weightRange) != 2 {
		return nil
	}
	return proto.Float64(weightRange[1])
}

func GetAvailableBusinessIdList(availability do.Availability) []int64 {
	if *availability.IsAllLocation {
		return nil
	}
	if availability.LocationOverrideList == nil {
		return nil
	}
	var result []int64
	for _, override := range availability.LocationOverrideList {
		result = append(result, override.BusinessId)
	}
	return result
}

func GetWeightRange(availability do.Availability) []float64 {
	if availability.WeightFilter == nil {
		return nil
	}
	if !*availability.WeightFilter {
		return nil
	}
	return []float64{*availability.AvailableMinWeight, *availability.AvailableMaxWeight}
}

func Int32ToInt64(i int32) int64 {
	return int64(i)
}

func TimeStampToPBTime(timestamp int64) timestamppb.Timestamp {
	return timestamppb.Timestamp{Seconds: timestamp}
}

//go:generate go run github.com/jmattheis/goverter/cmd/goverter gen ./
