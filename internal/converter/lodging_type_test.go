package converter

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func TestConvertCreateLodgingTypeRequestToDO(t *testing.T) {
	source := &v1.CreateLodgingTypeRequest{
		CompanyId:    1,
		Name:         "name",
		Description:  "desc",
		PhotoList:    []string{"photo1", "photo2"},
		MaxPetNum:    2,
		TokenStaffId: 4,
	}
	got := ConvertCreateLodgingTypeRequestToDO(source)
	assert.Equal(t, source.CompanyId, got.CompanyID)
	assert.Equal(t, source.Name, got.Name)
	assert.Equal(t, source.Description, got.Description)
	assert.Equal(t, source.PhotoList, got.PhotoList)
	assert.Equal(t, source.MaxPetNum, got.MaxPetNum)
	assert.Equal(t, source.TokenStaffId, got.CreatedBy)
	assert.Equal(t, source.GetSource(), got.Source)
}

func TestConvertLodgingTypeDOToPB(t *testing.T) {
	source := &do.LodgingTypeDO{
		ID:          1,
		Name:        "name",
		Description: "desc",
		PhotoList:   []string{"photo1", "photo2"},
		MaxPetNum:   3,
		Source:      offeringpb.LodgingTypeModel_ENTERPRISE_HUB,
	}
	got := ConvertLodgingTypeDOToPB(source)
	assert.Equal(t, source.ID, got.Id)
	assert.Equal(t, source.Name, got.Name)
	assert.Equal(t, source.Description, got.Description)
	assert.Equal(t, source.PhotoList, got.PhotoList)
	assert.Equal(t, source.MaxPetNum, got.MaxPetNum)
	assert.Equal(t, source.Source, got.Source)
}

func TestConvertLodgingTypeDOToPO(t *testing.T) {
	source := &do.LodgingTypeDO{
		CompanyID:   1,
		Name:        "name",
		Description: "desc",
		PhotoList:   []string{"photo1", "photo2"},
		MaxPetNum:   2,
		CreatedBy:   4,
		Source:      offeringpb.LodgingTypeModel_ENTERPRISE_HUB,
	}
	got := ConvertLodgingTypeDOToPO(source)
	assert.Equal(t, source.CompanyID, got.CompanyID)
	assert.Equal(t, source.Name, got.Name)
	assert.Equal(t, source.Description, got.Description)
	assert.Equal(t, source.PhotoList, got.Photo)
	assert.Equal(t, source.MaxPetNum, got.MaxPetNum)
	assert.Equal(t, source.CreatedBy, got.CreatedBy)
	assert.Equal(t, source.Source, got.Source)
}

func TestConvertLodgingTypePOToDO(t *testing.T) {
	source := &models.LodgingType{
		ID:          6,
		CompanyID:   1,
		Name:        "name",
		Description: "desc",
		Photo:       []string{"photo1", "photo2"},
		MaxPetNum:   2,
		DeletedAt:   gorm.DeletedAt{Valid: true, Time: time.Now()},
		Source:      offeringpb.LodgingTypeModel_ENTERPRISE_HUB,
	}
	got := ConvertLodgingTypePOToDO(source)
	assert.Equal(t, source.ID, got.ID)
	assert.Equal(t, source.CompanyID, got.CompanyID)
	assert.Equal(t, source.Name, got.Name)
	assert.Equal(t, source.Description, got.Description)
	assert.Equal(t, source.Photo, got.PhotoList)
	assert.Equal(t, source.MaxPetNum, got.MaxPetNum)
	assert.Equal(t, source.DeletedAt.Valid, got.IsDeleted)
	assert.Equal(t, source.Source, got.Source)
}

func TestConvertLodgingTypeUpdateOptDoToModel(t *testing.T) {
	source := &do.LodgingTypeUpdateOpt{
		Description: proto.String("desc"),
		Name:        proto.String("name"),
		PhotoList:   []string{"photo1", "photo2"},
		MaxPetNum:   proto.Int32(1),
		UpdatedBy:   proto.Int64(3),
		Source:      offeringpb.LodgingTypeModel_ENTERPRISE_HUB.Enum(),
	}
	got := ConvertLodgingTypeUpdateOptDoToModel(source)
	assert.Equal(t, source.Description, got.Description)
	assert.Equal(t, source.Name, got.Name)
	assert.Equal(t, source.PhotoList, *got.Photo)
	assert.Equal(t, source.MaxPetNum, got.MaxPetNum)
	assert.Equal(t, source.UpdatedBy, got.UpdatedBy)
	assert.Equal(t, source.Source, got.Source)
}

func TestConvertUpdateLodgingTypeRequestToUpdateOpt(t *testing.T) {
	source := &v1.UpdateLodgingTypeRequest{
		TokenStaffId: 3,
		Name:         proto.String("name"),
		Description:  proto.String("desc"),
		PhotoList:    &v1.PhotoList{PhotoList: []string{"photo1", "photo2"}},
		MaxPetNum:    proto.Int32(1),
		Source:       offeringpb.LodgingTypeModel_ENTERPRISE_HUB.Enum(),
	}
	got := ConvertUpdateLodgingTypeRequestToUpdateOpt(source)
	assert.Equal(t, source.Name, got.Name)
	assert.Equal(t, source.Description, got.Description)
	assert.Equal(t, source.PhotoList.PhotoList, got.PhotoList)
	assert.Equal(t, source.MaxPetNum, got.MaxPetNum)
	assert.Equal(t, source.TokenStaffId, *got.UpdatedBy)
	assert.Equal(t, source.Source, got.Source)
}
