package converter

import (
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"

	"github.com/lib/pq"
	"github.com/samber/lo"
)

func ConvertCreateLodgingTypeRequestToDO(source *offeringsvcpb.CreateLodgingTypeRequest) *do.LodgingTypeDO {
	if source == nil {
		return nil
	}
	result := &do.LodgingTypeDO{
		CompanyID:          source.CompanyId,
		Name:               source.Name,
		Description:        source.Description,
		PhotoList:          source.PhotoList,
		MaxPetNum:          source.MaxPetNum,
		CreatedBy:          source.TokenStaffId,
		AllowedPetSizeList: source.PetSizeIds,
		PetSizeFilter:      source.PetSizeFilter,
		Type:               source.LodgingUnitType,
		Source:             source.GetSource(),
	}
	// 兼容老版本
	// nolint:staticcheck
	if source.AllPetSizes {
		result.PetSizeFilter = source.AllPetSizes
	}
	if result.Type == offeringpb.LodgingUnitType_LODGING_UNIT_TYPE_UNSPECIFIED {
		result.Type = offeringpb.LodgingUnitType_AREA
	}
	return result
}

func ConvertUpdateLodgingTypeRequestToUpdateOpt(source *offeringsvcpb.UpdateLodgingTypeRequest) *do.LodgingTypeUpdateOpt {
	if source == nil {
		return nil
	}
	result := &do.LodgingTypeUpdateOpt{
		Description:        source.Description,
		Name:               source.Name,
		MaxPetNum:          source.MaxPetNum,
		UpdatedBy:          &source.TokenStaffId,
		AllowedPetSizeList: source.PetSizeIds,
		PetSizeFilter:      source.PetSizeFilter,
		Type:               source.LodgingUnitType,
		Source:             source.Source,
	}
	// nolint:staticcheck
	if source.AllPetSizes != nil {
		result.PetSizeFilter = source.AllPetSizes
	}
	if source.PhotoList != nil {
		result.PhotoList = source.PhotoList.PhotoList
	}
	return result
}

func ConvertLodgingTypeDOToPB(source *do.LodgingTypeDO) *offeringpb.LodgingTypeModel {
	if source == nil {
		return nil
	}
	return &offeringpb.LodgingTypeModel{
		Id:              source.ID,
		Name:            source.Name,
		Description:     source.Description,
		PhotoList:       source.PhotoList,
		MaxPetNum:       source.MaxPetNum,
		AllPetSizes:     source.PetSizeFilter,
		PetSizeFilter:   source.PetSizeFilter,
		PetSizeIds:      source.AllowedPetSizeList,
		LodgingUnitType: source.Type,
		Sort:            source.Sort,
		Source:          source.Source,
	}
}

func ConvertLodgingTypeDOListToPB(source []*do.LodgingTypeDO) []*offeringpb.LodgingTypeModel {
	if source == nil {
		return nil
	}
	result := make([]*offeringpb.LodgingTypeModel, len(source))
	for i := 0; i < len(source); i++ {
		result[i] = ConvertLodgingTypeDOToPB(source[i])
	}
	return result
}

func ConvertLodgingTypeDOToPO(source *do.LodgingTypeDO) *models.LodgingType {
	if source == nil {
		return nil
	}
	return &models.LodgingType{
		ID:                 source.ID,
		CompanyID:          source.CompanyID,
		Description:        source.Description,
		Name:               source.Name,
		Photo:              source.PhotoList,
		MaxPetNum:          source.MaxPetNum,
		CreatedBy:          source.CreatedBy,
		AllowedPetSizeList: source.AllowedPetSizeList,
		PetSizeFilter:      source.PetSizeFilter,
		Type:               source.Type,
		Sort:               source.Sort,
		Source:             source.Source,
	}
}

func ConvertLodgingTypePOToDO(source *models.LodgingType) *do.LodgingTypeDO {
	if source == nil {
		return nil
	}
	return &do.LodgingTypeDO{
		ID:                 source.ID,
		CompanyID:          source.CompanyID,
		Name:               source.Name,
		Description:        source.Description,
		PhotoList:          source.Photo,
		MaxPetNum:          source.MaxPetNum,
		CreatedBy:          source.CreatedBy,
		IsDeleted:          source.DeletedAt.Valid,
		AllowedPetSizeList: source.AllowedPetSizeList,
		PetSizeFilter:      source.PetSizeFilter,
		Type:               source.Type,
		Sort:               source.Sort,
		Source:             source.Source,
	}
}

func ConvertLodgingTypePOListToDO(source []*models.LodgingType) []*do.LodgingTypeDO {
	if source == nil {
		return nil
	}
	result := make([]*do.LodgingTypeDO, len(source))
	for i := 0; i < len(source); i++ {
		result[i] = ConvertLodgingTypePOToDO(source[i])
	}
	return result
}

func ConvertLodgingTypeUpdateOptDoToModel(source *do.LodgingTypeUpdateOpt) *models.LodgingTypeUpdateOpt {
	if source == nil {
		return nil
	}
	result := &models.LodgingTypeUpdateOpt{
		Description:   source.Description,
		Name:          source.Name,
		MaxPetNum:     source.MaxPetNum,
		UpdatedBy:     source.UpdatedBy,
		PetSizeFilter: source.PetSizeFilter,
		Type:          source.Type,
		Sort:          source.Sort,
		Source:        source.Source,
	}
	if source.PhotoList != nil {
		result.Photo = &source.PhotoList
	} else {
		/*
			bug：https://moego.atlassian.net/browse/MER-4896
			lodging type 删除了照片后，点 save 时，前端传了就是空数组，但因为apiv3的photoList是一个repeated的结构而不是一个message结构，
			后台无法通过判断photoList是否为nil来确定前端传的是空数组还是没传photoList，理论上没传photoList的话不应该更新，但基于现在设计只
			能每次update时都让前端把photo传过来，这块已经和PiEgg对齐
		*/
		result.Photo = &[]string{}
	}
	if source.AllowedPetSizeList != nil {
		result.AllowedPetSizeList = lo.ToPtr(pq.Int64Array(source.AllowedPetSizeList))
	}
	return result
}
