package converter

import (
	"errors"
	"fmt"
	"time"

	moneyutils "github.com/MoeGolibrary/go-lib/common/proto/money"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/utils"
)

func ConvertCreateGroupClassInstanceRequestToDO(source *offeringsvcpb.CreateInstanceAndSessionsRequest) (*do.GroupClassInstanceCreateOpt, error) {
	if source == nil {
		return nil, errors.New("source is nil")
	}

	return &do.GroupClassInstanceCreateOpt{
		CompanyID:    source.GetCompanyId(),
		BusinessID:   source.GetBusinessId(),
		GroupClassID: source.GetGroupClassId(),
		StaffID:      source.GetStaffId(),
		StartTime:    source.GetStartTime(),
		Occurrence:   source.GetOccurrence(),
	}, nil
}

func ConvertGroupClassInstanceDOToPO(source *do.GroupClassInstanceDO) (*models.GroupClassInstance, error) {
	if source == nil {
		return nil, errors.New("source is nil")
	}
	return &models.GroupClassInstance{
		ID:           source.ID,
		CompanyID:    source.CompanyID,
		BusinessID:   source.BusinessID,
		ServiceID:    source.ServiceID,
		Name:         source.Name,
		StaffID:      source.StaffID,
		Price:        moneyutils.ToDecimal(source.Price),
		TaxID:        source.TaxID,
		CurrencyCode: source.Price.GetCurrencyCode(),
		StartTime:    source.StartTime,
		Status:       source.Status,
		TimeZone:     source.StartTime.Location().String(),
		Capacity:     source.Capacity,
		Occurrence:   source.Occurrence,
	}, nil
}

func ConvertGroupClassInstancePOToDO(source *models.GroupClassInstance) (*do.GroupClassInstanceDO, error) {
	if source == nil {
		return nil, errors.New("source is nil")
	}

	loc, err := time.LoadLocation(source.TimeZone)
	if err != nil {
		return nil, fmt.Errorf("failed to load location %s: %v", source.TimeZone, err)
	}
	startTime := source.StartTime.In(loc)

	return &do.GroupClassInstanceDO{
		ID:         source.ID,
		CompanyID:  source.CompanyID,
		BusinessID: source.BusinessID,
		ServiceID:  source.ServiceID,
		Name:       source.Name,
		StaffID:    source.StaffID,
		Price:      moneyutils.FromDecimal(source.Price, source.CurrencyCode),
		StartTime:  &startTime,
		Capacity:   source.Capacity,
		Occurrence: source.Occurrence,
		Status:     source.Status,
		CreatedAt:  source.CreatedAt,
		UpdatedAt:  source.UpdatedAt,
	}, nil
}

func ConvertGroupClassInstancePOListToDO(source []*models.GroupClassInstance) ([]*do.GroupClassInstanceDO, error) {
	if len(source) == 0 {
		return nil, nil
	}

	results := make([]*do.GroupClassInstanceDO, 0, len(source))
	for _, item := range source {
		do, err := ConvertGroupClassInstancePOToDO(item)
		if err != nil {
			return nil, err
		}
		results = append(results, do)
	}
	return results, nil
}

func ConvertGroupClassInstanceDOToPB(source *do.GroupClassInstanceDO) *offeringpb.GroupClassInstance {
	pb := &offeringpb.GroupClassInstance{
		Id:           source.ID,
		CompanyId:    source.CompanyID,
		BusinessId:   source.BusinessID,
		GroupClassId: source.ServiceID,
		Name:         source.Name,
		StaffId:      source.StaffID,
		StartTime:    utils.GoogleDateTimeFromTime(source.StartTime),
		Capacity:     int32(source.Capacity),
		Occurrence:   source.Occurrence,
		Price:        source.Price,
		Status:       source.Status,
	}
	return pb
}

func ConvertGroupClassInstanceDOListToPB(source []*do.GroupClassInstanceDO) []*offeringpb.GroupClassInstance {
	pbs := make([]*offeringpb.GroupClassInstance, 0, len(source))
	for _, item := range source {
		pbs = append(pbs, ConvertGroupClassInstanceDOToPB(item))
	}
	return pbs
}

func ConvertGroupClassInstanceUpdateReqToOpt(source *offeringsvcpb.UpdateInstanceAndSessionsRequest) *do.GroupClassInstanceUpdateOpt {
	if source == nil {
		return nil
	}
	opt := &do.GroupClassInstanceUpdateOpt{}
	if source.GetStaffId() > 0 {
		staffID := source.GetStaffId()
		opt.StaffID = &staffID
	}
	if source.GetStartTime() != nil {
		opt.StartTime = source.GetStartTime()
	}
	if source.GetOccurrence() != nil {
		opt.Occurrence = source.GetOccurrence()
	}
	return opt
}
