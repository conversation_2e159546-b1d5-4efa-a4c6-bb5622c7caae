package converter

import (
	"fmt"
	"time"

	"google.golang.org/genproto/googleapis/type/interval"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func ConvertGroupClassSessionDOToPO(source *do.GroupClassSessionDO) (*models.GroupClassSession, error) {
	if source == nil {
		return nil, nil
	}

	return &models.GroupClassSession{
		ID:          source.ID,
		CompanyID:   source.CompanyID,
		BusinessID:  source.BusinessID,
		InstanceID:  source.InstanceID,
		StartTime:   source.StartTime,
		DurationMin: int64(source.Duration.Minutes()),
		IsModified:  proto.Bool(source.IsModified),
	}, nil
}

func ConvertGroupClassSessionDOListToPO(source []*do.GroupClassSessionDO) ([]*models.GroupClassSession, error) {
	if len(source) == 0 {
		return nil, nil
	}
	dest := make([]*models.GroupClassSession, 0, len(source))
	for _, item := range source {
		po, err := ConvertGroupClassSessionDOToPO(item)
		if err != nil {
			return nil, err
		}
		dest = append(dest, po)
	}
	return dest, nil
}

func ConvertGroupClassSessionDOToPB(source *do.GroupClassSessionDO, location *time.Location) *offeringpb.GroupClassSession {
	if source == nil {
		return nil
	}
	startTime := *source.StartTime
	endTime := source.StartTime.Add(source.Duration)
	pb := &offeringpb.GroupClassSession{
		Id:                   source.ID,
		CompanyId:            source.CompanyID,
		BusinessId:           source.BusinessID,
		GroupClassInstanceId: source.InstanceID,
		Interval: &interval.Interval{
			StartTime: timestamppb.New(startTime),
			EndTime:   timestamppb.New(endTime),
		},
		Duration:   durationpb.New(source.Duration),
		IsModified: source.IsModified,
	}
	now := time.Now()
	companyNow := now.In(location)
	todayDate := time.Date(companyNow.Year(), companyNow.Month(), companyNow.Day(), 0, 0, 0, 0, location)
	startDate := time.Date(startTime.Year(), startTime.Month(), startTime.Day(), 0, 0, 0, 0, location)
	endDate := time.Date(endTime.Year(), endTime.Month(), endTime.Day(), 0, 0, 0, 0, location)
	if startDate.After(todayDate) {
		pb.Status = offeringpb.GroupClassSession_UPCOMING
	} else if endDate.After(todayDate) || endDate.Equal(todayDate) {
		pb.Status = offeringpb.GroupClassSession_IN_PROGRESS
	} else {
		pb.Status = offeringpb.GroupClassSession_PAST
	}
	return pb
}

func ConvertGroupClassSessionDOListToPB(source []*do.GroupClassSessionDO, location *time.Location) []*offeringpb.GroupClassSession {
	pbs := make([]*offeringpb.GroupClassSession, 0, len(source))
	for _, do := range source {
		pbs = append(pbs, ConvertGroupClassSessionDOToPB(do, location))
	}
	return pbs
}

func ConvertGroupClassSessionPOToDO(source *models.GroupClassSession, timezone string) (*do.GroupClassSessionDO, error) {
	if source == nil {
		return nil, nil
	}
	location, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, fmt.Errorf("failed to load location %s: %v", timezone, err)
	}
	startTime := source.StartTime.In(location)
	return &do.GroupClassSessionDO{
		ID:         source.ID,
		CompanyID:  source.CompanyID,
		BusinessID: source.BusinessID,
		InstanceID: source.InstanceID,
		StartTime:  &startTime,
		Duration:   time.Duration(source.DurationMin) * time.Minute,
		IsModified: *source.IsModified,
	}, nil
}

func ConvertGroupClassSessionPOListToDO(source []*models.GroupClassSession, timezone string) ([]*do.GroupClassSessionDO, error) {
	if len(source) == 0 {
		return nil, nil
	}
	dest := make([]*do.GroupClassSessionDO, 0, len(source))
	for _, item := range source {
		do, err := ConvertGroupClassSessionPOToDO(item, timezone)
		if err != nil {
			return nil, err
		}
		dest = append(dest, do)
	}
	return dest, nil
}

func ConvertGroupClassSessionUpdateReqToOpt(source *offeringsvcpb.UpdateSessionRequest) *do.GroupClassSessionUpdateOpt {
	opt := &do.GroupClassSessionUpdateOpt{}
	if source.StartTime != nil {
		t := source.GetStartTime().AsTime()
		opt.StartTime = &t
	}
	if source.Duration != nil {
		d := source.GetDuration().AsDuration()
		opt.Duration = &d
	}
	return opt
}
