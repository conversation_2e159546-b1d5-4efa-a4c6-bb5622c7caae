package converter

import (
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func TestCovertMoeGroomingServiceCoatBinding_HappyPath(t *testing.T) {
	companyID := int64(1)
	serviceID := int64(2)
	customizedCoat := []int64{101, 102, 103}
	got := CovertMoeGroomingServiceCoatBinding(companyID, serviceID, customizedCoat)
	expect := &models.MoeGroomingServiceCoatBinding{
		ServiceID:  int32(serviceID),
		CoatIDList: lo.ToPtr(`[101,102,103]`),
		CompanyID:  companyID,
	}
	assert.Equal(t, expect, got)
}

func TestCovertMoeGroomingServiceCoatBinding_EmptyCoatList(t *testing.T) {
	companyID := int64(1)
	serviceID := int64(2)
	var customizedCoat []int64
	got := CovertMoeGroomingServiceCoatBinding(companyID, serviceID, customizedCoat)
	expect := &models.MoeGroomingServiceCoatBinding{
		ServiceID:  int32(serviceID),
		CoatIDList: lo.ToPtr(`[]`),
		CompanyID:  companyID,
	}
	assert.Equal(t, expect, got)
}

func TestCovertMoeGroomingServiceCoatBinding_NilCoatList(t *testing.T) {
	companyID := int64(1)
	serviceID := int64(2)
	var customizedCoat []int64 = nil
	got := CovertMoeGroomingServiceCoatBinding(companyID, serviceID, customizedCoat)
	expect := &models.MoeGroomingServiceCoatBinding{
		ServiceID:  int32(serviceID),
		CoatIDList: lo.ToPtr(`[]`),
		CompanyID:  companyID,
	}
	assert.Equal(t, expect, got)
}

func TestConvertMoeGroomingServiceLocation_HappyPath(t *testing.T) {
	companyID := int64(1)
	serviceID := int64(2)
	overrideRule := &do.BusinessOverrideRule{
		BusinessId:  3,
		Price:       lo.ToPtr(100.0),
		Duration:    lo.ToPtr(int32(60)),
		TaxID:       lo.ToPtr(int64(4)),
		MaxDuration: lo.ToPtr(int32(120)),
	}
	got := ConvertMoeGroomingServiceLocation(companyID, serviceID, overrideRule)
	expect := &models.MoeGroomingServiceLocation{
		CompanyID:   companyID,
		BusinessID:  int32(overrideRule.BusinessId),
		ServiceID:   int32(serviceID),
		IsDeleted:   lo.ToPtr(false),
		Price:       overrideRule.Price,
		Duration:    overrideRule.Duration,
		TaxID:       lo.ToPtr(int32(*overrideRule.TaxID)),
		MaxDuration: overrideRule.MaxDuration,
	}
	assert.Equal(t, expect, got)
}

func TestConvertMoeGroomingServiceLocation_NilFields(t *testing.T) {
	companyID := int64(1)
	serviceID := int64(2)
	overrideRule := &do.BusinessOverrideRule{
		BusinessId:  3,
		Price:       nil,
		Duration:    nil,
		TaxID:       nil,
		MaxDuration: nil,
	}
	got := ConvertMoeGroomingServiceLocation(companyID, serviceID, overrideRule)
	expect := &models.MoeGroomingServiceLocation{
		CompanyID:  companyID,
		BusinessID: int32(overrideRule.BusinessId),
		ServiceID:  int32(serviceID),
		IsDeleted:  lo.ToPtr(false),
	}
	assert.Equal(t, expect, got)
}

func TestConvertMoeAutoRollover_HappyPath(t *testing.T) {
	serviceId := int64(1)
	autoRolloverRule := do.AutoRolloverRule{
		Enabled:         true,
		AfterMinute:     30,
		TargetServiceId: 2,
	}
	got := ConvertMoeAutoRollover(serviceId, autoRolloverRule)
	expect := &models.MoeAutoRollover{
		ServiceID:       serviceId,
		Enabled:         lo.ToPtr(true),
		AfterMinute:     30,
		TargetServiceID: 2,
	}
	assert.Equal(t, expect, got)
}
