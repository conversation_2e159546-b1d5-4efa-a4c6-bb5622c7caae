package converter

import (
	"testing"

	"github.com/lib/pq"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func TestConvertEvaluationDefToDO_HappyPath(t *testing.T) {
	source := &offeringModelsV1.EvaluationDef{
		Name:                    "Evaluation Name",
		AvailableForAllBusiness: true,
		AvailableBusinessIds:    []int64{1, 2, 3},
		ServiceItemTypes:        []offeringModelsV1.ServiceItemType{offeringModelsV1.ServiceItemType_BOARDING, offeringModelsV1.ServiceItemType_DAYCARE},
		Duration:                60,
		ColorCode:               "#FFFFFF",
		IsActive:                true,
		Description:             "Description",
		AliasForOnlineBooking:   lo.ToPtr("Alias"),
		Price:                   lo.ToPtr(100.0),
		LodgingFilter:           false,
		CustomizedLodgingIds:    []int64{},
		IsAllStaff:              lo.ToPtr(false),
		AllowedStaffList:        []int64{1, 2, 3},
		AllowStaffAutoAssign:    lo.ToPtr(true),
	}
	got := ConvertEvaluationDefToDO(source)
	expect := &do.EvaluationDO{
		Name:                    "Evaluation Name",
		AvailableForAllBusiness: true,
		AvailableBusinessIds:    []int64{1, 2, 3},
		ServiceItemTypes:        []offeringModelsV1.ServiceItemType{offeringModelsV1.ServiceItemType_BOARDING, offeringModelsV1.ServiceItemType_DAYCARE},
		Duration:                60,
		ColorCode:               "#FFFFFF",
		IsActive:                true,
		Description:             "Description",
		OnlineBookingAlias:      lo.ToPtr("Alias"),
		Price:                   100.0,
		LodgingFilter:           false,
		CustomizedLodgingIds:    []int64{},
		IsAllStaff:              false,
		AllowedStaffList:        []int64{1, 2, 3},
		AllowStaffAutoAssign:    true,
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDefToDO_NoLodgingFilter(t *testing.T) {
	source := &offeringModelsV1.EvaluationDef{
		LodgingFilter:        false,
		CustomizedLodgingIds: []int64{4, 5, 6},
	}
	got := ConvertEvaluationDefToDO(source)
	expect := &do.EvaluationDO{
		LodgingFilter:        false,
		CustomizedLodgingIds: []int64{4, 5, 6},
		IsAllStaff:           true,
		AllowStaffAutoAssign: false,
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDefToDO_NilCustomizedLodgingIds(t *testing.T) {
	source := &offeringModelsV1.EvaluationDef{
		LodgingFilter:        false,
		CustomizedLodgingIds: nil,
	}
	got := ConvertEvaluationDefToDO(source)
	expect := &do.EvaluationDO{
		LodgingFilter:        false,
		CustomizedLodgingIds: []int64{},
		IsAllStaff:           true,
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDefToDO_CustomizedLodgingIds(t *testing.T) {
	source := &offeringModelsV1.EvaluationDef{
		LodgingFilter:        true,
		CustomizedLodgingIds: []int64{4, 5, 6},
	}
	got := ConvertEvaluationDefToDO(source)
	expect := &do.EvaluationDO{
		LodgingFilter:        true,
		CustomizedLodgingIds: []int64{4, 5, 6},
		IsAllStaff:           true,
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDOToPBModel_HappyPath(t *testing.T) {
	source := &do.EvaluationDO{
		Id:                      1,
		AvailableForAllBusiness: true,
		AvailableBusinessIds:    []int64{1, 2, 3},
		ServiceItemTypes:        []offeringModelsV1.ServiceItemType{offeringModelsV1.ServiceItemType_BOARDING, offeringModelsV1.ServiceItemType_DAYCARE},
		Price:                   100.0,
		Duration:                60,
		ColorCode:               "#FFFFFF",
		Name:                    "Evaluation Name",
		IsActive:                true,
		LodgingFilter:           true,
		CustomizedLodgingIds:    []int64{4, 5, 6},
		Description:             "Description",
		OnlineBookingAlias:      lo.ToPtr("Alias"),
		IsAllStaff:              true,
		AllowedStaffList:        []int64{1, 2, 3},
		AllowStaffAutoAssign:    true,
		BreedFilter:             false,
		PetBreedFilters:         []do.PetBreedFilter{},
	}
	got := ConvertEvaluationDOToPBModel(source)
	expect := &offeringModelsV1.EvaluationModel{
		Id:                      1,
		AvailableForAllBusiness: true,
		AvailableBusinessIds:    []int64{1, 2, 3},
		ServiceItemTypes:        []offeringModelsV1.ServiceItemType{offeringModelsV1.ServiceItemType_BOARDING, offeringModelsV1.ServiceItemType_DAYCARE},
		Price:                   100.0,
		Duration:                60,
		ColorCode:               "#FFFFFF",
		Name:                    "Evaluation Name",
		IsActive:                true,
		LodgingFilter:           true,
		CustomizedLodgingIds:    []int64{4, 5, 6},
		Description:             "Description",
		AliasForOnlineBooking:   "Alias",
		IsAllStaff:              true,
		AllowedStaffList:        []int64{1, 2, 3},
		AllowStaffAutoAssign:    true,
		BreedFilter:             false,
		BreedFilters:            []*offeringModelsV1.PetBreedFilter{},
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDOToPBModel_NilOnlineBookingAlias(t *testing.T) {
	source := &do.EvaluationDO{
		Id:                      1,
		AvailableForAllBusiness: true,
		AvailableBusinessIds:    []int64{1, 2, 3},
		ServiceItemTypes:        []offeringModelsV1.ServiceItemType{offeringModelsV1.ServiceItemType_BOARDING, offeringModelsV1.ServiceItemType_DAYCARE},
		Price:                   100.0,
		Duration:                60,
		ColorCode:               "#FFFFFF",
		Name:                    "Evaluation Name",
		IsActive:                true,
		LodgingFilter:           true,
		CustomizedLodgingIds:    []int64{4, 5, 6},
		Description:             "Description",
		OnlineBookingAlias:      nil,
		BreedFilter:             false,
		PetBreedFilters:         []do.PetBreedFilter{},
	}
	got := ConvertEvaluationDOToPBModel(source)
	expect := &offeringModelsV1.EvaluationModel{
		Id:                      1,
		AvailableForAllBusiness: true,
		AvailableBusinessIds:    []int64{1, 2, 3},
		ServiceItemTypes:        []offeringModelsV1.ServiceItemType{offeringModelsV1.ServiceItemType_BOARDING, offeringModelsV1.ServiceItemType_DAYCARE},
		Price:                   100.0,
		Duration:                60,
		ColorCode:               "#FFFFFF",
		Name:                    "Evaluation Name",
		IsActive:                true,
		LodgingFilter:           true,
		CustomizedLodgingIds:    []int64{4, 5, 6},
		Description:             "Description",
		AliasForOnlineBooking:   "Evaluation Name",
		BreedFilter:             false,
		BreedFilters:            []*offeringModelsV1.PetBreedFilter{},
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDOToBriefView_HappyPath(t *testing.T) {
	source := &do.EvaluationDO{
		Id:                   1,
		Price:                100.0,
		Duration:             60,
		ColorCode:            "#FFFFFF",
		Name:                 "Evaluation Name",
		Description:          "Description",
		OnlineBookingAlias:   lo.ToPtr("Alias"),
		IsAllStaff:           true,
		AllowedStaffList:     []int64{1, 2, 3},
		AllowStaffAutoAssign: true,
		BreedFilter:          false,
		PetBreedFilters:      []do.PetBreedFilter{},
	}
	got := ConvertEvaluationDOToBriefView(source)
	expect := &offeringModelsV1.EvaluationBriefView{
		Id:                    1,
		Price:                 100.0,
		Duration:              60,
		ColorCode:             "#FFFFFF",
		Name:                  "Evaluation Name",
		Description:           "Description",
		AliasForOnlineBooking: "Alias",
		IsAllStaff:            true,
		AllowedStaffList:      []int64{1, 2, 3},
		AllowStaffAutoAssign:  true,
		BreedFilter:           false,
		BreedFilters:          []*offeringModelsV1.PetBreedFilter{},
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDOToBriefView_NilOnlineBookingAlias(t *testing.T) {
	source := &do.EvaluationDO{
		Id:                 1,
		Price:              100.0,
		Duration:           60,
		ColorCode:          "#FFFFFF",
		Name:               "Evaluation Name",
		Description:        "Description",
		OnlineBookingAlias: nil,
		BreedFilter:        false,
		PetBreedFilters:    []do.PetBreedFilter{},
	}
	got := ConvertEvaluationDOToBriefView(source)
	expect := &offeringModelsV1.EvaluationBriefView{
		Id:                    1,
		Price:                 100.0,
		Duration:              60,
		ColorCode:             "#FFFFFF",
		Name:                  "Evaluation Name",
		Description:           "Description",
		AliasForOnlineBooking: "Evaluation Name",
		BreedFilter:           false,
		BreedFilters:          []*offeringModelsV1.PetBreedFilter{},
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDOToDBModel_HappyPath(t *testing.T) {
	source := &do.EvaluationDO{
		Id:                      1,
		Name:                    "Evaluation Name",
		CompanyId:               123,
		AvailableForAllBusiness: true,
		AvailableBusinessIds:    []int64{1, 2, 3},
		ServiceItemTypes:        []offeringModelsV1.ServiceItemType{offeringModelsV1.ServiceItemType_BOARDING, offeringModelsV1.ServiceItemType_DAYCARE},
		Price:                   100.0,
		Duration:                60,
		ColorCode:               "#FFFFFF",
		IsActive:                true,
		LodgingFilter:           true,
		CustomizedLodgingIds:    []int64{4, 5, 6},
		Description:             "Description",
		OnlineBookingAlias:      lo.ToPtr("Alias"),
		IsAllStaff:              true,
		AllowedStaffList:        []int64{1, 2, 3},
		AllowStaffAutoAssign:    true,
	}
	got := ConvertEvaluationDOToDBModel(source)
	expect := models.Evaluation{
		ID:                      1,
		Name:                    "Evaluation Name",
		CompanyID:               123,
		AvailableForAllBusiness: lo.ToPtr(true),
		AvailableBusinessIDList: []int64{1, 2, 3},
		ServiceItemTypeList:     pq.Int32Array{int32(offeringModelsV1.ServiceItemType_BOARDING), int32(offeringModelsV1.ServiceItemType_DAYCARE)},
		Price:                   &source.Price,
		Duration:                &source.Duration,
		ColorCode:               &source.ColorCode,
		IsActive:                true,
		LodgingFilter:           true,
		AllowedLodgingList:      []int64{4, 5, 6},
		Description:             "Description",
		OnlineBookingAlias:      lo.ToPtr("Alias"),
		IsAllStaff:              lo.ToPtr(true),
		AllowedStaffList:        []int64{1, 2, 3},
		AllowStaffAutoAssign:    true,
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDOToDBModel_EmptyFields(t *testing.T) {
	availableForAllBusiness := false
	price := 0.0
	duration := int32(0)
	colorCode := ""
	isAllStaff := false

	source := &do.EvaluationDO{
		AvailableForAllBusiness: availableForAllBusiness,
		Price:                   price,
		Duration:                duration,
		ColorCode:               colorCode,
	}
	got := ConvertEvaluationDOToDBModel(source)
	expect := models.Evaluation{
		AvailableForAllBusiness: lo.ToPtr(availableForAllBusiness),
		Price:                   lo.ToPtr(price),
		Duration:                lo.ToPtr(duration),
		ColorCode:               lo.ToPtr(colorCode),
		IsAllStaff:              lo.ToPtr(isAllStaff),
		AllowedStaffList:        []int64{},
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDBModelToDO_HappyPath(t *testing.T) {
	source := &models.Evaluation{
		ID:                      1,
		Name:                    "Evaluation Name",
		CompanyID:               123,
		AvailableForAllBusiness: lo.ToPtr(true),
		AvailableBusinessIDList: pq.Int64Array{1, 2, 3},
		ServiceItemTypeList:     pq.Int32Array{int32(offeringModelsV1.ServiceItemType_BOARDING), int32(offeringModelsV1.ServiceItemType_DAYCARE)},
		Price:                   lo.ToPtr(100.0),
		Duration:                lo.ToPtr(int32(60)),
		ColorCode:               lo.ToPtr("#FFFFFF"),
		IsActive:                true,
		LodgingFilter:           true,
		AllowedLodgingList:      pq.Int64Array{4, 5, 6},
		Description:             "Description",
		OnlineBookingAlias:      lo.ToPtr("Alias"),
		IsAllStaff:              lo.ToPtr(true),
		AllowedStaffList:        pq.Int64Array{1, 2, 3},
		AllowStaffAutoAssign:    true,
		Source:                  offeringModelsV1.EvaluationModel_ENTERPRISE_HUB,
	}
	got := ConvertEvaluationDBModelToDO(source)
	expect := &do.EvaluationDO{
		Id:                      1,
		Name:                    "Evaluation Name",
		CompanyId:               123,
		AvailableForAllBusiness: true,
		AvailableBusinessIds:    []int64{1, 2, 3},
		ServiceItemTypes:        []offeringModelsV1.ServiceItemType{offeringModelsV1.ServiceItemType_BOARDING, offeringModelsV1.ServiceItemType_DAYCARE},
		Price:                   100.0,
		Duration:                60,
		ColorCode:               "#FFFFFF",
		IsActive:                true,
		LodgingFilter:           true,
		CustomizedLodgingIds:    []int64{4, 5, 6},
		Description:             "Description",
		OnlineBookingAlias:      lo.ToPtr("Alias"),
		IsAllStaff:              true,
		AllowedStaffList:        []int64{1, 2, 3},
		AllowStaffAutoAssign:    true,
		Source:                  offeringModelsV1.EvaluationModel_ENTERPRISE_HUB.Enum(),
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDBModelToDO_NilFields(t *testing.T) {
	source := &models.Evaluation{
		AvailableForAllBusiness: lo.ToPtr(false),
		AvailableBusinessIDList: nil,
		ServiceItemTypeList:     nil,
		Price:                   nil,
		Duration:                nil,
		ColorCode:               nil,
		OnlineBookingAlias:      nil,
	}
	got := ConvertEvaluationDBModelToDO(source)
	expect := &do.EvaluationDO{
		AvailableForAllBusiness: false,
		AvailableBusinessIds:    nil,
		ServiceItemTypes:        nil,
		Price:                   0,
		Duration:                0,
		ColorCode:               "",
		OnlineBookingAlias:      nil,
		Source:                  offeringModelsV1.EvaluationModel_SOURCE_UNSPECIFIED.Enum(),
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDOToUpdateOpt_HappyPath(t *testing.T) {
	source := &do.EvaluationDO{
		Name:                    "Evaluation Name",
		AvailableForAllBusiness: true,
		AvailableBusinessIds:    []int64{1, 2, 3},
		ServiceItemTypes:        []offeringModelsV1.ServiceItemType{offeringModelsV1.ServiceItemType_BOARDING, offeringModelsV1.ServiceItemType_DAYCARE},
		Price:                   100.0,
		Duration:                60,
		ColorCode:               "#FFFFFF",
		IsActive:                true,
		LodgingFilter:           true,
		CustomizedLodgingIds:    []int64{4, 5, 6},
		Description:             "Description",
		OnlineBookingAlias:      lo.ToPtr("Alias"),
		IsAllStaff:              true,
		AllowedStaffList:        []int64{1, 2, 3},
		AllowStaffAutoAssign:    true,
		IsResettable:            true,
		ResetIntervalDays:       30,
	}
	got := ConvertEvaluationDOToUpdateOpt(source)
	expect := models.EvaluationUpdateOpt{
		Name:                    lo.ToPtr("Evaluation Name"),
		AvailableForAllBusiness: lo.ToPtr(true),
		AvailableBusinessIdList: lo.ToPtr(pq.Int64Array{1, 2, 3}),
		ServiceItemTypeList:     lo.ToPtr(pq.Int32Array{int32(offeringModelsV1.ServiceItemType_BOARDING), int32(offeringModelsV1.ServiceItemType_DAYCARE)}),
		Price:                   lo.ToPtr(100.0),
		Duration:                lo.ToPtr(int32(60)),
		ColorCode:               lo.ToPtr("#FFFFFF"),
		IsActive:                lo.ToPtr(true),
		LodgingFilter:           lo.ToPtr(true),
		CustomizedLodgingIds:    lo.ToPtr(pq.Int64Array{4, 5, 6}),
		Description:             lo.ToPtr("Description"),
		OnlineBookingAlias:      lo.ToPtr("Alias"),
		IsAllStaff:              lo.ToPtr(true),
		AllowedStaffList:        lo.ToPtr(pq.Int64Array{1, 2, 3}),
		AllowStaffAutoAssign:    lo.ToPtr(true),
		IsResettable:            lo.ToPtr(true),
		ResetIntervalDays:       lo.ToPtr(int32(30)),
		TaxId:                   lo.ToPtr(int64(0)),
	}
	assert.Equal(t, expect, got)
}

func TestConvertEvaluationDOToUpdateOpt_EmptyFields(t *testing.T) {
	source := &do.EvaluationDO{
		Name:                    "",
		AvailableForAllBusiness: false,
		AvailableBusinessIds:    []int64{},
		ServiceItemTypes:        []offeringModelsV1.ServiceItemType{},
		Price:                   0.0,
		Duration:                0,
		ColorCode:               "",
		IsActive:                false,
		LodgingFilter:           false,
		CustomizedLodgingIds:    []int64{},
		Description:             "",
		OnlineBookingAlias:      nil,
		IsAllStaff:              false,
		AllowedStaffList:        []int64{},
		AllowStaffAutoAssign:    false,

		IsResettable:      false,
		ResetIntervalDays: 0,
	}
	got := ConvertEvaluationDOToUpdateOpt(source)
	expect := models.EvaluationUpdateOpt{
		Name:                    lo.ToPtr(""),
		AvailableForAllBusiness: lo.ToPtr(false),
		AvailableBusinessIdList: lo.ToPtr(pq.Int64Array{}),
		ServiceItemTypeList:     lo.ToPtr(pq.Int32Array{}),
		Price:                   lo.ToPtr(0.0),
		Duration:                lo.ToPtr(int32(0)),
		ColorCode:               lo.ToPtr(""),
		IsActive:                lo.ToPtr(false),
		LodgingFilter:           lo.ToPtr(false),
		CustomizedLodgingIds:    lo.ToPtr(pq.Int64Array{}),
		Description:             lo.ToPtr(""),
		OnlineBookingAlias:      nil,
		IsAllStaff:              lo.ToPtr(false),
		AllowedStaffList:        lo.ToPtr(pq.Int64Array{}),
		AllowStaffAutoAssign:    lo.ToPtr(false),
		IsResettable:            lo.ToPtr(false),
		ResetIntervalDays:       lo.ToPtr(int32(0)),
		TaxId:                   lo.ToPtr(int64(0)),
	}
	assert.Equal(t, expect, got)
}
