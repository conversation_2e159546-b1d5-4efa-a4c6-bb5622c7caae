package converter

import (
	pbModels "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter/impl"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

var lodgingUnitConverter = &impl.LodgingUnitConverterImpl{}

var lodgingTypeConverter = &impl.LodgingTypeConverter{}

func ConvertCreateLodgingUnitRequestToDO(source *v1.CreateLodgingUnitRequest) *do.LodgingUnitDO {
	return lodgingUnitConverter.ConverterCreateRequestToDO(source)
}

func ConvertBatchCreateLodgingUnitRequestToDO(request *v1.BatchCreateLodgingUnitRequest) []*do.LodgingUnitDO {
	if request == nil {
		return nil
	}
	source := request.LodgingUnitParamsList
	result := make([]*do.LodgingUnitDO, len(source))
	for i := 0; i < len(source); i++ {
		result[i] = ConvertCreateLodgingUnitRequestToDO(source[i])
	}
	return result
}

func ConvertUpdateLodgingUnitRequestToUpdateOpt(source *v1.UpdateLodgingUnitRequest) *do.LodgingUnitUpdateOpt {
	return lodgingUnitConverter.ConverterUpdateRequestToUpdateOpt(source)
}

func ConvertLodgingUnitDOToPB(source *do.LodgingUnitDO) *pbModels.LodgingUnitModel {
	return lodgingUnitConverter.ConverterDoToPB(source)
}

func ConvertLodgingUnitDoListToPB(source []*do.LodgingUnitDO) []*pbModels.LodgingUnitModel {
	if source == nil {
		return nil
	}
	result := make([]*pbModels.LodgingUnitModel, len(source))
	for i := 0; i < len(source); i++ {
		result[i] = ConvertLodgingUnitDOToPB(source[i])
	}
	return result
}

func ConvertLodgingUnitDOToPO(source *do.LodgingUnitDO) *models.LodgingUnit {
	return lodgingUnitConverter.ConverterDOToPO(source)
}

func ConvertLodgingUnitDOListToPO(source []*do.LodgingUnitDO) []*models.LodgingUnit {
	if source == nil {
		return nil
	}
	result := make([]*models.LodgingUnit, len(source))
	for i := 0; i < len(source); i++ {
		result[i] = ConvertLodgingUnitDOToPO(source[i])
	}
	return result
}

func ConvertLodgingUnitPOToDO(source *models.LodgingUnit) *do.LodgingUnitDO {
	return lodgingUnitConverter.ConverterPOToDO(source)
}

func ConvertLodgingUnitPOListToDO(source []*models.LodgingUnit) []*do.LodgingUnitDO {
	if source == nil {
		return nil
	}
	result := make([]*do.LodgingUnitDO, len(source))
	for i := 0; i < len(source); i++ {
		result[i] = ConvertLodgingUnitPOToDO(source[i])
	}
	return result
}

func ConvertLodgingUnitUpdateOptDoToModel(source *do.LodgingUnitUpdateOpt) *models.LodgingUnitUpdateOpt {
	return lodgingUnitConverter.ConvertUpdateOptDoToModel(source)
}

func ConvertLodgingUnitWhereOptDoToModel(source *do.LodgingUnitWhereOpt) *models.LodgingUnitWhereOpt {
	result := &models.LodgingUnitWhereOpt{
		ID:            source.Id,
		BusinessID:    source.BusinessID,
		LodgingTypeID: source.LodgingTypeID,
	}
	if source.IDIn != nil {
		result.IDIn = &source.IDIn
	}
	if source.LodgingTypeIDIn != nil {
		result.LodgingTypeIDIn = &source.LodgingTypeIDIn
	}
	if source.CameraIDIn != nil {
		result.CameraIdIDIn = &source.CameraIDIn
	}
	return result
}

func ConvertLodgingTypeUpdateByIdToUpdateOpt(source *do.LodgingTypeUpdateByIDOpt) *do.LodgingTypeUpdateOpt {
	return lodgingTypeConverter.ConvertUpdateByIdToUpdateOpt(source)
}

func ConvertLodgingUnitUpdateByIdToUpdateOpt(source *do.LodgingUnitUpdateByIDOpt) *do.LodgingUnitUpdateOpt {
	return lodgingUnitConverter.ConvertUpdateByIdToUpdateOpt(source)
}
