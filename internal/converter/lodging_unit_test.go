package converter

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func TestConvertCreateLodgingUnitRequestToDO(t *testing.T) {
	source := &v1.CreateLodgingUnitRequest{
		CompanyId:     1,
		BusinessId:    2,
		TokenStaffId:  3,
		Name:          "name",
		LodgingTypeId: 4,
	}
	got := ConvertCreateLodgingUnitRequestToDO(source)
	assert.Equal(t, source.CompanyId, got.CompanyID)
	assert.Equal(t, source.BusinessId, got.BusinessID)
	assert.Equal(t, source.TokenStaffId, got.CreatedBy)
	assert.Equal(t, source.Name, got.Name)
	assert.Equal(t, source.LodgingTypeId, got.LodgingTypeID)
}

func TestConvertUpdateLodgingUnitRequestToUpdateOpt(t *testing.T) {
	source := &v1.UpdateLodgingUnitRequest{
		TokenStaffId: 3,
		Name:         proto.String("name"),
	}
	got := ConvertUpdateLodgingUnitRequestToUpdateOpt(source)
	assert.Equal(t, source.TokenStaffId, *got.UpdatedBy)
	assert.Equal(t, *source.Name, *got.Name)
}

func TestConvertLodgingUnitDOToPB(t *testing.T) {
	source := &do.LodgingUnitDO{
		ID:            1,
		CompanyID:     2,
		BusinessID:    3,
		LodgingTypeID: 4,
		Name:          "name",
	}
	got := ConvertLodgingUnitDOToPB(source)
	assert.Equal(t, source.ID, got.Id)
	assert.Equal(t, source.CompanyID, got.CompanyId)
	assert.Equal(t, source.BusinessID, got.BusinessId)
	assert.Equal(t, source.LodgingTypeID, got.LodgingTypeId)
	assert.Equal(t, source.Name, got.Name)
}

func TestConvertLodgingUnitDOToPO(t *testing.T) {
	source := &do.LodgingUnitDO{
		CompanyID:     2,
		BusinessID:    3,
		LodgingTypeID: 4,
		Name:          "name",
		CreatedBy:     5,
	}
	got := ConvertLodgingUnitDOToPO(source)
	assert.Equal(t, source.CompanyID, got.CompanyID)
	assert.Equal(t, source.BusinessID, got.BusinessID)
	assert.Equal(t, source.LodgingTypeID, got.LodgingTypeID)
	assert.Equal(t, source.Name, got.Name)
	assert.Equal(t, source.CreatedBy, got.CreatedBy)
}

func TestConvertLodgingUnitPOToDO(t *testing.T) {
	source := &models.LodgingUnit{
		ID:            1,
		CompanyID:     2,
		BusinessID:    3,
		LodgingTypeID: 4,
		Name:          "name",
	}
	got := ConvertLodgingUnitPOToDO(source)
	assert.Equal(t, source.ID, got.ID)
	assert.Equal(t, source.CompanyID, got.CompanyID)
	assert.Equal(t, source.BusinessID, got.BusinessID)
	assert.Equal(t, source.LodgingTypeID, got.LodgingTypeID)
	assert.Equal(t, source.Name, got.Name)
}

func TestConvertLodgingUnitUpdateOptDoToModel(t *testing.T) {
	source := &do.LodgingUnitUpdateOpt{
		Name:      proto.String("name"),
		UpdatedBy: proto.Int64(3),
	}
	got := ConvertLodgingUnitUpdateOptDoToModel(source)
	assert.Equal(t, source.Name, got.Name)
	assert.Equal(t, source.UpdatedBy, got.UpdatedBy)
}

func TestConvertLodgingUnitWhereOptDoToModel(t *testing.T) {
	source := &do.LodgingUnitWhereOpt{
		Id:              proto.Int64(1),
		IDIn:            []int64{2, 3},
		BusinessID:      proto.Int64(4),
		LodgingTypeIDIn: []int64{5, 6},
		LodgingTypeID:   proto.Int64(7),
	}
	got := ConvertLodgingUnitWhereOptDoToModel(source)
	assert.Equal(t, *source.Id, *got.ID)
	assert.Equal(t, source.IDIn, *got.IDIn)
	assert.Equal(t, source.BusinessID, got.BusinessID)
	assert.Equal(t, source.LodgingTypeIDIn, *got.LodgingTypeIDIn)
	assert.Equal(t, *source.LodgingTypeID, *got.LodgingTypeID)
}
