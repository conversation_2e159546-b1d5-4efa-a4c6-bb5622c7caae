package converter

import (
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func VaccineRequirementDBToDO(vaccineRequirementDB *models.VaccineRequirement) *do.VaccineRequirement {
	if vaccineRequirementDB == nil {
		return nil
	}
	return &do.VaccineRequirement{
		Id:              vaccineRequirementDB.ID,
		ServiceItemType: vaccineRequirementDB.ServiceItemType,
		VaccineID:       vaccineRequirementDB.VaccineID,
	}
}

func VaccineRequirementDOToPB(source *do.VaccineRequirement) *offeringpb.ServiceVaccineRequirementModel {
	if source == nil {
		return nil
	}
	return &offeringpb.ServiceVaccineRequirementModel{
		Id:              source.Id,
		ServiceItemType: source.ServiceItemType,
		VaccineId:       source.VaccineID,
	}
}
