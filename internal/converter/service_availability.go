package converter

import (
	"google.golang.org/protobuf/proto"

	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func ConvertAvailableStaffRuleDOToPB(source do.ServiceStaffAvailabilityRule) *offeringsvcpb.ListAvailableStaffIdResponse_StaffIds {
	return &offeringsvcpb.ListAvailableStaffIdResponse_StaffIds{
		IsAllStaff: source.AvailableForAllStaff,
		StaffIds:   source.StaffIds,
	}
}

func ConvertPetCodeAvailabilityToDB(serviceId int64, source *do.PetCodeAvailability) *models.MoeServicePetCodeFilter {
	if source == nil {
		return &models.MoeServicePetCodeFilter{
			ServiceID:    serviceId,
			IsWhitelist:  nil,
			IsAllPetCode: nil,
			PetCodeList:  nil,
		}
	}

	return &models.MoeServicePetCodeFilter{
		ServiceID:    serviceId,
		IsWhitelist:  proto.Bool(source.IsWhiteList),
		IsAllPetCode: proto.Bool(source.IsAllPetCode),
		PetCodeList:  source.PetCodeIds,
	}
}

func ConvertPetCodeAvailabilityFromDB(source *models.MoeServicePetCodeFilter) *do.PetCodeAvailability {
	if source == nil {
		return &do.PetCodeAvailability{
			IsWhiteList:  true,
			IsAllPetCode: true,
			PetCodeIds:   nil,
		}
	}

	return &do.PetCodeAvailability{
		IsWhiteList:  *source.IsWhitelist,
		IsAllPetCode: *source.IsAllPetCode,
		PetCodeIds:   source.PetCodeList,
	}
}
