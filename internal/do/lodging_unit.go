package do

import "time"

type LodgingUnitDO struct {
	ID            int64
	CompanyID     int64
	BusinessID    int64
	LodgingTypeID int64
	Name          string
	CreatedBy     int64
	CameraId      int64
	Sort          int32
}

type LodgingUnitUpdateOpt struct {
	Name      *string
	UpdatedBy *int64
	CameraId  *int64
	UpdatedAt *time.Time
	Sort      *int32
}

type LodgingUnitUpdateByIDOpt struct {
	ID        int64
	Name      *string
	UpdatedBy *int64
	CameraId  *int64
	UpdatedAt *time.Time
	Sort      *int32
}

type LodgingUnitWhereOpt struct {
	Id                  *int64
	IDIn                []int64 // nil 时，不过滤；空数组时，过滤掉所有
	BusinessID          *int64
	LodgingTypeIDIn     []int64 // nil 时，不过滤；空数组时，过滤掉所有
	LodgingTypeID       *int64
	ServiceID           *int64
	EvaluationServiceID *int64
	CameraIDIn          []int64 // nil 时，不过滤；空数组时，过滤掉所有
}
