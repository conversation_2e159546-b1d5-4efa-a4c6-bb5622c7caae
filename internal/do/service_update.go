package do

import (
	"encoding/json"
	"time"

	"google.golang.org/protobuf/proto"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

type ServiceUpdateOpt struct {
	ServiceID                  int64
	Name                       *string
	Description                *string
	CategoryId                 *int64
	ColorCode                  *string
	Inactive                   *bool
	Images                     []string
	Availability               *Availability
	Price                      *float64
	PriceUnit                  *offeringpb.ServicePriceUnit
	TaxId                      *int64
	Duration                   *int32
	Commission                 *Commission
	AddonAvailability          *AddonAvailability
	MaxDuration                *int32
	AutoRolloverRule           *AutoRolloverRule
	BundleServiceIds           []int64
	Source                     *offeringpb.ServiceModel_Source
	NumSessions                *int32
	DurationSessionMin         *int32
	Capacity                   *int32
	IsRequirePrerequisiteClass *bool
	PrerequisiteClassIds       []int64
	IsEvaluationRequired       *bool
	IsEvaluationRequiredForOb  *bool
	EvaluationID               *int64
	AdditionalServiceRule      *offeringpb.AdditionalServiceRule
}

func (s *ServiceUpdateOpt) NeedBreedFilter() bool {
	return s.Availability != nil && *s.Availability.BreedFilter
}

func (s *ServiceUpdateOpt) NeedCoatFilter() bool {
	return s.Availability != nil && *s.Availability.CoatFilter
}

func (s *ServiceUpdateOpt) NeedPetSizeFilter() bool {
	return s.Availability != nil && *s.Availability.PetSizeFilter
}

func (s *ServiceUpdateOpt) NeedLocationOverride() bool {
	return s.Availability != nil && s.Availability.LocationOverrideList != nil
}

func (s *ServiceUpdateOpt) ToModel() models.ServiceUpdateOpt {
	serviceUpdateOpt := models.ServiceUpdateOpt{
		Name:                       s.Name,
		CategoryID:                 s.CategoryId,
		Description:                s.Description,
		TaxID:                      s.TaxId,
		Price:                      s.Price,
		Duration:                   s.Duration,
		Inactive:                   s.Inactive,
		ColorCode:                  s.ColorCode,
		UpdateTime:                 time.Now().Unix(),
		AllowedLodgingList:         nil,
		MaxDuration:                s.MaxDuration,
		NumSessions:                s.NumSessions,
		DurationSessionMin:         s.DurationSessionMin,
		Capacity:                   s.Capacity,
		IsRequirePrerequisiteClass: s.IsRequirePrerequisiteClass,
		IsEvaluationRequired:       s.IsEvaluationRequired,
		IsEvaluationRequiredForOb:  s.IsEvaluationRequiredForOb,
		EvaluationID:               s.EvaluationID,
	}

	if s.AdditionalServiceRule != nil {
		additionalServiceRuleJSON, _ := json.Marshal(s.AdditionalServiceRule)
		serviceUpdateOpt.AdditionalServiceRule = proto.String(string(additionalServiceRuleJSON))
	}

	if s.PrerequisiteClassIds != nil {
		prerequisiteClassIdsJSON, _ := json.Marshal(s.PrerequisiteClassIds)
		serviceUpdateOpt.PrerequisiteClassIds = proto.String(string(prerequisiteClassIdsJSON))
	}

	if s.PriceUnit != nil {
		serviceUpdateOpt.PriceUnit = proto.Int32(int32(*s.PriceUnit))
	}

	if s.Availability != nil {
		serviceUpdateOpt.BreedFilter = s.Availability.BreedFilter
		serviceUpdateOpt.WeightFilter = s.Availability.WeightFilter
		serviceUpdateOpt.WeightDownLimit = s.Availability.AvailableMinWeight
		serviceUpdateOpt.WeightUpLimit = s.Availability.AvailableMaxWeight
		serviceUpdateOpt.CoatFilter = s.Availability.CoatFilter
		serviceUpdateOpt.IsAllLocation = s.Availability.IsAllLocation
		serviceUpdateOpt.IsAllStaff = s.Availability.AvailableForAllStaff
		serviceUpdateOpt.RequireDedicatedStaff = s.Availability.RequireDedicatedStaff
		serviceUpdateOpt.RequiredDedicatedLodging = s.Availability.RequireDedicatedLodging
		serviceUpdateOpt.LodgingFilter = s.Availability.LodgingFilter
		serviceUpdateOpt.PetSizeFilter = s.Availability.PetSizeFilter
	}

	if s.AddonAvailability != nil {
		serviceUpdateOpt.ServiceFilter = s.AddonAvailability.ServiceFilter
	}

	if s.Commission != nil {
		serviceUpdateOpt.AddToCommission = s.Commission.AddToCommissionBase
		serviceUpdateOpt.CanTip = s.Commission.CanTip
	}

	if s.Images != nil {
		imagesJSON, _ := json.Marshal(s.Images)
		serviceUpdateOpt.Images = proto.String(string(imagesJSON))
	} else {
		serviceUpdateOpt.Images = proto.String("[]")
	}

	if s.Availability != nil && s.Availability.CustomizedLodgings != nil {
		lodgingListJSON, _ := json.Marshal(s.Availability.CustomizedLodgings)
		serviceUpdateOpt.AllowedLodgingList = proto.String(string(lodgingListJSON))
	}

	if s.NeedPetSizeFilter() {
		petSizeListJSON, _ := json.Marshal(s.Availability.CustomizedPetSizes)
		serviceUpdateOpt.AllowedPetSizeList = proto.String(string(petSizeListJSON))
	}

	if s.PriceUnit != nil {
		serviceUpdateOpt.PriceUnit = proto.Int32(int32(*s.PriceUnit))
	}
	if s.Source != nil {
		serviceUpdateOpt.Source = proto.Int32(int32(*s.Source))
	}

	return serviceUpdateOpt
}

type ImageListUpdateOpt struct {
	Values []string
}

type AvailabilityUpdateOpt struct {
	// location availability
	IsAllLocation        *bool
	LocationOverrideList []LocationOverrideRuleUpdateOpt

	// lodging availability
	RequireDedicatedLodging *bool
	LodgingFilter           *bool
	CustomizedLodgings      []int64

	// staff availability
	RequireDedicatedStaff *bool
	//AvailableForAllStaff  bool // 暂时没有
	//AvailableStaffIds     []int64 // 暂时没有

	// pet type & breed
	BreedFilter     *bool
	CustomizedBreed []PetTypeAndBreedAvailabilityRuleUpdateOpt

	// pet size availability
	WeightFilter       *bool
	PetSizeFilter      *bool
	CustomizedPetSizes []int64
	AvailableMinWeight *float64 // deprecated field, should use pet size first
	AvailableMaxWeight *float64 // deprecated field, should use pet size first

	// coat type availability
	CoatFilter     *bool
	CustomizedCoat []int64
}

type PetTypeAndBreedAvailabilityRuleUpdateOpt struct {
	PetTypeID int64
	IsAll     *bool
	Breeds    []string
}

type CommissionUpdateOpt struct {
	AddToCommissionBase *bool
	CanTip              *bool
}

type LocationOverrideRuleUpdateOpt struct {
	BusinessId int64
	Price      *float64
	TaxId      *int64
	Duration   *int32
}

type AddonAvailabilityUpdateOpt struct {
	// service availability
	ServiceFilter     *bool
	ServiceFilterList []ServiceFilterRuleUpdateOpt
}

type ServiceFilterRuleUpdateOpt struct {
	ServiceItemType         offeringpb.ServiceItemType
	AvailableForAllServices *bool
	AvailableServiceIdList  []int64
}
