package do

import v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

type LodgingTypeDO struct {
	ID                 int64
	CompanyID          int64
	Name               string
	Description        string
	PhotoList          []string
	MaxPetNum          int32
	CreatedBy          int64
	IsDeleted          bool
	AllowedPetSizeList []int64
	PetSizeFilter      bool
	Type               v1.LodgingUnitType
	Sort               int32
	Source             v1.LodgingTypeModel_Source
}

type LodgingTypeUpdateOpt struct {
	Description        *string
	Name               *string
	PhotoList          []string
	MaxPetNum          *int32
	UpdatedBy          *int64
	AllowedPetSizeList []int64
	PetSizeFilter      *bool
	Type               *v1.LodgingUnitType
	Sort               *int32
	Source             *v1.LodgingTypeModel_Source
}

type LodgingTypeUpdateByIDOpt struct {
	ID                 int64
	Description        *string
	Name               *string
	PhotoList          []string
	MaxPetNum          *int32
	UpdatedBy          *int64
	AllowedPetSizeList []int64
	PetSizeFilter      *bool
	Type               *v1.LodgingUnitType
	Sort               *int32
}
