package do

import (
	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
)

type EvaluationDO struct {
	Id                      int64
	Name                    string
	CompanyId               int64
	AvailableForAllBusiness bool
	AvailableBusinessIds    []int64
	ServiceItemTypes        []offeringModelsV1.ServiceItemType
	Price                   float64
	Duration                int32
	ColorCode               string
	IsActive                bool
	LodgingFilter           bool
	CustomizedLodgingIds    []int64
	Description             string
	OnlineBookingAlias      *string
	IsOBAvailable           bool
	IsAllStaff              bool
	AllowedStaffList        []int64
	AllowStaffAutoAssign    bool
	IsResettable            bool
	ResetIntervalDays       int32
	BreedFilter             bool
	PetBreedFilters         []PetBreedFilter
	TaxId                   int64
	Source                  *offeringModelsV1.EvaluationModel_Source
}

type EvaluationFilter struct {
	BusinessId      *int64
	ServiceItemType *offeringModelsV1.ServiceItemType
	FilterByPet     *offeringModelsV1.ServiceFilterByPet
	IncludeInactive bool
}

type EvaluationModelFilter struct {
	Ids          []int64
	CompanyIds   []int64
	IsResettable *bool
	BusinessId   *int64
}

type PetBreedFilter struct {
	PetType    customerpb.PetType
	IsAllBreed bool
	BreedNames []string
}
