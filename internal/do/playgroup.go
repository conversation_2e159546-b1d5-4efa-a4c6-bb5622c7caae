package do

type Play<PERSON>DO struct {
	ID             int64
	CompanyID      int64
	Name           string
	Description    string
	ColorCode      string
	MaxPetCapacity int32
	Sort           int32
	OperateId      int64
}

type PlaygroupSortDO struct {
	CompanyID    int64
	PlaygroupIds []int64
	OperateId    int64
}

type PlaygroupFilterDO struct {
	CompanyID      int64
	PlaygroupIds   []int64
	IncludeDeleted bool
}
