package do

import (
	"time"

	"gorm.io/gorm"
)

type GroupClassSessionDO struct {
	ID         int64
	CompanyID  int64
	BusinessID int64
	InstanceID int64
	StartTime  *time.Time
	Duration   time.Duration
	IsModified bool
	CreatedAt  *time.Time
	UpdatedAt  *time.Time
	DeletedAt  *time.Time
}

type GroupClassSessionFilter struct {
	IDs          []int64
	CompanyID    int64
	InstanceIDs  []int64
	StartTimeMin *time.Time
	StartTimeMax *time.Time
}

func (f *GroupClassSessionFilter) Apply(db *gorm.DB) *gorm.DB {
	if f == nil {
		return db
	}

	if len(f.IDs) > 0 {
		db = db.Where("id IN ?", f.IDs)
	}
	if f.CompanyID > 0 {
		db = db.Where("company_id = ?", f.CompanyID)
	}
	if len(f.InstanceIDs) > 0 {
		db = db.Where("instance_id IN ?", f.InstanceIDs)
	}
	if f.StartTimeMin != nil {
		db = db.Where("start_time >= ?", f.StartTimeMin)
	}
	if f.StartTimeMax != nil {
		db = db.Where("start_time <= ?", f.StartTimeMax)
	}

	return db
}

type GroupClassSessionUpdateOpt struct {
	StartTime *time.Time
	Duration  *time.Duration
}
