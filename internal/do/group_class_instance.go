package do

import (
	"time"

	"github.com/samber/lo"

	"google.golang.org/genproto/googleapis/type/datetime"
	"google.golang.org/genproto/googleapis/type/money"
	"gorm.io/gorm"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
)

type GroupClassInstanceDO struct {
	ID         int64
	CompanyID  int64
	BusinessID int64
	ServiceID  int64
	Name       string
	StaffID    int64
	Price      *money.Money
	TaxID      int64
	StartTime  *time.Time
	Capacity   int64
	Occurrence *offeringpb.GroupClassInstance_Occurrence
	Status     offeringpb.GroupClassInstance_Status
	CreatedAt  *time.Time
	UpdatedAt  *time.Time
}

type GroupClassInstanceCreateOpt struct {
	CompanyID    int64
	BusinessID   int64
	GroupClassID int64
	StaffID      int64
	StartTime    *datetime.DateTime
	Occurrence   *offeringpb.GroupClassInstance_Occurrence
}

type GroupClassInstanceFilter struct {
	IDs          []int64
	CompanyID    int64
	BusinessIDs  []int64
	StaffIDs     []int64
	ServiceIDs   []int64
	Statuses     []offeringpb.GroupClassInstance_Status
	StartTimeMin *time.Time
	StartTimeMax *time.Time
}

func (f *GroupClassInstanceFilter) Apply(db *gorm.DB) *gorm.DB {
	if f == nil {
		return db
	}
	if len(f.IDs) > 0 {
		db = db.Where("id IN ?", f.IDs)
	}
	if f.CompanyID > 0 {
		db = db.Where("company_id = ?", f.CompanyID)
	}
	if len(f.BusinessIDs) > 0 {
		db = db.Where("business_id IN ?", f.BusinessIDs)
	}
	if len(f.StaffIDs) > 0 {
		db = db.Where("staff_id IN ?", f.StaffIDs)
	}
	if len(f.ServiceIDs) > 0 {
		db = db.Where("service_id IN ?", f.ServiceIDs)
	}
	filteredStatuses := lo.Filter(f.Statuses, func(status offeringpb.GroupClassInstance_Status, _ int) bool {
		return status != offeringpb.GroupClassInstance_STATUS_UNSPECIFIED
	})
	if len(filteredStatuses) > 0 {
		db = db.Where("status IN ?", filteredStatuses)
	}

	return db
}

type GroupClassInstanceUpdateOpt struct {
	StaffID    *int64
	StartTime  *datetime.DateTime
	Occurrence *offeringpb.GroupClassInstance_Occurrence
}
