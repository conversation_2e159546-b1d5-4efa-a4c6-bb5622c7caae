package do

import (
	offeringV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
)

type UniqueService interface {
	GetName() string
	GetServiceType() offeringV1.ServiceType
	GetServiceItemType() offeringV1.ServiceItemType
}

type uniqueService struct {
	Name            string
	ServiceType     offeringV1.ServiceType
	ServiceItemType offeringV1.ServiceItemType
}

func (u uniqueService) GetName() string {
	return u.Name
}

func (u uniqueService) GetServiceType() offeringV1.ServiceType {
	return u.ServiceType
}

func (u uniqueService) GetServiceItemType() offeringV1.ServiceItemType {
	return u.ServiceItemType
}

func NewUniqueService(name string, serviceType offeringV1.ServiceType, serviceItemType offeringV1.ServiceItemType) UniqueService {
	return &uniqueService{
		Name:            name,
		ServiceType:     serviceType,
		ServiceItemType: serviceItemType,
	}
}
