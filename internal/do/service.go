package do

import (
	"encoding/json"
	"time"

	"google.golang.org/genproto/googleapis/type/latlng"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	"github.com/MoeGolibrary/go-lib/merror"
	customerModelV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	errorsModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	"github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

type ServiceBrief struct {
	ServiceId                  int64
	CategoryId                 int64
	ServiceType                offeringpb.ServiceType
	ServiceItemType            offeringpb.ServiceItemType
	Name                       string
	Description                string
	Price                      float64
	PriceUnit                  offeringpb.ServicePriceUnit
	Duration                   int32
	TaxId                      int64
	CreateTime                 int64
	UpdateTime                 int64
	IsDeleted                  bool
	Inactive                   bool
	ColorCode                  string
	MaxDuration                int32
	RequireDedicatedStaff      bool
	RequireDedicatedLodging    bool
	LodgingFilter              bool
	CustomizedLodgings         []int64
	Images                     []string
	AvailableForAllStaff       bool
	NumSessions                int32
	DurationSessionMin         int32
	Capacity                   int32
	IsRequirePrerequisiteClass bool
	PrerequisiteClassIds       []int64
	IsEvaluationRequired       bool
	IsEvaluationRequiredForOb  bool
	EvaluationId               int64
	CompanyId                  int64
}

type ServiceDO struct {
	ServiceID                  int64
	Name                       string
	Description                string
	CategoryId                 int64
	ColorCode                  *string
	Inactive                   bool
	Images                     []string
	Availability               Availability
	Price                      float64
	PriceUnit                  offeringpb.ServicePriceUnit
	ServiceType                offeringpb.ServiceType
	TaxId                      int64
	Duration                   *int32
	MaxDuration                *int32
	Commission                 Commission
	ServiceItemType            offeringpb.ServiceItemType
	AddonAvailability          *AddonAvailability
	AutoRolloverRule           *AutoRolloverRule
	CreateTime                 int64
	UpdateTime                 int64
	IsDeleted                  bool
	CompanyId                  int64
	BundleServiceIds           []int64
	Source                     offeringpb.ServiceModel_Source
	NumSessions                int32
	DurationSessionMin         int32
	Capacity                   int32
	IsRequirePrerequisiteClass bool
	PrerequisiteClassIds       []int64
	IsEvaluationRequired       bool
	IsEvaluationRequiredForOb  bool
	EvaluationId               int64
	AdditionalServiceRule      *offeringpb.AdditionalServiceRule
}

func (s *ServiceDO) GetName() string {
	return s.Name
}

func (s *ServiceDO) GetServiceType() offeringpb.ServiceType {
	return s.ServiceType
}

func (s *ServiceDO) GetServiceItemType() offeringpb.ServiceItemType {
	return s.ServiceItemType
}

func (s *ServiceDO) ToModel() models.MoeGroomingService {
	serviceModel := models.MoeGroomingService{
		CategoryID:  int32(s.CategoryId),
		Name:        s.Name,
		Description: proto.String(s.Description),
		TaxID:       int32(s.TaxId),
		Price:       s.Price,
		Duration:    s.GetDuration(),
		MaxDuration: s.GetMaxDuration(),
		Inactive:    &s.Inactive,
		ColorCode:   s.ColorCode,
		CreateTime:  time.Now().Unix(),
		UpdateTime:  time.Now().Unix(),
		//ShowBasePrice:           0, //TODO: OB 字段
		//BookOnlineAvailable:     0, //TODO: OB 字段
		IsAllStaff:                 s.Availability.AvailableForAllStaff,
		BreedFilter:                s.Availability.BreedFilter,
		WeightFilter:               s.Availability.WeightFilter,
		CoatFilter:                 s.Availability.CoatFilter,
		IsAllLocation:              s.Availability.IsAllLocation,
		LodgingFilter:              s.Availability.LodgingFilter,
		ServiceFilter:              proto.Bool(false),
		WeightDownLimit:            s.Availability.AvailableMinWeight,
		WeightUpLimit:              s.Availability.AvailableMaxWeight,
		RequireDedicatedStaff:      s.Availability.RequireDedicatedStaff,
		RequireDedicatedLodging:    s.Availability.RequireDedicatedLodging,
		Images:                     proto.String("[]"),
		AllowedLodgingList:         proto.String("[]"),
		AllowedPetSizeList:         proto.String("[]"),
		AddToCommission:            s.Commission.AddToCommissionBase,
		CanTip:                     s.Commission.CanTip,
		PetSizeFilter:              s.Availability.PetSizeFilter,
		Type:                       proto.Int32(int32(s.ServiceType)),
		ServiceItemType:            proto.Int32(int32(s.ServiceItemType)),
		PriceUnit:                  proto.Int32(int32(s.PriceUnit)),
		Source:                     proto.Int32(int32(s.Source)),
		NumSessions:                s.NumSessions,
		DurationSessionMin:         s.DurationSessionMin,
		Capacity:                   s.Capacity,
		IsRequirePrerequisiteClass: &s.IsRequirePrerequisiteClass,
		PrerequisiteClassIds:       s.PrerequisiteClassIds,
		IsEvaluationRequired:       proto.Bool(s.IsEvaluationRequired),
		IsEvaluationRequiredForOb:  proto.Bool(s.IsEvaluationRequiredForOb),
		EvaluationID:               s.EvaluationId,
		AdditionalServiceRule:      s.AdditionalServiceRule,
	}

	if s.Images != nil {
		imagesJSON, _ := json.Marshal(s.Images)
		serviceModel.Images = proto.String(string(imagesJSON))
	}

	if s.Availability.CustomizedLodgings != nil {
		lodgingListJSON, _ := json.Marshal(s.Availability.CustomizedLodgings)
		serviceModel.AllowedLodgingList = proto.String(string(lodgingListJSON))
	}

	if s.NeedPetSizeFilter() {
		petSizeListJSON, _ := json.Marshal(s.Availability.CustomizedPetSizes)
		serviceModel.AllowedPetSizeList = proto.String(string(petSizeListJSON))
	}

	if s.ServiceType == offeringpb.ServiceType_ADDON {
		serviceModel.ServiceFilter = s.AddonAvailability.ServiceFilter
	}

	return serviceModel
}

func (s *ServiceDO) ValidateParams() error {
	if err := s.validateAvailabilityParams(); err != nil {
		return err
	}
	if s.ServiceType == offeringpb.ServiceType_ADDON {
		if err := s.validateAddonAvailabilityParams(); err != nil {
			return err
		}
	}
	if err := s.validateAutoRolloverRuleParams(); err != nil {
		return err
	}

	return nil
}

func (s *ServiceDO) validateAvailabilityParams() error {
	availability := s.Availability
	if !*availability.IsAllLocation && len(availability.AvailableBusinessIdList) == 0 {
		return status.Error(codes.InvalidArgument, "available locations must be provided")
	}

	//if availability.RequireDedicatedStaff {
	//	if !availability.AvailableForAllStaff && len(availability.AvailableStaffIds) == 0 {
	//		return status.Error(codes.InvalidArgument, "available staff must be provided")
	//	}
	//}

	if *availability.BreedFilter {
		if len(availability.CustomizedBreed) == 0 {
			return status.Error(codes.InvalidArgument, "available pet type and breeds must be provided")
		}

		for _, r := range availability.CustomizedBreed {
			if *r.PetTypeID == 0 {
				return status.Error(codes.InvalidArgument, "pet type id must be provided")
			}

			if !*r.IsAll && len(r.Breeds) == 0 {
				return status.Error(codes.InvalidArgument, "available pet breeds must be provided")
			}
		}
	}

	if *availability.PetSizeFilter && len(availability.CustomizedPetSizes) == 0 {
		return status.Error(codes.InvalidArgument, "available pet sizes must be provided")
	}

	if *availability.CoatFilter && len(availability.CustomizedCoat) == 0 {
		return status.Error(codes.InvalidArgument, "available coat types must be provided")
	}

	return nil
}

func (s *ServiceDO) validateAddonAvailabilityParams() error {
	if s.AddonAvailability == nil {
		return status.Error(codes.InvalidArgument, "addon availability must be provided")
	}
	if s.AddonAvailability.GetServiceFilter() && len(s.AddonAvailability.ServiceFilterList) == 0 {
		return status.Error(codes.InvalidArgument, "available services must be provided")
	}
	for _, r := range s.AddonAvailability.ServiceFilterList {
		if !*r.AvailableForAllServices && len(r.AvailableServiceIdList) == 0 {
			return status.Error(codes.InvalidArgument, "available service ids must be provided")
		}
	}
	return nil
}

func (s *ServiceDO) validateAutoRolloverRuleParams() error {
	if s.AutoRolloverRule == nil {
		return nil
	}

	if s.AutoRolloverRule.Enabled && s.AutoRolloverRule.TargetServiceId == s.ServiceID {
		return merror.NewBizError(errorsModelsV1.Code_CODE_PARAMS_ERROR, "invalid auto rollover rule: target service id cannot be equal to source service id")
	}

	return nil
}

func (s *ServiceDO) NeedBreedFilter() bool {
	return *s.Availability.BreedFilter
}

func (s *ServiceDO) NeedCoatFilter() bool {
	return *s.Availability.CoatFilter
}

func (s *ServiceDO) NeedPetSizeFilter() bool {
	return *s.Availability.PetSizeFilter
}

func (s *ServiceDO) NeedLocationOverride() bool {
	return len(s.Availability.LocationOverrideList) > 0
}

func (s *ServiceDO) NeedLocationStaffOverride() bool {
	return len(s.Availability.StaffOverrideList) > 0
}

func (s *ServiceDO) GetDuration() int32 {
	if s.Duration == nil {
		return 0
	}
	return *s.Duration
}

func (s *ServiceDO) GetMaxDuration() int32 {
	if s.MaxDuration == nil {
		return 0
	}
	return *s.MaxDuration
}

type Availability struct {
	// location availability
	IsAllLocation           *bool
	AvailableBusinessIdList []int64
	LocationOverrideList    []BusinessOverrideRule

	// lodging availability
	RequireDedicatedLodging *bool
	LodgingFilter           *bool
	CustomizedLodgings      []int64

	// staff availability
	RequireDedicatedStaff *bool
	AvailableForAllStaff  *bool
	AvailableStaffIdList  []int64
	StaffOverrideList     []StaffOverrideRule

	// pet type & breed
	BreedFilter     *bool
	CustomizedBreed []PetTypeAndBreedAvailabilityRule

	// pet size availability
	WeightFilter       *bool
	PetSizeFilter      *bool
	CustomizedPetSizes []int64
	AvailableMinWeight *float64 // deprecated field, should use pet size first
	AvailableMaxWeight *float64 // deprecated field, should use pet size first

	// coat type availability
	CoatFilter     *bool
	CustomizedCoat []int64

	// pet code filter
	PetCodeFilter *PetCodeAvailability

	// bundle service ids
	BundleServiceIds []int64
}

func (a *Availability) ApplicableForLodging(lodgingTypeId int64) bool {
	if a.LodgingFilter == nil || !*a.LodgingFilter {
		return true
	}

	for _, lodging := range a.CustomizedLodgings {
		if lodging == lodgingTypeId {
			return true
		}
	}

	return false
}

type PetCodeAvailability struct {
	IsWhiteList  bool    // whether to use white list or black list
	IsAllPetCode bool    // whether to apply to all pet codes
	PetCodeIds   []int64 // pet code list, only valid when IsAllPetCodes is false
}

type PetTypeAndBreedAvailabilityRule struct {
	PetTypeID *int64
	IsAll     *bool
	Breeds    []string
}

type Commission struct {
	AddToCommissionBase *bool
	CanTip              *bool
}

type AddonAvailability struct {
	// service availability
	ServiceFilter     *bool
	ServiceFilterList []AddonAvailabilityRule
}

func (a *AddonAvailability) GetServiceFilter() bool {
	if a == nil || a.ServiceFilter == nil {
		return false
	}
	return *a.ServiceFilter
}

type AddonAvailabilityRule struct {
	ServiceItemType         *offeringpb.ServiceItemType
	AvailableForAllServices *bool
	AvailableServiceIdList  []int64
}

type ApplicableServiceQueryFilter struct {
	BusinessId            *int64
	ServiceItemType       *offeringpb.ServiceItemType
	PetFilter             *PetFilter
	SelectedServiceFilter *SelectedServiceFilter
	LodgingFilter         *LodgingFilter
	Keyword               *string
	Inactive              *bool
	Zipcode               *string // for zone pricing rules
	Coordinate            *latlng.LatLng
}

type PetFilter struct {
	PetWeight     *float64
	PetSizeId     *int64
	PetTypeId     *customerModelV1.PetType
	PetBreed      *string
	PetCoatTypeId *int64
	PetCodeIds    []int64
}

type SelectedServiceFilter struct {
	SelectedServiceIdList   []int64
	SelectedServiceItemType *offeringpb.ServiceItemType
}

type LodgingFilter struct {
	SelectedLodgingTypeIdList []int64
}

type ServiceQueryFilter struct {
	BusinessIDList            []int64
	ServiceIdList             []int64
	ServiceItemTypes          []offeringpb.ServiceItemType
	ServiceTypes              []offeringpb.ServiceType
	Inactive                  *bool
	Keyword                   *string
	PrerequisiteClassIds      []int64
	FilterPrerequisiteClasses *bool
}

type CategoryQueryFilter struct {
	CategoryIdList   []int64
	ServiceTypes     []offeringpb.ServiceType
	ServiceItemTypes []offeringpb.ServiceItemType
}
