package models

type BreedBindingWhereOpt struct {
	Id          *int32  `gorm:"column:id;"`
	CompanyID   *int64  `gorm:"column:company_id;"`
	ServiceIDIn []int64 `gorm:"column:service_id;query_expr:in"`
	Status      *int32  `gorm:"column:status;"`
	PetTypeId   *int32  `gorm:"column:pet_type_id;"`
	IsAll       *bool   `gorm:"column:is_all;"`
}

type BreedBindingUpdateOpt struct {
	BreedNameList *string `gorm:"column:breed_name_list;"`
	Status        *int32  `gorm:"column:status;"`
}
