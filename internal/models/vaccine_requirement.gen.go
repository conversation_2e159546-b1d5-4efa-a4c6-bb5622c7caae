// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

const TableNameVaccineRequirement = "vaccine_requirement"

// VaccineRequirement mapped from table <vaccine_requirement>
type VaccineRequirement struct {
	ID              int64                      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CompanyID       int64                      `gorm:"column:company_id;not null" json:"company_id"`
	ServiceItemType offeringpb.ServiceItemType `gorm:"column:service_item_type;not null;default:0;serializer:proto_enum" json:"service_item_type"`
	VaccineID       int64                      `gorm:"column:vaccine_id;not null" json:"vaccine_id"`
}

// TableName VaccineRequirement's table name
func (*VaccineRequirement) TableName() string {
	return TableNameVaccineRequirement
}
