package models

import "time"

type ServiceStaffOverrideRuleWhereOpt struct {
	ID         int64   `gorm:"column:id"`
	IDs        []int64 `gorm:"column:id;query_expr:in"`
	CompanyId  int64   `gorm:"column:company_id"`
	BusinessId int64   `gorm:"column:business_id"`
	ServiceId  int64   `gorm:"column:service_id"`
	StaffId    int64   `gorm:"column:staff_id"`
}

type ServiceStaffOverrideRuleUpdateOpt struct {
	Price     *float64   `gorm:"column:price"`
	Duration  *int32     `gorm:"column:duration"`
	UpdateBy  *int64     `gorm:"column:update_by"`
	UpdatedAt *time.Time `gorm:"column:updated_at;"`
}
