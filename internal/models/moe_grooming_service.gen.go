// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import  "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

const TableNameMoeGroomingService = "moe_grooming_service"

// MoeGroomingService 宠物服务内容
type MoeGroomingService struct {
	ID          int32   `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"`                          // id
	BusinessID  int32   `gorm:"column:business_id;not null;comment:商家id" json:"business_id"`                           // 商家id
	CategoryID  int32   `gorm:"column:category_id;not null;comment:类型id" json:"category_id"`                           // 类型id
	Name        string  `gorm:"column:name;not null;comment:服务名称" json:"name"`                                         // 服务名称
	Description *string `gorm:"column:description;comment:描述" json:"description"`                                      // 描述
	Type        *int32  `gorm:"column:type;not null;default:1;comment:数据类型：1-主服务(service)；2-额外服务(addons)" json:"type"` // 数据类型：1-主服务(service)；2-额外服务(addons)
	TaxID       int32   `gorm:"column:tax_id;not null;comment:税费id" json:"tax_id"`                                     // 税费id
	Price       float64 `gorm:"column:price;not null;comment:服务价格" json:"price"`                                       // 服务价格
	Duration    int32   `gorm:"column:duration;not null;comment:服务时间" json:"duration"`                                 // 服务时间
	Inactive    *bool   `gorm:"column:inactive;not null" json:"inactive"`
	Sort        int32   `gorm:"column:sort;not null;comment:排序值" json:"sort"`                                    // 排序值
	ColorCode   *string `gorm:"column:color_code;not null;default:#F15A2B;comment:color code" json:"color_code"` // color code
	Status      *int32  `gorm:"column:status;not null;default:1;comment:1 正常 2删除" json:"status"`                 // 1 正常 2删除
	CreateTime  int64   `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`                     // 创建时间
	UpdateTime  int64   `gorm:"column:update_time;not null;comment:修改时间" json:"update_time"`                     // 修改时间
	/*
		booking online 是否显示价格
		0 do not show price
		1 show fixd service price
		2 show price with "starting at"
		3 show price sa "Varies"
	*/
	ShowBasePrice              int32                             `gorm:"column:show_base_price;not null;comment:booking online 是否显示价格\n0 do not show price\n1 show fixd service price\n2 show price with "starting at"\n3 show price sa "Varies"" json:"show_base_price"`
	BookOnlineAvailable        *int32                            `gorm:"column:book_online_available;not null;default:1;comment:0-not  1-yes" json:"book_online_available"`                                                                       // 0-not  1-yes
	IsAllStaff                 *bool                             `gorm:"column:is_all_staff;not null;default:1;comment:是否全选staff" json:"is_all_staff"`                                                                                            // 是否全选staff
	BreedFilter                *bool                             `gorm:"column:breed_filter;not null;comment:filtered by breed, 0-all breed, 1-filter by selected breeds, see table moe_grooming_service_type_breed_binding" json:"breed_filter"` // filtered by breed, 0-all breed, 1-filter by selected breeds, see table moe_grooming_service_type_breed_binding
	WeightFilter               *bool                             `gorm:"column:weight_filter;not null;comment:filtered by weight, 0-all weight, 1-filter by weight range" json:"weight_filter"`                                                   // filtered by weight, 0-all weight, 1-filter by weight range
	WeightDownLimit            *float64                          `gorm:"column:weight_down_limit;not null;default:0.00;comment:weight filter down limit" json:"weight_down_limit"`                                                                // weight filter down limit
	WeightUpLimit              *float64                          `gorm:"column:weight_up_limit;not null;default:0.00;comment:weight filter up limit" json:"weight_up_limit"`                                                                      // weight filter up limit
	CoatFilter                 *bool                             `gorm:"column:coat_filter;not null;comment:filtered by coat, 0-all coat, 1-filter by selected coat" json:"coat_filter"`                                                          // filtered by coat, 0-all coat, 1-filter by selected coat
	CompanyID                  int64                             `gorm:"column:company_id;not null" json:"company_id"`
	IsAllLocation              *bool                             `gorm:"column:is_all_location;not null;default:1" json:"is_all_location"`
	Images                     *string                           `gorm:"column:images;default:_utf8mb4\'[]\'" json:"images"`
	ServiceItemType            *int32                            `gorm:"column:service_item_type;not null;default:1;comment:1 - grooming, 2 - boarding, 3 - daycare, 6 - training class" json:"service_item_type"` // 1 - grooming, 2 - boarding, 3 - daycare, 6 - training class
	PriceUnit                  *int32                            `gorm:"column:price_unit;not null;default:1;comment:1-per session, 2-per night, 3-per hour, 4-per day" json:"price_unit"`                         // 1-per session, 2-per night, 3-per hour, 4-per day
	AddToCommission            *bool                             `gorm:"column:add_to_commission;not null;default:1" json:"add_to_commission"`
	CanTip                     *bool                             `gorm:"column:can_tip;not null;default:1" json:"can_tip"`
	RequireDedicatedStaff      *bool                             `gorm:"column:require_dedicated_staff;not null;default:1" json:"require_dedicated_staff"`
	RequireDedicatedLodging    *bool                             `gorm:"column:require_dedicated_lodging;not null" json:"require_dedicated_lodging"`
	LodgingFilter              *bool                             `gorm:"column:lodging_filter;not null" json:"lodging_filter"`
	AllowedLodgingList         *string                           `gorm:"column:allowed_lodging_list;default:_utf8mb4\'[]\';comment:allowed lodging id list, only when require_dedicated_lodging and lodging_filter is true" json:"allowed_lodging_list"` // allowed lodging id list, only when require_dedicated_lodging and lodging_filter is true
	ServiceFilter              *bool                             `gorm:"column:service_filter;not null;comment:only for add-on" json:"service_filter"`                                                                                                   // only for add-on
	AllowedPetSizeList         *string                           `gorm:"column:allowed_pet_size_list;default:_utf8mb4\'[]\';comment:allowed pet size list, only when service_filter is true" json:"allowed_pet_size_list"`                               // allowed pet size list, only when service_filter is true
	PetSizeFilter              *bool                             `gorm:"column:pet_size_filter;not null" json:"pet_size_filter"`
	MaxDuration                int32                             `gorm:"column:max_duration;not null;comment:max duration in minutes, only for daycare" json:"max_duration"`                                                                                        // max duration in minutes, only for daycare
	Source                     *int32                            `gorm:"column:source;not null;default:1;comment:1-MoeGo Platform 2-Enterprise Hub" json:"source"`                                                                                                  // 1-MoeGo Platform 2-Enterprise Hub
	NumSessions                int32                             `gorm:"column:num_sessions;not null;comment:Number of sessions, only for training" json:"num_sessions"`                                                                                            // Number of sessions, only for training
	DurationSessionMin         int32                             `gorm:"column:duration_session_min;not null;comment:Duration of each session in minutes, only for training" json:"duration_session_min"`                                                           // Duration of each session in minutes, only for training
	Capacity                   int32                             `gorm:"column:capacity;not null;comment:Capacity of group class, zero means unlimited, only for training" json:"capacity"`                                                                         // Capacity of group class, zero means unlimited, only for training
	IsRequirePrerequisiteClass *bool                             `gorm:"column:is_require_prerequisite_class;not null;comment:whether it require a prerequisite class" json:"is_require_prerequisite_class"`                                                        // whether it require a prerequisite class
	PrerequisiteClassIds       []int64                           `gorm:"column:prerequisite_class_ids;comment:Prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture;serializer:json" json:"prerequisite_class_ids"` // Prerequisite class ids of training group class, only valid when is_require_prerequisite_class is ture
	IsEvaluationRequired       *bool                             `gorm:"column:is_evaluation_required;not null;comment:whether evaluation is required" json:"is_evaluation_required"`                                                                               // whether evaluation is required
	IsEvaluationRequiredForOb  *bool                             `gorm:"column:is_evaluation_required_for_ob;not null;comment:whether evaluation is required before online booking" json:"is_evaluation_required_for_ob"`                                           // whether evaluation is required before online booking
	EvaluationID               int64                             `gorm:"column:evaluation_id;not null;comment:related evaluation service id" json:"evaluation_id"`                                                                                                  // related evaluation service id
	AdditionalServiceRule      *offeringpb.AdditionalServiceRule `gorm:"column:additional_service_rule;not null;default:json_object();serializer:proto_json" json:"additional_service_rule"`
}

// TableName MoeGroomingService's table name
func (*MoeGroomingService) TableName() string {
	return TableNameMoeGroomingService
}
