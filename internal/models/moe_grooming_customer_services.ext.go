package models

const (
	SaveTypePrice = 1
	SaveTypeTime  = 2
)

func (m *MoeGroomingCustomerService) GetSavedPrice() *float64 {
	if m == nil || m.SaveType == nil || *m.SaveType != SaveTypePrice {
		return nil
	}
	return m.ServiceFee
}

func (m *MoeGroomingCustomerService) GetSavedTime() *int32 {
	if m == nil || m.SaveType == nil || *m.SaveType != SaveTypeTime {
		return nil
	}
	return &m.ServiceTime
}
