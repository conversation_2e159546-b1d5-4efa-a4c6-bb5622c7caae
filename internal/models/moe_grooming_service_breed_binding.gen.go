// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNameMoeGroomingServiceBreedBinding = "moe_grooming_service_breed_binding"

// MoeGroomingServiceBreedBinding service and breed binding关系表
type MoeGroomingServiceBreedBinding struct {
	ID            int32   `gorm:"column:id;primaryKey;autoIncrement:true;comment:primary key" json:"id"`                                     // primary key
	BusinessID    int32   `gorm:"column:business_id;not null;comment:business id" json:"business_id"`                                        // business id
	ServiceID     int32   `gorm:"column:service_id;not null;comment:service id" json:"service_id"`                                           // service id
	PetTypeID     *int32  `gorm:"column:pet_type_id;not null;default:1;comment:pet type binding, 1-dog 2-cat 3-other..." json:"pet_type_id"` // pet type binding, 1-dog 2-cat 3-other...
	Status        int32   `gorm:"column:status;not null;comment:status, 0-normal 1-invalid" json:"status"`                                   // status, 0-normal 1-invalid
	CreateTime    int64   `gorm:"column:create_time;not null;comment:create time" json:"create_time"`                                        // create time
	UpdateTime    int64   `gorm:"column:update_time;not null;comment:update time" json:"update_time"`                                        // update time
	IsAll         *bool   `gorm:"column:is_all;not null;default:1;comment:is all breed" json:"is_all"`                                       // is all breed
	BreedNameList *string `gorm:"column:breed_name_list;default:json_array();comment:breed name list in json format" json:"breed_name_list"` // breed name list in json format
	BreedNames    string  `gorm:"column:breed_names;not null;comment:Old breed name list" json:"breed_names"`                                // Old breed name list
	CompanyID     int64   `gorm:"column:company_id;not null" json:"company_id"`
}

// TableName MoeGroomingServiceBreedBinding's table name
func (*MoeGroomingServiceBreedBinding) TableName() string {
	return TableNameMoeGroomingServiceBreedBinding
}
