// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNameMoeServicePetCodeFilter = "moe_service_pet_code_filter"

// MoeServicePetCodeFilter mapped from table <moe_service_pet_code_filter>
type MoeServicePetCodeFilter struct {
	ID           int64   `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ServiceID    int64   `gorm:"column:service_id;not null" json:"service_id"`
	IsWhitelist  *bool   `gorm:"column:is_whitelist;not null;default:1" json:"is_whitelist"`
	IsAllPetCode *bool   `gorm:"column:is_all_pet_code;not null;default:1" json:"is_all_pet_code"`
	PetCodeList  []int64 `gorm:"column:pet_code_list;not null;default:json_array();comment:json array of pet code, only valid when is_all_pet_code is false;serializer:json" json:"pet_code_list"` // json array of pet code, only valid when is_all_pet_code is false
}

// TableName MoeServicePetCodeFilter's table name
func (*MoeServicePetCodeFilter) TableName() string {
	return TableNameMoeServicePetCodeFilter
}
