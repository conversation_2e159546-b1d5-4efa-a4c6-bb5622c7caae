// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNameMoeAutoRollover = "moe_auto_rollover"

// MoeAutoRollover auto rollover setting for grooming service
type MoeAutoRollover struct {
	ID              int64 `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ServiceID       int64 `gorm:"column:service_id;not null" json:"service_id"`
	Enabled         *bool `gorm:"column:enabled;not null" json:"enabled"`
	AfterMinute     int32 `gorm:"column:after_minute;not null;comment:minutes after the max duration" json:"after_minute"` // minutes after the max duration
	TargetServiceID int64 `gorm:"column:target_service_id;not null" json:"target_service_id"`
}

// TableName MoeAutoRollover's table name
func (*MoeAutoRollover) TableName() string {
	return TableNameMoeAutoRollover
}
