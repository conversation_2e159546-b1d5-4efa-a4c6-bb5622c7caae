// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameServiceStaffOverrideRule = "service_staff_override_rule"

// ServiceStaffOverrideRule mapped from table <service_staff_override_rule>
type ServiceStaffOverrideRule struct {
	ID         int64      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CompanyID  int64      `gorm:"column:company_id;not null" json:"company_id"`
	BusinessID int64      `gorm:"column:business_id;not null" json:"business_id"`
	StaffID    int64      `gorm:"column:staff_id;not null" json:"staff_id"`
	ServiceID  int64      `gorm:"column:service_id;not null" json:"service_id"`
	Price      *float64   `gorm:"column:price;comment:customized price for the service" json:"price"`          // customized price for the service
	Duration   *int32     `gorm:"column:duration;comment:customized duration for the service" json:"duration"` // customized duration for the service
	UpdatedBy  int64      `gorm:"column:updated_by;not null" json:"updated_by"`
	CreatedAt  *time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt  *time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;autoUpdateTime:false" json:"updated_at"`
}

// TableName ServiceStaffOverrideRule's table name
func (*ServiceStaffOverrideRule) TableName() string {
	return TableNameServiceStaffOverrideRule
}
