// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameMoeGroomingServiceLocation = "moe_grooming_service_location"

// MoeGroomingServiceLocation mapped from table <moe_grooming_service_location>
type MoeGroomingServiceLocation struct {
	ID          int64      `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"` // id
	CompanyID   int64      `gorm:"column:company_id;not null" json:"company_id"`
	BusinessID  int32      `gorm:"column:business_id;not null;comment:商家id" json:"business_id"` // 商家id
	ServiceID   int32      `gorm:"column:service_id;not null" json:"service_id"`
	TaxID       *int32     `gorm:"column:tax_id;comment:税费id" json:"tax_id"`                                                  // 税费id
	Price       *float64   `gorm:"column:price;comment:服务价格" json:"price"`                                                    // 服务价格
	Duration    *int32     `gorm:"column:duration;comment:服务时间" json:"duration"`                                              // 服务时间
	IsDeleted   *bool      `gorm:"column:is_deleted;not null;comment:是否已删除" json:"is_deleted"`                                // 是否已删除
	CreateTime  *time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`     // 创建时间
	UpdateTime  *time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:修改时间" json:"update_time"`     // 修改时间
	MaxDuration *int32     `gorm:"column:max_duration;comment:max duration in minutes, only for daycare" json:"max_duration"` // max duration in minutes, only for daycare
}

// TableName MoeGroomingServiceLocation's table name
func (*MoeGroomingServiceLocation) TableName() string {
	return TableNameMoeGroomingServiceLocation
}
