package models

import (
	"time"

	"github.com/lib/pq"
)

type EvaluationWhereOpt struct {
	ID           *int64   `gorm:"column:id"`
	IDs          *[]int64 `gorm:"column:id;query_expr:in"`
	CompanyId    *int64   `gorm:"column:company_id"`
	CompanyIds   []int64  `gorm:"column:company_id;query_expr:in"`
	IsResettable *bool    `gorm:"column:is_resettable"`
}

type EvaluationUpdateOpt struct {
	Name                    *string        `gorm:"column:name"`
	AvailableForAllBusiness *bool          `gorm:"column:available_for_all_business"`
	AvailableBusinessIdList *pq.Int64Array `gorm:"column:available_business_id_list"`
	ServiceItemTypeList     *pq.Int32Array `gorm:"column:service_item_type_list"`
	Price                   *float64       `gorm:"column:price"`
	Duration                *int32         `gorm:"column:duration"`
	ColorCode               *string        `gorm:"column:color_code"`
	IsActive                *bool          `gorm:"column:is_active"`
	LodgingFilter           *bool          `gorm:"column:lodging_filter"`
	CustomizedLodgingIds    *pq.Int64Array `gorm:"column:allowed_lodging_list"`
	Description             *string        `gorm:"column:description"`
	OnlineBookingAlias      *string        `gorm:"column:online_booking_alias"`
	IsAllStaff              *bool          `gorm:"column:is_all_staff"`
	AllowedStaffList        *pq.Int64Array `gorm:"column:allowed_staff_list"`
	AllowStaffAutoAssign    *bool          `gorm:"column:allow_staff_auto_assign"`
	IsResettable            *bool          `gorm:"column:is_resettable"`
	ResetIntervalDays       *int32         `gorm:"column:reset_interval_days"`
	TaxId                   *int64         `gorm:"column:tax_id"`
	UpdatedAt               *time.Time     `gorm:"column:updated_at"`
	DeletedAt               *time.Time     `gorm:"column:deleted_at"`
	Source                  *int16         `gorm:"column:source"`
}
