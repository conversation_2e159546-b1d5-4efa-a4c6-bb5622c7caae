// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameMoeGroomingServiceCoatBinding = "moe_grooming_service_coat_binding"

// MoeGroomingServiceCoatBinding mapped from table <moe_grooming_service_coat_binding>
type MoeGroomingServiceCoatBinding struct {
	ID         int64      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	BusinessID int32      `gorm:"column:business_id;not null;comment:business id" json:"business_id"`                           // business id
	ServiceID  int32      `gorm:"column:service_id;not null;comment:service id" json:"service_id"`                              // service id
	CoatIDList *string    `gorm:"column:coat_id_list;not null;default:json_array();comment:coat id list" json:"coat_id_list"`   // coat id list
	CreateTime *time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:create time" json:"create_time"` // create time
	UpdateTime *time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:update time" json:"update_time"` // update time
	CompanyID  int64      `gorm:"column:company_id;not null" json:"company_id"`
}

// TableName MoeGroomingServiceCoatBinding's table name
func (*MoeGroomingServiceCoatBinding) TableName() string {
	return TableNameMoeGroomingServiceCoatBinding
}
