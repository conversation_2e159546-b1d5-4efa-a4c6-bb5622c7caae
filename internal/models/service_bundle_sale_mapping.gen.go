// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameServiceBundleSaleMapping = "service_bundle_sale_mapping"

// ServiceBundleSaleMapping mapped from table <service_bundle_sale_mapping>
type ServiceBundleSaleMapping struct {
	ID              int64      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CompanyID       int64      `gorm:"column:company_id;not null" json:"company_id"`
	ServiceID       int64      `gorm:"column:service_id;not null" json:"service_id"`
	BundleServiceID int64      `gorm:"column:bundle_service_id;not null" json:"bundle_service_id"`
	CreatedAt       *time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
}

// TableName ServiceBundleSaleMapping's table name
func (*ServiceBundleSaleMapping) TableName() string {
	return TableNameServiceBundleSaleMapping
}
