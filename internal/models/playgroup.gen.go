// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"

	"gorm.io/gorm"
)

const TableNamePlaygroup = "playgroup"

// Playgroup mapped from table <playgroup>
type Playgroup struct {
	ID             int64          `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CompanyID      int64          `gorm:"column:company_id;not null;comment:company id" json:"company_id"` // company id
	Name           string         `gorm:"column:name;not null" json:"name"`
	ColorCode      string         `gorm:"column:color_code;not null;comment:playgroup color code" json:"color_code"`                             // playgroup color code
	MaxPetCapacity int32          `gorm:"column:max_pet_capacity;not null;comment:max pet capacity" json:"max_pet_capacity"`                     // max pet capacity
	Sort           int32          `gorm:"column:sort;not null;comment:Playgroup list sort. Start with 1 and put the smallest first" json:"sort"` // Playgroup list sort. Start with 1 and put the smallest first
	Description    string         `gorm:"column:description;not null" json:"description"`
	CreatedAt      *time.Time     `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt      *time.Time     `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;autoUpdateTime:false" json:"updated_at"`
	CreatedBy      int64          `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedBy      int64          `gorm:"column:updated_by;not null" json:"updated_by"`
	DeletedAt      gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
	DeletedBy      int64          `gorm:"column:deleted_by;not null" json:"deleted_by"`
}

// TableName Playgroup's table name
func (*Playgroup) TableName() string {
	return TableNamePlaygroup
}
