package models

import (
	"encoding/json"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/constant"
)

func (s *MoeGroomingService) GetDescription() string {
	if s == nil || s.Description == nil {
		return ""
	}
	return *s.Description
}

func (s *MoeGroomingService) GetImages() []string {
	images := make([]string, 0)
	_ = json.Unmarshal([]byte(*s.Images), &images)
	return images
}

func (s *MoeGroomingService) GetCustomizedLodgingList() []int64 {
	lodgingIds := make([]int64, 0)
	_ = json.Unmarshal([]byte(*s.AllowedLodgingList), &lodgingIds)
	return lodgingIds
}

func (s *MoeGroomingService) GetCustomizedPetSizeList() []int64 {
	petSizeIds := make([]int64, 0)
	_ = json.Unmarshal([]byte(*s.AllowedPetSizeList), &petSizeIds)
	return petSizeIds
}

func (s *MoeGroomingService) AvailableForAllLodging() bool {
	return s.LodgingFilter != nil && !*s.LodgingFilter
}

func (s *MoeGroomingService) AvailableInAllLocation() bool {
	return s.IsAllLocation != nil && *s.IsAllLocation
}

func (s *MoeGroomingService) AvailableForAllSelectedService() bool {
	return s.ServiceFilter != nil && !*s.ServiceFilter
}

func (s *MoeGroomingService) IsDeleted() bool {
	return s.Status != nil && *s.Status == constant.DeleteStatus
}

func (s *MoeGroomingService) GetServiceType() offeringpb.ServiceType {
	if s == nil || s.Type == nil {
		return offeringpb.ServiceType_SERVICE_TYPE_UNSPECIFIED
	}
	return offeringpb.ServiceType(*s.Type)
}

func (s *MoeGroomingService) GetServiceItemType() offeringpb.ServiceItemType {
	if s == nil || s.ServiceItemType == nil {
		return offeringpb.ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
	}
	return offeringpb.ServiceItemType(*s.ServiceItemType)
}
