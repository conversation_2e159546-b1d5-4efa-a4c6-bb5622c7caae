// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNameMoeGroomingServiceCategory = "moe_grooming_service_category"

// MoeGroomingServiceCategory 宠物服务类型
type MoeGroomingServiceCategory struct {
	ID              int32  `gorm:"column:id;primaryKey;autoIncrement:true;comment:id" json:"id"`                // id
	BusinessID      int32  `gorm:"column:business_id;not null;comment:商家id" json:"business_id"`                 // 商家id
	Name            string `gorm:"column:name;not null;comment:类型名称" json:"name"`                               // 类型名称
	Type            int32  `gorm:"column:type;not null;comment:数据类型：1-主服务(service)；2-额外服务(addons)" json:"type"` // 数据类型：1-主服务(service)；2-额外服务(addons)
	Sort            int32  `gorm:"column:sort;not null;comment:排序值" json:"sort"`                                // 排序值
	Status          *int32 `gorm:"column:status;not null;default:1;comment:1 正常  2删除" json:"status"`            // 1 正常  2删除
	CreateTime      int64  `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`                 // 创建时间
	UpdateTime      int64  `gorm:"column:update_time;not null;comment:修改时间" json:"update_time"`                 // 修改时间
	CompanyID       int64  `gorm:"column:company_id;not null" json:"company_id"`
	ServiceItemType *int32 `gorm:"column:service_item_type;not null;default:1;comment:1 - grooming, 2 - boarding, 3 - daycare" json:"service_item_type"` // 1 - grooming, 2 - boarding, 3 - daycare
}

// TableName MoeGroomingServiceCategory's table name
func (*MoeGroomingServiceCategory) TableName() string {
	return TableNameMoeGroomingServiceCategory
}
