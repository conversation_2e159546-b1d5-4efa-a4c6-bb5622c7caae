// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameServiceStaffAvailability = "service_staff_availability"

// ServiceStaffAvailability mapped from table <service_staff_availability>
type ServiceStaffAvailability struct {
	ID        int64      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	ServiceID int64      `gorm:"column:service_id;not null" json:"service_id"`
	StaffID   int64      `gorm:"column:staff_id;not null;comment:available staff id" json:"staff_id"` // available staff id
	CreatedAt *time.Time `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt *time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;autoUpdateTime:false" json:"updated_at"`
}

// TableName ServiceStaffAvailability's table name
func (*ServiceStaffAvailability) TableName() string {
	return TableNameServiceStaffAvailability
}
