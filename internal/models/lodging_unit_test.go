package models

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestLodgingUnitWhereOpt_AddLodgingTypesFilter(t *testing.T) {
	l := &LodgingUnitWhereOpt{}
	var lodgingTypeIds []int64

	// 已有 lodgingTypeIDIn 为 nil，不过滤。AddLodgingTypesFilter 后，按入参 lodgingTypeIds 过滤
	{
		// 不过滤
		l.LodgingTypeIDIn = nil
		lodgingTypeIds = nil
		l.AddLodgingTypesFilter(lodgingTypeIds)
		assert.Nil(t, l.LodgingTypeIDIn)

		// 过滤掉所有
		l.LodgingTypeIDIn = nil
		lodgingTypeIds = []int64{}
		l.AddLodgingTypesFilter(lodgingTypeIds)
		assert.NotNil(t, l.LodgingTypeIDIn)
		assert.Empty(t, l.LodgingTypeIDIn)

		// 仅过滤出 1、2、3 的 lodgingType
		l.LodgingTypeIDIn = nil
		lodgingTypeIds = []int64{1, 2, 3}
		l.AddLodgingTypesFilter(lodgingTypeIds)
		assert.Equal(t, []int64{1, 2, 3}, *l.LodgingTypeIDIn)
	}

	// 已有 lodgingTypeIDIn 为空数组，过滤掉所有。AddLodgingTypesFilter 后，依然是过滤掉所有
	{
		l.LodgingTypeIDIn = &[]int64{}
		lodgingTypeIds = nil
		l.AddLodgingTypesFilter(lodgingTypeIds)
		assert.NotNil(t, l.LodgingTypeIDIn)
		assert.Empty(t, *l.LodgingTypeIDIn)

		l.LodgingTypeIDIn = &[]int64{}
		lodgingTypeIds = []int64{}
		l.AddLodgingTypesFilter(lodgingTypeIds)
		assert.NotNil(t, l.LodgingTypeIDIn)
		assert.Empty(t, *l.LodgingTypeIDIn)

		l.LodgingTypeIDIn = &[]int64{}
		lodgingTypeIds = []int64{1, 2, 3}
		l.AddLodgingTypesFilter(lodgingTypeIds)
		assert.NotNil(t, l.LodgingTypeIDIn)
		assert.Empty(t, *l.LodgingTypeIDIn)
	}

	// 已有 lodgingTypeIDIn 为 [1, 2, 3]。AddLodgingTypesFilter 后，取两者交集过滤
	{
		// 过滤出 1、2、3 的 lodgingType
		l.LodgingTypeIDIn = &[]int64{1, 2, 3}
		lodgingTypeIds = nil
		l.AddLodgingTypesFilter(lodgingTypeIds)
		assert.Equal(t, []int64{1, 2, 3}, *l.LodgingTypeIDIn)

		// 过滤掉所有
		l.LodgingTypeIDIn = &[]int64{1, 2, 3}
		lodgingTypeIds = []int64{}
		l.AddLodgingTypesFilter(lodgingTypeIds)
		assert.NotNil(t, l.LodgingTypeIDIn)
		assert.Empty(t, *l.LodgingTypeIDIn)

		// 过滤出 2、3 的 lodgingType
		l.LodgingTypeIDIn = &[]int64{1, 2, 3}
		lodgingTypeIds = []int64{2, 3, 4}
		l.AddLodgingTypesFilter(lodgingTypeIds)
		assert.Equal(t, []int64{2, 3}, *l.LodgingTypeIDIn)
	}
}
