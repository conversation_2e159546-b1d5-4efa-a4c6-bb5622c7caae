package models

import offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

type ServiceWhereOpt struct {
	Id                   *int64                       `gorm:"column:id"`
	ServiceIdListIn      []int64                      `gorm:"column:id;query_expr:in"`
	ServiceItemTypesIn   []offeringpb.ServiceItemType `gorm:"column:service_item_type;query_expr:in"`
	ServiceTypesIn       []offeringpb.ServiceType     `gorm:"column:type;query_expr:in"`
	Inactive             *bool                        `gorm:"column:inactive"`
	Status               *int32                       `gorm:"column:status"`
	CompanyID            *int64                       `gorm:"column:company_id"`
	NameLike             *string                      `gorm:"column:name;query_expr:like"`
	PetSizeFilter        *bool                        `gorm:"column:pet_size_filter"`
	LodgingFilter        *bool                        `gorm:"column:lodging_filter"`
	PrerequisiteClassIds []int64                      `gorm:"column:prerequisite_class_ids;query_expr:in"`
}

type ServiceUpdateOpt struct {
	Name                       *string  `gorm:"column:name"`
	CategoryID                 *int64   `gorm:"column:category_id"`
	Description                *string  `gorm:"column:description"`
	TaxID                      *int64   `gorm:"column:tax_id"`
	Price                      *float64 `gorm:"column:price"`
	Duration                   *int32   `gorm:"column:duration"`
	Inactive                   *bool    `gorm:"column:inactive"`
	ColorCode                  *string  `gorm:"column:color_code"`
	UpdateTime                 int64    `gorm:"column:update_time"`
	BreedFilter                *bool    `gorm:"column:breed_filter"`
	WeightFilter               *bool    `gorm:"column:weight_filter"`
	WeightDownLimit            *float64 `gorm:"column:weight_down_limit"`
	WeightUpLimit              *float64 `gorm:"column:weight_up_limit"`
	CoatFilter                 *bool    `gorm:"column:coat_filter"`
	IsAllLocation              *bool    `gorm:"column:is_all_location"`
	IsAllStaff                 *bool    `gorm:"column:is_all_staff"`
	Images                     *string  `gorm:"column:images"`
	PriceUnit                  *int32   `gorm:"column:price_unit"`
	AddToCommission            *bool    `gorm:"column:add_to_commission"`
	CanTip                     *bool    `gorm:"column:can_tip"`
	RequireDedicatedStaff      *bool    `gorm:"column:require_dedicated_staff"`
	RequiredDedicatedLodging   *bool    `gorm:"column:require_dedicated_lodging"`
	LodgingFilter              *bool    `gorm:"column:lodging_filter"`
	AllowedLodgingList         *string  `gorm:"column:allowed_lodging_list"`
	ServiceFilter              *bool    `gorm:"column:service_filter"`
	AllowedPetSizeList         *string  `gorm:"column:allowed_pet_size_list"`
	PetSizeFilter              *bool    `gorm:"column:pet_size_filter"`
	MaxDuration                *int32   `gorm:"column:max_duration"`
	Source                     *int32   `gorm:"column:source"`
	NumSessions                *int32   `gorm:"column:num_sessions"`
	DurationSessionMin         *int32   `gorm:"column:duration_session_min"`
	Capacity                   *int32   `gorm:"column:capacity"`
	IsRequirePrerequisiteClass *bool    `gorm:"column:is_require_prerequisite_class"`
	PrerequisiteClassIds       *string  `gorm:"column:prerequisite_class_ids;serializer:json"`
	IsEvaluationRequired       *bool    `gorm:"column:is_evaluation_required"`
	IsEvaluationRequiredForOb  *bool    `gorm:"column:is_evaluation_required_for_ob"`
	EvaluationID               *int64   `gorm:"column:evaluation_id"`
	AdditionalServiceRule      *string  `gorm:"column:additional_service_rule;serializer:json"`
}
