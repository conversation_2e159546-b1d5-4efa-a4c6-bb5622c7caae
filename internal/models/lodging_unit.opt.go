package models

import (
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
)

type LodgingUnitWhereOpt struct {
	ID              *int64   `gorm:"column:id;"`
	IDIn            *[]int64 `gorm:"column:id;query_expr:in"`
	CompanyID       *int64   `gorm:"column:company_id;"`
	BusinessID      *int64   `gorm:"column:business_id;"`
	LodgingTypeID   *int64   `gorm:"column:lodging_type_id;"`
	LodgingTypeIDIn *[]int64 `gorm:"column:lodging_type_id;query_expr:in"`
	CameraId        *int64   `gorm:"column:camera_id;"`
	CameraIdIDIn    *[]int64 `gorm:"column:camera_id;query_expr:in"`
}

// AddLodgingTypesFilter 添加房型过滤条件. 与已有的房型过滤条件取交集
func (l *LodgingUnitWhereOpt) AddLodgingTypesFilter(lodgingTypeIds []int64) {
	if lodgingTypeIds == nil {
		return
	}
	if l.LodgingTypeIDIn == nil {
		l.LodgingTypeIDIn = &lodgingTypeIds
	} else {
		l.LodgingTypeIDIn = lo.ToPtr(lo.Intersect(*l.LodgingTypeIDIn, lodgingTypeIds))
	}
}

type LodgingUnitUpdateOpt struct {
	Name      *string         `gorm:"column:name;"`
	UpdatedAt *time.Time      `gorm:"column:updated_at;"`
	UpdatedBy *int64          `gorm:"column:updated_by;"`
	DeletedAt *gorm.DeletedAt `gorm:"column:deleted_at;"`
	DeletedBy *int64          `gorm:"column:deleted_by;"`
	CameraId  *int64          `gorm:"column:camera_id;"`
	Sort      *int32          `gorm:"column:sort;"`
}
