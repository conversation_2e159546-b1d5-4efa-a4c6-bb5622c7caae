// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

const TableNameMoeGroomingCustomerService = "moe_grooming_customer_services"

// MoeGroomingCustomerService 客户独立服务信息表
type MoeGroomingCustomerService struct {
	ID            int32    `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	BusinessID    int32    `gorm:"column:business_id;not null;comment:商家id" json:"business_id"`                                            // 商家id
	CustomerID    int32    `gorm:"column:customer_id;not null;comment:客户id" json:"customer_id"`                                            // 客户id
	CreateBy      int32    `gorm:"column:create_by;not null;comment:记录创建staff_id" json:"create_by"`                                        // 记录创建staff_id
	PetID         int32    `gorm:"column:pet_id;not null;comment:宠物id" json:"pet_id"`                                                      // 宠物id
	ServiceID     int32    `gorm:"column:service_id;not null;comment:服务id（对应表mm_business_services）" json:"service_id"`                     // 服务id（对应表mm_business_services）
	ServiceName   string   `gorm:"column:service_name;not null;comment:服务名称" json:"service_name"`                                          // 服务名称
	ServiceDetail *string  `gorm:"column:service_detail;comment:服务描述（详情）" json:"service_detail"`                                           // 服务描述（详情）
	ServiceType   int32    `gorm:"column:service_type;not null;default:1;comment:数据类型：1-主服务；2-额外服务" json:"service_type"`                   // 数据类型：1-主服务；2-额外服务
	CategoryID    int32    `gorm:"column:category_id;not null;comment:服务分类id" json:"category_id"`                                          // 服务分类id
	ServiceTime   int32    `gorm:"column:service_time;not null;comment:服务时长（分钟）" json:"service_time"`                                      // 服务时长（分钟）
	ServiceFee    *float64 `gorm:"column:service_fee;not null;default:0.0000;comment:服务价格" json:"service_fee"`                             // 服务价格
	SaveType      *int32   `gorm:"column:save_type;not null;default:1;comment:save price/time for client:1-price；2-time" json:"save_type"` // save price/time for client:1-price；2-time
	Status        *int32   `gorm:"column:status;not null;default:1;comment:数据状态：1-正常 2-已删除 3-数据冲突导致的删除" json:"status"`                     // 数据状态：1-正常 2-已删除 3-数据冲突导致的删除
	CreateTime    int64    `gorm:"column:create_time;not null;comment:创建时间" json:"create_time"`                                            // 创建时间
	UpdateTime    int64    `gorm:"column:update_time;not null;comment:修改时间" json:"update_time"`                                            // 修改时间
	TaxID         int32    `gorm:"column:tax_id;not null;comment:税费id" json:"tax_id"`                                                      // 税费id
	TaxRate       *float64 `gorm:"column:tax_rate;not null;default:0.000;comment:税率" json:"tax_rate"`                                      // 税率
	CompanyID     int64    `gorm:"column:company_id;not null" json:"company_id"`
}

// TableName MoeGroomingCustomerService's table name
func (*MoeGroomingCustomerService) TableName() string {
	return TableNameMoeGroomingCustomerService
}
