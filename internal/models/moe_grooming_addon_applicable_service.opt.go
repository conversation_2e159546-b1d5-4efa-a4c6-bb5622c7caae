package models

import offeringV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

type MoeGroomingAddonApplicableServiceWhereOpt struct {
	Id                      *int64                      `gorm:"column:id;"`
	CompanyID               *int64                      `gorm:"column:company_id;"`
	AddonID                 *int64                      `gorm:"column:addon_id;"`
	AddonIDIn               []int64                     `gorm:"column:addon_id;query_expr:in"`
	ServiceItemType         *offeringV1.ServiceItemType `gorm:"column:service_item_type;"`
	AvailableForAllServices *bool                       `gorm:"column:available_for_all_services;"`
}

type MoeGroomingAddonApplicableServiceUpdateOpt struct {
	AvailableServiceIDList *string `gorm:"column:available_service_id_list;"`
}
