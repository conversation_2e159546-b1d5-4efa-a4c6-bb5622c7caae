package models

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
)

type ServiceCategoryWhereOpt struct {
	CompanyID        *int64               `gorm:"column:company_id;"`
	Name             *string              `gorm:"column:name;"`
	Status           *int32               `gorm:"column:status;"`
	IDList           []int64              `gorm:"column:id;query_expr:in"`
	ServiceItemTypes []v1.ServiceItemType `gorm:"column:service_item_type;query_expr:in"`
	ServiceTypes     []v1.ServiceType     `gorm:"column:type;query_expr:in"`
}
