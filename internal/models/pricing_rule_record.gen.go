// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"

	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	"gorm.io/gorm"
)

const TableNamePricingRuleRecord = "pricing_rule_record"

// PricingRuleRecord mapped from table <pricing_rule_record>
type PricingRuleRecord struct {
	ID                       int64                                      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CompanyID                int64                                      `gorm:"column:company_id;not null" json:"company_id"`
	RuleType                 offeringModelsV2.RuleType                  `gorm:"column:rule_type;not null;comment:rule type, 1-multi pets, 2-multi duration, 3-peak date" json:"rule_type"` // rule type, 1-multi pets, 2-multi duration, 3-peak date
	RuleName                 string                                     `gorm:"column:rule_name;not null" json:"rule_name"`
	IsActive                 bool                                       `gorm:"column:is_active;not null" json:"is_active"`
	AllBoardingApplicable    bool                                       `gorm:"column:all_boarding_applicable;not null" json:"all_boarding_applicable"`
	SelectedBoardingServices []int64                                    `gorm:"column:selected_boarding_services;default:[];serializer:json" json:"selected_boarding_services"`
	AllDaycareApplicable     bool                                       `gorm:"column:all_daycare_applicable;not null" json:"all_daycare_applicable"`
	SelectedDaycareServices  []int64                                    `gorm:"column:selected_daycare_services;default:[];serializer:json" json:"selected_daycare_services"`
	RuleApplyType            offeringModelsV2.RuleApplyType             `gorm:"column:rule_apply_type;not null;comment:rule apply type, 1-each pet, 2-additional pets, 3-all pets(deprecated), 4-first pet" json:"rule_apply_type"` // rule apply type, 1-each pet, 2-additional pets, 3-all pets(deprecated), 4-first pet
	NeedInSameLodging        bool                                       `gorm:"column:need_in_same_lodging;not null" json:"need_in_same_lodging"`
	RuleConfiguration        *offeringModelsV2.PricingRuleConfiguration `gorm:"column:rule_configuration;default:{};serializer:proto_json" json:"rule_configuration"`
	UpdatedBy                int64                                      `gorm:"column:updated_by;not null" json:"updated_by"`
	CreatedAt                *time.Time                                 `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt                *time.Time                                 `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt                gorm.DeletedAt                             `gorm:"column:deleted_at" json:"deleted_at"`
	IsChargePerLodging       bool                                       `gorm:"column:is_charge_per_lodging;not null" json:"is_charge_per_lodging"`
	Source                   offeringModelsV2.Source                    `gorm:"column:source;not null;default:1;comment:1-MoeGo Platform 2-Enterprise Hub" json:"source"` // 1-MoeGo Platform 2-Enterprise Hub
	AllGroomingApplicable    bool                                       `gorm:"column:all_grooming_applicable;not null" json:"all_grooming_applicable"`
	SelectedGroomingServices []int64                                    `gorm:"column:selected_grooming_services;default:[];serializer:json" json:"selected_grooming_services"`
	AllAddonApplicable       bool                                       `gorm:"column:all_addon_applicable;not null" json:"all_addon_applicable"`
	SelectedAddonServices    []int64                                    `gorm:"column:selected_addon_services;default:[];serializer:json" json:"selected_addon_services"`
}

// TableName PricingRuleRecord's table name
func (*PricingRuleRecord) TableName() string {
	return TableNamePricingRuleRecord
}
