package models

import (
	"encoding/json"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
)

func (m *MoeGroomingAddonApplicableService) GetAllowedServiceList() []int64 {
	serviceIDList := make([]int64, 0)
	_ = json.Unmarshal([]byte(*m.AvailableServiceIDList), &serviceIDList)
	return serviceIDList
}

func (m *MoeGroomingAddonApplicableService) GetServiceItemType() offeringpb.ServiceItemType {
	return offeringpb.ServiceItemType(m.ServiceItemType)
}
