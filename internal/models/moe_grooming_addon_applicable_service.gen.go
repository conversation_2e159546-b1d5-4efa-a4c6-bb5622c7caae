// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"
)

const TableNameMoeGroomingAddonApplicableService = "moe_grooming_addon_applicable_service"

// MoeGroomingAddonApplicableService mapped from table <moe_grooming_addon_applicable_service>
type MoeGroomingAddonApplicableService struct {
	ID                      int64      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CompanyID               int64      `gorm:"column:company_id;not null" json:"company_id"`
	AddonID                 int64      `gorm:"column:addon_id;not null;comment:moe_grooming_service.id" json:"addon_id"`                                                                                              // moe_grooming_service.id
	ServiceItemType         int32      `gorm:"column:service_item_type;not null;comment:1 - grooming, 2 - boarding, 3 - daycare" json:"service_item_type"`                                                            // 1 - grooming, 2 - boarding, 3 - daycare
	AvailableForAllServices *bool      `gorm:"column:available_for_all_services;not null;comment:false for all service in service_item_type, true for specific service" json:"available_for_all_services"`            // false for all service in service_item_type, true for specific service
	AvailableServiceIDList  *string    `gorm:"column:available_service_id_list;default:_utf8mb4\'[]\';comment:customized applicable service list, only when service_filter is true" json:"available_service_id_list"` // customized applicable service list, only when service_filter is true
	CreatedAt               *time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt               *time.Time `gorm:"column:updated_at;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName MoeGroomingAddonApplicableService's table name
func (*MoeGroomingAddonApplicableService) TableName() string {
	return TableNameMoeGroomingAddonApplicableService
}
