package models

import (
	"time"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
)

type EventStatus string

const (
	StatusPending EventStatus = "PENDING"
	StatusDone    EventStatus = "DONE"
	StatusFailed  EventStatus = "FAILED"
)

const TableNameEvent = "event"

type Event struct {
	ID          int64                 `gorm:"column:id;primaryKey"`
	CreatedAt   time.Time             `gorm:"column:created_at"`
	UpdatedAt   time.Time             `gorm:"column:updated_at"`
	MessageType eventbuspb.EventType  `gorm:"column:message_type;serializer:proto_enum"`
	ReferenceID string                `gorm:"column:reference_id"`
	Payload     *eventbuspb.EventData `gorm:"column:payload;serializer:proto_json"`
	Status      EventStatus           `gorm:"column:status"`
	RetryTimes  int                   `gorm:"column:retry_times"`
}

func (e *Event) TableName() string {
	return TableNameEvent
}
