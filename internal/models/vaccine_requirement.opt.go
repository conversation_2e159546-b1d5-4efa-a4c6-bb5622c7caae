package models

import offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

type VaccineRequirementWhereOpt struct {
	CompanyId        int64                        `gorm:"column:company_id"`
	ServiceItemTypes []offeringpb.ServiceItemType `gorm:"column:service_item_type;query_expr:in"`
	VaccineIDs       []int64                      `gorm:"column:vaccine_id;query_expr:in"`
}

type VaccineRequirementDeleteOpt struct {
	CompanyId       int64                       `gorm:"column:company_id"`
	ServiceItemType *offeringpb.ServiceItemType `gorm:"column:service_item_type"`
	VaccineID       *int64                      `gorm:"column:vaccine_id"`
}
