// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
)

const TableNameCustomizeCareType = "customize_care_type"

// CustomizeCareType mapped from table <customize_care_type>
type CustomizeCareType struct {
	ID              int64                      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	Name            string                     `gorm:"column:name;not null" json:"name"`
	CompanyID       int64                      `gorm:"column:company_id;not null" json:"company_id"`
	ServiceItemType offeringpb.ServiceItemType `gorm:"column:service_item_type;not null" json:"service_item_type"`
	Sort            int32                      `gorm:"column:sort;not null" json:"sort"`
	UpdatedBy       int64                      `gorm:"column:updated_by;not null" json:"updated_by"`
	CreatedAt       *time.Time                 `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt       *time.Time                 `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName CustomizeCareType's table name
func (*CustomizeCareType) TableName() string {
	return TableNameCustomizeCareType
}
