package models

import (
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"time"
)

type CustomizeCareTypeWhereOpt struct {
	ID              *int64                     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CompanyID       *int64                     `gorm:"column:company_id;not null" json:"company_id"`
	ServiceItemType offeringpb.ServiceItemType `gorm:"column:service_item_type" json:"service_item_type"`
}

type CustomizeCareTypeUpdateOpt struct {
	Name      *string   `gorm:"column:name;not null" json:"name"`
	UpdatedAt time.Time `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	UpdatedBy *int64    `gorm:"column:updated_by;not null" json:"updated_by"`
}
