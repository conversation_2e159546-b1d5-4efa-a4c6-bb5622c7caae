package models

import (
	"time"

	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
)

type DiscountSettingWhereOpt struct {
	ID        *int64 `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CompanyID int64  `gorm:"column:company_id;not null" json:"company_id"`
}

type DiscountSettingUpdateOpt struct {
	UpdatedBy     *int64                      `gorm:"column:updated_by;not null" json:"updated_by"`
	CreatedAt     *time.Time                  `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt     *time.Time                  `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	ApplyBestOnly *bool                       `gorm:"column:apply_best_only;not null" json:"apply_best_only"`
	ApplySequence []offeringModelsV2.RuleType `gorm:"column:apply_sequence;default:[];serializer:json" json:"apply_sequence"`
}
