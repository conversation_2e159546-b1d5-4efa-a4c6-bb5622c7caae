// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"

	"gorm.io/gorm"
)

const TableNameEvaluationLocationOverride = "evaluation_location_override"

// EvaluationLocationOverride mapped from table <evaluation_location_override>
type EvaluationLocationOverride struct {
	ID           int64          `gorm:"column:id;not null" json:"id"`
	EvaluationID int64          `gorm:"column:evaluation_id;not null" json:"evaluation_id"`
	BusinessID   int64          `gorm:"column:business_id;not null" json:"business_id"`
	Price        *float64       `gorm:"column:price" json:"price"`
	Duration     *int32         `gorm:"column:duration" json:"duration"`
	CreatedAt    *time.Time     `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    *time.Time     `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
}

// TableName EvaluationLocationOverride's table name
func (*EvaluationLocationOverride) TableName() string {
	return TableNameEvaluationLocationOverride
}
