package models

import (
	"time"

	"gorm.io/gorm"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
)

type PricingRuleRecordWhereOpt struct {
	ID          *int64                             `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	IDs         []int64                            `gorm:"column:id;query_expr:in"`
	ExcludedIDs []int64                            `gorm:"column:id;query_expr:not in"`
	RuleName    *string                            `gorm:"column:rule_name;not null" json:"rule_name"`
	CompanyID   int64                              `gorm:"column:company_id;not null" json:"company_id"`
	RuleType    *offeringModelsV2.RuleType         `gorm:"column:rule_type;not null;comment:rule type, 1-multi pets, 2-multi duration, 3-peak date" json:"rule_type"` // rule type, 1-multi pets, 2-multi duration, 3-peak date
	RuleTypes   []offeringModelsV2.RuleType        `gorm:"column:rule_type;query_expr:in;not null;comment:rule type, 1-multi pets, 2-multi duration, 3-peak date" json:"rule_types"`
	IsActive    *bool                              `gorm:"column:is_active;not null" json:"is_active"`
	CareTypes   []offeringModelsV1.ServiceItemType `json:"care_types"`
}

type PricingRuleRecordUpdateOpt struct {
	RuleName                 *string                                    `gorm:"column:rule_name;not null" json:"rule_name"`
	AllBoardingApplicable    *bool                                      `gorm:"column:all_boarding_applicable;not null" json:"all_boarding_applicable"`
	SelectedBoardingServices *[]int64                                   `gorm:"column:selected_boarding_services;default:[];serializer:json" json:"selected_boarding_services"`
	AllDaycareApplicable     *bool                                      `gorm:"column:all_daycare_applicable;not null" json:"all_daycare_applicable"`
	SelectedDaycareServices  *[]int64                                   `gorm:"column:selected_daycare_services;default:[];serializer:json" json:"selected_daycare_services"`
	RuleApplyType            offeringModelsV2.RuleApplyType             `gorm:"column:rule_apply_type;not null;comment:rule apply type, 1-each pet, 2-additional pets, 3-all pets" json:"rule_apply_type"` // rule apply type, 1-each pet, 2-additional pets, 3-all pets
	NeedInSameLodging        *bool                                      `gorm:"column:need_in_same_lodging;not null" json:"need_in_same_lodging"`
	RuleConfiguration        *offeringModelsV2.PricingRuleConfiguration `gorm:"column:rule_configuration;default:{};serializer:proto_json" json:"rule_configuration"`
	IsActive                 *bool                                      `gorm:"column:is_active;not null" json:"is_active"`
	UpdatedBy                *int64                                     `gorm:"column:updated_by;not null" json:"updated_by"`
	UpdatedAt                time.Time                                  `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	IsChargePerLodging       *bool                                      `gorm:"column:is_charge_per_lodging;not null" json:"is_charge_per_lodging"`
	Source                   *offeringModelsV2.Source                   `gorm:"column:source;not null;default:1;comment:1-MoeGo Platform 2-Enterprise Hub" json:"source"` // 1-MoeGo Platform 2-Enterprise Hub
	AllGroomingApplicable    *bool                                      `gorm:"column:all_grooming_applicable;not null" json:"all_grooming_applicable"`
	SelectedGroomingServices *[]int64                                   `gorm:"column:selected_grooming_services;default:[];serializer:json" json:"selected_grooming_services"`
	AllAddonApplicable       *bool                                      `gorm:"column:all_addon_applicable;not null" json:"all_addon_applicable"`
	SelectedAddonServices    *[]int64                                   `gorm:"column:selected_addon_services;default:[];serializer:json" json:"selected_addon_services"`
}

type PricingRuleRecordDeleteOpt struct {
	UpdatedBy int64          `gorm:"column:updated_by;not null" json:"updated_by"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
}
