// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	"time"

	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/lib/pq"
	"gorm.io/gorm"
)

const TableNameLodgingType = "lodging_type"

// LodgingType mapped from table <lodging_type>
type LodgingType struct {
	ID                 int64                      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	CompanyID          int64                      `gorm:"column:company_id;not null" json:"company_id"`
	Description        string                     `gorm:"column:description;not null" json:"description"`
	Name               string                     `gorm:"column:name;not null" json:"name"`
	Photo              []string                   `gorm:"column:photo;not null;default:[];comment:photo list;serializer:json" json:"photo"`                          // photo list
	MaxPetNum          int32                      `gorm:"column:max_pet_num;not null;comment:maximum number of pets the lodging can accommodate" json:"max_pet_num"` // maximum number of pets the lodging can accommodate
	CreatedAt          *time.Time                 `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	CreatedBy          int64                      `gorm:"column:created_by;not null" json:"created_by"`
	UpdatedAt          *time.Time                 `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP;autoUpdateTime:false" json:"updated_at"`
	UpdatedBy          int64                      `gorm:"column:updated_by;not null" json:"updated_by"`
	DeletedAt          gorm.DeletedAt             `gorm:"column:deleted_at" json:"deleted_at"`
	DeletedBy          int64                      `gorm:"column:deleted_by;not null" json:"deleted_by"`
	PetSizeFilter      bool                       `gorm:"column:pet_size_filter;not null;comment:if need pet size filter" json:"pet_size_filter"`                                                                           // if need pet size filter
	AllowedPetSizeList pq.Int64Array              `gorm:"column:allowed_pet_size_list;type:bigint[];not null;default:ARRAY[];comment:allowed pet size list, only when service_filter is true" json:"allowed_pet_size_list"` // allowed pet size list, only when service_filter is true
	Type               v1.LodgingUnitType         `gorm:"column:type;not null;default:2;comment:lodging 类型：1-Room/kennel type；2-Area type" json:"type"`                                                                     // lodging 类型：1-Room/kennel type；2-Area type
	Sort               int32                      `gorm:"column:sort;not null" json:"sort"`
	Source             v1.LodgingTypeModel_Source `gorm:"column:source;not null;default:1;comment:1-MoeGo Platform 2-Enterprise Hub" json:"source"` // 1-MoeGo Platform 2-Enterprise Hub
}

// TableName LodgingType's table name
func (*LodgingType) TableName() string {
	return TableNameLodgingType
}
