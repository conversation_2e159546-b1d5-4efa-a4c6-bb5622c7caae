// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newEvaluationLocationOverride(db *gorm.DB, opts ...gen.DOOption) evaluationLocationOverride {
	_evaluationLocationOverride := evaluationLocationOverride{}

	_evaluationLocationOverride.evaluationLocationOverrideDo.UseDB(db, opts...)
	_evaluationLocationOverride.evaluationLocationOverrideDo.UseModel(&models.EvaluationLocationOverride{})

	tableName := _evaluationLocationOverride.evaluationLocationOverrideDo.TableName()
	_evaluationLocationOverride.ALL = field.NewAsterisk(tableName)
	_evaluationLocationOverride.ID = field.NewInt64(tableName, "id")
	_evaluationLocationOverride.EvaluationID = field.NewInt64(tableName, "evaluation_id")
	_evaluationLocationOverride.BusinessID = field.NewInt64(tableName, "business_id")
	_evaluationLocationOverride.Price = field.NewFloat64(tableName, "price")
	_evaluationLocationOverride.Duration = field.NewInt32(tableName, "duration")
	_evaluationLocationOverride.CreatedAt = field.NewTime(tableName, "created_at")
	_evaluationLocationOverride.UpdatedAt = field.NewTime(tableName, "updated_at")
	_evaluationLocationOverride.DeletedAt = field.NewField(tableName, "deleted_at")

	_evaluationLocationOverride.fillFieldMap()

	return _evaluationLocationOverride
}

type evaluationLocationOverride struct {
	evaluationLocationOverrideDo evaluationLocationOverrideDo

	ALL          field.Asterisk
	ID           field.Int64
	EvaluationID field.Int64
	BusinessID   field.Int64
	Price        field.Float64
	Duration     field.Int32
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field

	fieldMap map[string]field.Expr
}

func (e evaluationLocationOverride) Table(newTableName string) *evaluationLocationOverride {
	e.evaluationLocationOverrideDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e evaluationLocationOverride) As(alias string) *evaluationLocationOverride {
	e.evaluationLocationOverrideDo.DO = *(e.evaluationLocationOverrideDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *evaluationLocationOverride) updateTableName(table string) *evaluationLocationOverride {
	e.ALL = field.NewAsterisk(table)
	e.ID = field.NewInt64(table, "id")
	e.EvaluationID = field.NewInt64(table, "evaluation_id")
	e.BusinessID = field.NewInt64(table, "business_id")
	e.Price = field.NewFloat64(table, "price")
	e.Duration = field.NewInt32(table, "duration")
	e.CreatedAt = field.NewTime(table, "created_at")
	e.UpdatedAt = field.NewTime(table, "updated_at")
	e.DeletedAt = field.NewField(table, "deleted_at")

	e.fillFieldMap()

	return e
}

func (e *evaluationLocationOverride) WithContext(ctx context.Context) IEvaluationLocationOverrideDo {
	return e.evaluationLocationOverrideDo.WithContext(ctx)
}

func (e evaluationLocationOverride) TableName() string {
	return e.evaluationLocationOverrideDo.TableName()
}

func (e evaluationLocationOverride) Alias() string { return e.evaluationLocationOverrideDo.Alias() }

func (e evaluationLocationOverride) Columns(cols ...field.Expr) gen.Columns {
	return e.evaluationLocationOverrideDo.Columns(cols...)
}

func (e *evaluationLocationOverride) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *evaluationLocationOverride) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 8)
	e.fieldMap["id"] = e.ID
	e.fieldMap["evaluation_id"] = e.EvaluationID
	e.fieldMap["business_id"] = e.BusinessID
	e.fieldMap["price"] = e.Price
	e.fieldMap["duration"] = e.Duration
	e.fieldMap["created_at"] = e.CreatedAt
	e.fieldMap["updated_at"] = e.UpdatedAt
	e.fieldMap["deleted_at"] = e.DeletedAt
}

func (e evaluationLocationOverride) clone(db *gorm.DB) evaluationLocationOverride {
	e.evaluationLocationOverrideDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e evaluationLocationOverride) replaceDB(db *gorm.DB) evaluationLocationOverride {
	e.evaluationLocationOverrideDo.ReplaceDB(db)
	return e
}

type evaluationLocationOverrideDo struct{ gen.DO }

type IEvaluationLocationOverrideDo interface {
	gen.SubQuery
	Debug() IEvaluationLocationOverrideDo
	WithContext(ctx context.Context) IEvaluationLocationOverrideDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IEvaluationLocationOverrideDo
	WriteDB() IEvaluationLocationOverrideDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IEvaluationLocationOverrideDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IEvaluationLocationOverrideDo
	Not(conds ...gen.Condition) IEvaluationLocationOverrideDo
	Or(conds ...gen.Condition) IEvaluationLocationOverrideDo
	Select(conds ...field.Expr) IEvaluationLocationOverrideDo
	Where(conds ...gen.Condition) IEvaluationLocationOverrideDo
	Order(conds ...field.Expr) IEvaluationLocationOverrideDo
	Distinct(cols ...field.Expr) IEvaluationLocationOverrideDo
	Omit(cols ...field.Expr) IEvaluationLocationOverrideDo
	Join(table schema.Tabler, on ...field.Expr) IEvaluationLocationOverrideDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IEvaluationLocationOverrideDo
	RightJoin(table schema.Tabler, on ...field.Expr) IEvaluationLocationOverrideDo
	Group(cols ...field.Expr) IEvaluationLocationOverrideDo
	Having(conds ...gen.Condition) IEvaluationLocationOverrideDo
	Limit(limit int) IEvaluationLocationOverrideDo
	Offset(offset int) IEvaluationLocationOverrideDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IEvaluationLocationOverrideDo
	Unscoped() IEvaluationLocationOverrideDo
	Create(values ...*models.EvaluationLocationOverride) error
	CreateInBatches(values []*models.EvaluationLocationOverride, batchSize int) error
	Save(values ...*models.EvaluationLocationOverride) error
	First() (*models.EvaluationLocationOverride, error)
	Take() (*models.EvaluationLocationOverride, error)
	Last() (*models.EvaluationLocationOverride, error)
	Find() ([]*models.EvaluationLocationOverride, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.EvaluationLocationOverride, err error)
	FindInBatches(result *[]*models.EvaluationLocationOverride, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.EvaluationLocationOverride) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IEvaluationLocationOverrideDo
	Assign(attrs ...field.AssignExpr) IEvaluationLocationOverrideDo
	Joins(fields ...field.RelationField) IEvaluationLocationOverrideDo
	Preload(fields ...field.RelationField) IEvaluationLocationOverrideDo
	FirstOrInit() (*models.EvaluationLocationOverride, error)
	FirstOrCreate() (*models.EvaluationLocationOverride, error)
	FindByPage(offset int, limit int) (result []*models.EvaluationLocationOverride, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IEvaluationLocationOverrideDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (e evaluationLocationOverrideDo) Debug() IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Debug())
}

func (e evaluationLocationOverrideDo) WithContext(ctx context.Context) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e evaluationLocationOverrideDo) ReadDB() IEvaluationLocationOverrideDo {
	return e.Clauses(dbresolver.Read)
}

func (e evaluationLocationOverrideDo) WriteDB() IEvaluationLocationOverrideDo {
	return e.Clauses(dbresolver.Write)
}

func (e evaluationLocationOverrideDo) Session(config *gorm.Session) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Session(config))
}

func (e evaluationLocationOverrideDo) Clauses(conds ...clause.Expression) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e evaluationLocationOverrideDo) Returning(value interface{}, columns ...string) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e evaluationLocationOverrideDo) Not(conds ...gen.Condition) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e evaluationLocationOverrideDo) Or(conds ...gen.Condition) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e evaluationLocationOverrideDo) Select(conds ...field.Expr) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e evaluationLocationOverrideDo) Where(conds ...gen.Condition) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e evaluationLocationOverrideDo) Order(conds ...field.Expr) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e evaluationLocationOverrideDo) Distinct(cols ...field.Expr) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e evaluationLocationOverrideDo) Omit(cols ...field.Expr) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e evaluationLocationOverrideDo) Join(table schema.Tabler, on ...field.Expr) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e evaluationLocationOverrideDo) LeftJoin(table schema.Tabler, on ...field.Expr) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e evaluationLocationOverrideDo) RightJoin(table schema.Tabler, on ...field.Expr) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e evaluationLocationOverrideDo) Group(cols ...field.Expr) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e evaluationLocationOverrideDo) Having(conds ...gen.Condition) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e evaluationLocationOverrideDo) Limit(limit int) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e evaluationLocationOverrideDo) Offset(offset int) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e evaluationLocationOverrideDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e evaluationLocationOverrideDo) Unscoped() IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Unscoped())
}

func (e evaluationLocationOverrideDo) Create(values ...*models.EvaluationLocationOverride) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e evaluationLocationOverrideDo) CreateInBatches(values []*models.EvaluationLocationOverride, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e evaluationLocationOverrideDo) Save(values ...*models.EvaluationLocationOverride) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e evaluationLocationOverrideDo) First() (*models.EvaluationLocationOverride, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.EvaluationLocationOverride), nil
	}
}

func (e evaluationLocationOverrideDo) Take() (*models.EvaluationLocationOverride, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.EvaluationLocationOverride), nil
	}
}

func (e evaluationLocationOverrideDo) Last() (*models.EvaluationLocationOverride, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.EvaluationLocationOverride), nil
	}
}

func (e evaluationLocationOverrideDo) Find() ([]*models.EvaluationLocationOverride, error) {
	result, err := e.DO.Find()
	return result.([]*models.EvaluationLocationOverride), err
}

func (e evaluationLocationOverrideDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.EvaluationLocationOverride, err error) {
	buf := make([]*models.EvaluationLocationOverride, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e evaluationLocationOverrideDo) FindInBatches(result *[]*models.EvaluationLocationOverride, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e evaluationLocationOverrideDo) Attrs(attrs ...field.AssignExpr) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e evaluationLocationOverrideDo) Assign(attrs ...field.AssignExpr) IEvaluationLocationOverrideDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e evaluationLocationOverrideDo) Joins(fields ...field.RelationField) IEvaluationLocationOverrideDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e evaluationLocationOverrideDo) Preload(fields ...field.RelationField) IEvaluationLocationOverrideDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e evaluationLocationOverrideDo) FirstOrInit() (*models.EvaluationLocationOverride, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.EvaluationLocationOverride), nil
	}
}

func (e evaluationLocationOverrideDo) FirstOrCreate() (*models.EvaluationLocationOverride, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.EvaluationLocationOverride), nil
	}
}

func (e evaluationLocationOverrideDo) FindByPage(offset int, limit int) (result []*models.EvaluationLocationOverride, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e evaluationLocationOverrideDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e evaluationLocationOverrideDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e evaluationLocationOverrideDo) Delete(models ...*models.EvaluationLocationOverride) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *evaluationLocationOverrideDo) withDO(do gen.Dao) *evaluationLocationOverrideDo {
	e.DO = *do.(*gen.DO)
	return e
}
