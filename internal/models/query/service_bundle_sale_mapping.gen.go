// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newServiceBundleSaleMapping(db *gorm.DB, opts ...gen.DOOption) serviceBundleSaleMapping {
	_serviceBundleSaleMapping := serviceBundleSaleMapping{}

	_serviceBundleSaleMapping.serviceBundleSaleMappingDo.UseDB(db, opts...)
	_serviceBundleSaleMapping.serviceBundleSaleMappingDo.UseModel(&models.ServiceBundleSaleMapping{})

	tableName := _serviceBundleSaleMapping.serviceBundleSaleMappingDo.TableName()
	_serviceBundleSaleMapping.ALL = field.NewAsterisk(tableName)
	_serviceBundleSaleMapping.ID = field.NewInt64(tableName, "id")
	_serviceBundleSaleMapping.CompanyID = field.NewInt64(tableName, "company_id")
	_serviceBundleSaleMapping.ServiceID = field.NewInt64(tableName, "service_id")
	_serviceBundleSaleMapping.BundleServiceID = field.NewInt64(tableName, "bundle_service_id")
	_serviceBundleSaleMapping.CreatedAt = field.NewTime(tableName, "created_at")

	_serviceBundleSaleMapping.fillFieldMap()

	return _serviceBundleSaleMapping
}

type serviceBundleSaleMapping struct {
	serviceBundleSaleMappingDo serviceBundleSaleMappingDo

	ALL             field.Asterisk
	ID              field.Int64
	CompanyID       field.Int64
	ServiceID       field.Int64
	BundleServiceID field.Int64
	CreatedAt       field.Time

	fieldMap map[string]field.Expr
}

func (s serviceBundleSaleMapping) Table(newTableName string) *serviceBundleSaleMapping {
	s.serviceBundleSaleMappingDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceBundleSaleMapping) As(alias string) *serviceBundleSaleMapping {
	s.serviceBundleSaleMappingDo.DO = *(s.serviceBundleSaleMappingDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceBundleSaleMapping) updateTableName(table string) *serviceBundleSaleMapping {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.CompanyID = field.NewInt64(table, "company_id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.BundleServiceID = field.NewInt64(table, "bundle_service_id")
	s.CreatedAt = field.NewTime(table, "created_at")

	s.fillFieldMap()

	return s
}

func (s *serviceBundleSaleMapping) WithContext(ctx context.Context) IServiceBundleSaleMappingDo {
	return s.serviceBundleSaleMappingDo.WithContext(ctx)
}

func (s serviceBundleSaleMapping) TableName() string { return s.serviceBundleSaleMappingDo.TableName() }

func (s serviceBundleSaleMapping) Alias() string { return s.serviceBundleSaleMappingDo.Alias() }

func (s serviceBundleSaleMapping) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceBundleSaleMappingDo.Columns(cols...)
}

func (s *serviceBundleSaleMapping) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceBundleSaleMapping) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 5)
	s.fieldMap["id"] = s.ID
	s.fieldMap["company_id"] = s.CompanyID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["bundle_service_id"] = s.BundleServiceID
	s.fieldMap["created_at"] = s.CreatedAt
}

func (s serviceBundleSaleMapping) clone(db *gorm.DB) serviceBundleSaleMapping {
	s.serviceBundleSaleMappingDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceBundleSaleMapping) replaceDB(db *gorm.DB) serviceBundleSaleMapping {
	s.serviceBundleSaleMappingDo.ReplaceDB(db)
	return s
}

type serviceBundleSaleMappingDo struct{ gen.DO }

type IServiceBundleSaleMappingDo interface {
	gen.SubQuery
	Debug() IServiceBundleSaleMappingDo
	WithContext(ctx context.Context) IServiceBundleSaleMappingDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IServiceBundleSaleMappingDo
	WriteDB() IServiceBundleSaleMappingDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IServiceBundleSaleMappingDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IServiceBundleSaleMappingDo
	Not(conds ...gen.Condition) IServiceBundleSaleMappingDo
	Or(conds ...gen.Condition) IServiceBundleSaleMappingDo
	Select(conds ...field.Expr) IServiceBundleSaleMappingDo
	Where(conds ...gen.Condition) IServiceBundleSaleMappingDo
	Order(conds ...field.Expr) IServiceBundleSaleMappingDo
	Distinct(cols ...field.Expr) IServiceBundleSaleMappingDo
	Omit(cols ...field.Expr) IServiceBundleSaleMappingDo
	Join(table schema.Tabler, on ...field.Expr) IServiceBundleSaleMappingDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IServiceBundleSaleMappingDo
	RightJoin(table schema.Tabler, on ...field.Expr) IServiceBundleSaleMappingDo
	Group(cols ...field.Expr) IServiceBundleSaleMappingDo
	Having(conds ...gen.Condition) IServiceBundleSaleMappingDo
	Limit(limit int) IServiceBundleSaleMappingDo
	Offset(offset int) IServiceBundleSaleMappingDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IServiceBundleSaleMappingDo
	Unscoped() IServiceBundleSaleMappingDo
	Create(values ...*models.ServiceBundleSaleMapping) error
	CreateInBatches(values []*models.ServiceBundleSaleMapping, batchSize int) error
	Save(values ...*models.ServiceBundleSaleMapping) error
	First() (*models.ServiceBundleSaleMapping, error)
	Take() (*models.ServiceBundleSaleMapping, error)
	Last() (*models.ServiceBundleSaleMapping, error)
	Find() ([]*models.ServiceBundleSaleMapping, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.ServiceBundleSaleMapping, err error)
	FindInBatches(result *[]*models.ServiceBundleSaleMapping, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.ServiceBundleSaleMapping) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IServiceBundleSaleMappingDo
	Assign(attrs ...field.AssignExpr) IServiceBundleSaleMappingDo
	Joins(fields ...field.RelationField) IServiceBundleSaleMappingDo
	Preload(fields ...field.RelationField) IServiceBundleSaleMappingDo
	FirstOrInit() (*models.ServiceBundleSaleMapping, error)
	FirstOrCreate() (*models.ServiceBundleSaleMapping, error)
	FindByPage(offset int, limit int) (result []*models.ServiceBundleSaleMapping, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IServiceBundleSaleMappingDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s serviceBundleSaleMappingDo) Debug() IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceBundleSaleMappingDo) WithContext(ctx context.Context) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceBundleSaleMappingDo) ReadDB() IServiceBundleSaleMappingDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceBundleSaleMappingDo) WriteDB() IServiceBundleSaleMappingDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceBundleSaleMappingDo) Session(config *gorm.Session) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceBundleSaleMappingDo) Clauses(conds ...clause.Expression) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceBundleSaleMappingDo) Returning(value interface{}, columns ...string) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceBundleSaleMappingDo) Not(conds ...gen.Condition) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceBundleSaleMappingDo) Or(conds ...gen.Condition) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceBundleSaleMappingDo) Select(conds ...field.Expr) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceBundleSaleMappingDo) Where(conds ...gen.Condition) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceBundleSaleMappingDo) Order(conds ...field.Expr) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceBundleSaleMappingDo) Distinct(cols ...field.Expr) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceBundleSaleMappingDo) Omit(cols ...field.Expr) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceBundleSaleMappingDo) Join(table schema.Tabler, on ...field.Expr) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceBundleSaleMappingDo) LeftJoin(table schema.Tabler, on ...field.Expr) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceBundleSaleMappingDo) RightJoin(table schema.Tabler, on ...field.Expr) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceBundleSaleMappingDo) Group(cols ...field.Expr) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceBundleSaleMappingDo) Having(conds ...gen.Condition) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceBundleSaleMappingDo) Limit(limit int) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceBundleSaleMappingDo) Offset(offset int) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceBundleSaleMappingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceBundleSaleMappingDo) Unscoped() IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceBundleSaleMappingDo) Create(values ...*models.ServiceBundleSaleMapping) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceBundleSaleMappingDo) CreateInBatches(values []*models.ServiceBundleSaleMapping, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceBundleSaleMappingDo) Save(values ...*models.ServiceBundleSaleMapping) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceBundleSaleMappingDo) First() (*models.ServiceBundleSaleMapping, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceBundleSaleMapping), nil
	}
}

func (s serviceBundleSaleMappingDo) Take() (*models.ServiceBundleSaleMapping, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceBundleSaleMapping), nil
	}
}

func (s serviceBundleSaleMappingDo) Last() (*models.ServiceBundleSaleMapping, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceBundleSaleMapping), nil
	}
}

func (s serviceBundleSaleMappingDo) Find() ([]*models.ServiceBundleSaleMapping, error) {
	result, err := s.DO.Find()
	return result.([]*models.ServiceBundleSaleMapping), err
}

func (s serviceBundleSaleMappingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.ServiceBundleSaleMapping, err error) {
	buf := make([]*models.ServiceBundleSaleMapping, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceBundleSaleMappingDo) FindInBatches(result *[]*models.ServiceBundleSaleMapping, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceBundleSaleMappingDo) Attrs(attrs ...field.AssignExpr) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceBundleSaleMappingDo) Assign(attrs ...field.AssignExpr) IServiceBundleSaleMappingDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceBundleSaleMappingDo) Joins(fields ...field.RelationField) IServiceBundleSaleMappingDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceBundleSaleMappingDo) Preload(fields ...field.RelationField) IServiceBundleSaleMappingDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceBundleSaleMappingDo) FirstOrInit() (*models.ServiceBundleSaleMapping, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceBundleSaleMapping), nil
	}
}

func (s serviceBundleSaleMappingDo) FirstOrCreate() (*models.ServiceBundleSaleMapping, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceBundleSaleMapping), nil
	}
}

func (s serviceBundleSaleMappingDo) FindByPage(offset int, limit int) (result []*models.ServiceBundleSaleMapping, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceBundleSaleMappingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceBundleSaleMappingDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceBundleSaleMappingDo) Delete(models ...*models.ServiceBundleSaleMapping) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceBundleSaleMappingDo) withDO(do gen.Dao) *serviceBundleSaleMappingDo {
	s.DO = *do.(*gen.DO)
	return s
}
