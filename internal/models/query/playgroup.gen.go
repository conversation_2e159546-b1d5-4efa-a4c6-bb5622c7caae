// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newPlaygroup(db *gorm.DB, opts ...gen.DOOption) playgroup {
	_playgroup := playgroup{}

	_playgroup.playgroupDo.UseDB(db, opts...)
	_playgroup.playgroupDo.UseModel(&models.Playgroup{})

	tableName := _playgroup.playgroupDo.TableName()
	_playgroup.ALL = field.NewAsterisk(tableName)
	_playgroup.ID = field.NewInt64(tableName, "id")
	_playgroup.CompanyID = field.NewInt64(tableName, "company_id")
	_playgroup.Name = field.NewString(tableName, "name")
	_playgroup.ColorCode = field.NewString(tableName, "color_code")
	_playgroup.MaxPetCapacity = field.NewInt32(tableName, "max_pet_capacity")
	_playgroup.Sort = field.NewInt32(tableName, "sort")
	_playgroup.Description = field.NewString(tableName, "description")
	_playgroup.CreatedAt = field.NewTime(tableName, "created_at")
	_playgroup.UpdatedAt = field.NewTime(tableName, "updated_at")
	_playgroup.CreatedBy = field.NewInt64(tableName, "created_by")
	_playgroup.UpdatedBy = field.NewInt64(tableName, "updated_by")
	_playgroup.DeletedAt = field.NewField(tableName, "deleted_at")
	_playgroup.DeletedBy = field.NewInt64(tableName, "deleted_by")

	_playgroup.fillFieldMap()

	return _playgroup
}

type playgroup struct {
	playgroupDo playgroupDo

	ALL            field.Asterisk
	ID             field.Int64
	CompanyID      field.Int64 // company id
	Name           field.String
	ColorCode      field.String // playgroup color code
	MaxPetCapacity field.Int32  // max pet capacity
	Sort           field.Int32  // Playgroup list sort. Start with 1 and put the smallest first
	Description    field.String
	CreatedAt      field.Time
	UpdatedAt      field.Time
	CreatedBy      field.Int64
	UpdatedBy      field.Int64
	DeletedAt      field.Field
	DeletedBy      field.Int64

	fieldMap map[string]field.Expr
}

func (p playgroup) Table(newTableName string) *playgroup {
	p.playgroupDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p playgroup) As(alias string) *playgroup {
	p.playgroupDo.DO = *(p.playgroupDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *playgroup) updateTableName(table string) *playgroup {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.CompanyID = field.NewInt64(table, "company_id")
	p.Name = field.NewString(table, "name")
	p.ColorCode = field.NewString(table, "color_code")
	p.MaxPetCapacity = field.NewInt32(table, "max_pet_capacity")
	p.Sort = field.NewInt32(table, "sort")
	p.Description = field.NewString(table, "description")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.CreatedBy = field.NewInt64(table, "created_by")
	p.UpdatedBy = field.NewInt64(table, "updated_by")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.DeletedBy = field.NewInt64(table, "deleted_by")

	p.fillFieldMap()

	return p
}

func (p *playgroup) WithContext(ctx context.Context) IPlaygroupDo {
	return p.playgroupDo.WithContext(ctx)
}

func (p playgroup) TableName() string { return p.playgroupDo.TableName() }

func (p playgroup) Alias() string { return p.playgroupDo.Alias() }

func (p playgroup) Columns(cols ...field.Expr) gen.Columns { return p.playgroupDo.Columns(cols...) }

func (p *playgroup) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *playgroup) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 13)
	p.fieldMap["id"] = p.ID
	p.fieldMap["company_id"] = p.CompanyID
	p.fieldMap["name"] = p.Name
	p.fieldMap["color_code"] = p.ColorCode
	p.fieldMap["max_pet_capacity"] = p.MaxPetCapacity
	p.fieldMap["sort"] = p.Sort
	p.fieldMap["description"] = p.Description
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["created_by"] = p.CreatedBy
	p.fieldMap["updated_by"] = p.UpdatedBy
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["deleted_by"] = p.DeletedBy
}

func (p playgroup) clone(db *gorm.DB) playgroup {
	p.playgroupDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p playgroup) replaceDB(db *gorm.DB) playgroup {
	p.playgroupDo.ReplaceDB(db)
	return p
}

type playgroupDo struct{ gen.DO }

type IPlaygroupDo interface {
	gen.SubQuery
	Debug() IPlaygroupDo
	WithContext(ctx context.Context) IPlaygroupDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPlaygroupDo
	WriteDB() IPlaygroupDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPlaygroupDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPlaygroupDo
	Not(conds ...gen.Condition) IPlaygroupDo
	Or(conds ...gen.Condition) IPlaygroupDo
	Select(conds ...field.Expr) IPlaygroupDo
	Where(conds ...gen.Condition) IPlaygroupDo
	Order(conds ...field.Expr) IPlaygroupDo
	Distinct(cols ...field.Expr) IPlaygroupDo
	Omit(cols ...field.Expr) IPlaygroupDo
	Join(table schema.Tabler, on ...field.Expr) IPlaygroupDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPlaygroupDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPlaygroupDo
	Group(cols ...field.Expr) IPlaygroupDo
	Having(conds ...gen.Condition) IPlaygroupDo
	Limit(limit int) IPlaygroupDo
	Offset(offset int) IPlaygroupDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPlaygroupDo
	Unscoped() IPlaygroupDo
	Create(values ...*models.Playgroup) error
	CreateInBatches(values []*models.Playgroup, batchSize int) error
	Save(values ...*models.Playgroup) error
	First() (*models.Playgroup, error)
	Take() (*models.Playgroup, error)
	Last() (*models.Playgroup, error)
	Find() ([]*models.Playgroup, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Playgroup, err error)
	FindInBatches(result *[]*models.Playgroup, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Playgroup) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPlaygroupDo
	Assign(attrs ...field.AssignExpr) IPlaygroupDo
	Joins(fields ...field.RelationField) IPlaygroupDo
	Preload(fields ...field.RelationField) IPlaygroupDo
	FirstOrInit() (*models.Playgroup, error)
	FirstOrCreate() (*models.Playgroup, error)
	FindByPage(offset int, limit int) (result []*models.Playgroup, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPlaygroupDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p playgroupDo) Debug() IPlaygroupDo {
	return p.withDO(p.DO.Debug())
}

func (p playgroupDo) WithContext(ctx context.Context) IPlaygroupDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p playgroupDo) ReadDB() IPlaygroupDo {
	return p.Clauses(dbresolver.Read)
}

func (p playgroupDo) WriteDB() IPlaygroupDo {
	return p.Clauses(dbresolver.Write)
}

func (p playgroupDo) Session(config *gorm.Session) IPlaygroupDo {
	return p.withDO(p.DO.Session(config))
}

func (p playgroupDo) Clauses(conds ...clause.Expression) IPlaygroupDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p playgroupDo) Returning(value interface{}, columns ...string) IPlaygroupDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p playgroupDo) Not(conds ...gen.Condition) IPlaygroupDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p playgroupDo) Or(conds ...gen.Condition) IPlaygroupDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p playgroupDo) Select(conds ...field.Expr) IPlaygroupDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p playgroupDo) Where(conds ...gen.Condition) IPlaygroupDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p playgroupDo) Order(conds ...field.Expr) IPlaygroupDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p playgroupDo) Distinct(cols ...field.Expr) IPlaygroupDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p playgroupDo) Omit(cols ...field.Expr) IPlaygroupDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p playgroupDo) Join(table schema.Tabler, on ...field.Expr) IPlaygroupDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p playgroupDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPlaygroupDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p playgroupDo) RightJoin(table schema.Tabler, on ...field.Expr) IPlaygroupDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p playgroupDo) Group(cols ...field.Expr) IPlaygroupDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p playgroupDo) Having(conds ...gen.Condition) IPlaygroupDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p playgroupDo) Limit(limit int) IPlaygroupDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p playgroupDo) Offset(offset int) IPlaygroupDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p playgroupDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPlaygroupDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p playgroupDo) Unscoped() IPlaygroupDo {
	return p.withDO(p.DO.Unscoped())
}

func (p playgroupDo) Create(values ...*models.Playgroup) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p playgroupDo) CreateInBatches(values []*models.Playgroup, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p playgroupDo) Save(values ...*models.Playgroup) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p playgroupDo) First() (*models.Playgroup, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Playgroup), nil
	}
}

func (p playgroupDo) Take() (*models.Playgroup, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Playgroup), nil
	}
}

func (p playgroupDo) Last() (*models.Playgroup, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Playgroup), nil
	}
}

func (p playgroupDo) Find() ([]*models.Playgroup, error) {
	result, err := p.DO.Find()
	return result.([]*models.Playgroup), err
}

func (p playgroupDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Playgroup, err error) {
	buf := make([]*models.Playgroup, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p playgroupDo) FindInBatches(result *[]*models.Playgroup, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p playgroupDo) Attrs(attrs ...field.AssignExpr) IPlaygroupDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p playgroupDo) Assign(attrs ...field.AssignExpr) IPlaygroupDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p playgroupDo) Joins(fields ...field.RelationField) IPlaygroupDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p playgroupDo) Preload(fields ...field.RelationField) IPlaygroupDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p playgroupDo) FirstOrInit() (*models.Playgroup, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Playgroup), nil
	}
}

func (p playgroupDo) FirstOrCreate() (*models.Playgroup, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Playgroup), nil
	}
}

func (p playgroupDo) FindByPage(offset int, limit int) (result []*models.Playgroup, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p playgroupDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p playgroupDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p playgroupDo) Delete(models ...*models.Playgroup) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *playgroupDo) withDO(do gen.Dao) *playgroupDo {
	p.DO = *do.(*gen.DO)
	return p
}
