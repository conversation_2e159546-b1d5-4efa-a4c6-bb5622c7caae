// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newDiscountSetting(db *gorm.DB, opts ...gen.DOOption) discountSetting {
	_discountSetting := discountSetting{}

	_discountSetting.discountSettingDo.UseDB(db, opts...)
	_discountSetting.discountSettingDo.UseModel(&models.DiscountSetting{})

	tableName := _discountSetting.discountSettingDo.TableName()
	_discountSetting.ALL = field.NewAsterisk(tableName)
	_discountSetting.ID = field.NewInt64(tableName, "id")
	_discountSetting.CompanyID = field.NewInt64(tableName, "company_id")
	_discountSetting.UpdatedBy = field.NewInt64(tableName, "updated_by")
	_discountSetting.CreatedAt = field.NewTime(tableName, "created_at")
	_discountSetting.UpdatedAt = field.NewTime(tableName, "updated_at")
	_discountSetting.ApplyBestOnly = field.NewBool(tableName, "apply_best_only")
	_discountSetting.ApplySequence = field.NewField(tableName, "apply_sequence")

	_discountSetting.fillFieldMap()

	return _discountSetting
}

type discountSetting struct {
	discountSettingDo discountSettingDo

	ALL           field.Asterisk
	ID            field.Int64
	CompanyID     field.Int64
	UpdatedBy     field.Int64
	CreatedAt     field.Time
	UpdatedAt     field.Time
	ApplyBestOnly field.Bool
	ApplySequence field.Field

	fieldMap map[string]field.Expr
}

func (d discountSetting) Table(newTableName string) *discountSetting {
	d.discountSettingDo.UseTable(newTableName)
	return d.updateTableName(newTableName)
}

func (d discountSetting) As(alias string) *discountSetting {
	d.discountSettingDo.DO = *(d.discountSettingDo.As(alias).(*gen.DO))
	return d.updateTableName(alias)
}

func (d *discountSetting) updateTableName(table string) *discountSetting {
	d.ALL = field.NewAsterisk(table)
	d.ID = field.NewInt64(table, "id")
	d.CompanyID = field.NewInt64(table, "company_id")
	d.UpdatedBy = field.NewInt64(table, "updated_by")
	d.CreatedAt = field.NewTime(table, "created_at")
	d.UpdatedAt = field.NewTime(table, "updated_at")
	d.ApplyBestOnly = field.NewBool(table, "apply_best_only")
	d.ApplySequence = field.NewField(table, "apply_sequence")

	d.fillFieldMap()

	return d
}

func (d *discountSetting) WithContext(ctx context.Context) IDiscountSettingDo {
	return d.discountSettingDo.WithContext(ctx)
}

func (d discountSetting) TableName() string { return d.discountSettingDo.TableName() }

func (d discountSetting) Alias() string { return d.discountSettingDo.Alias() }

func (d discountSetting) Columns(cols ...field.Expr) gen.Columns {
	return d.discountSettingDo.Columns(cols...)
}

func (d *discountSetting) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := d.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (d *discountSetting) fillFieldMap() {
	d.fieldMap = make(map[string]field.Expr, 7)
	d.fieldMap["id"] = d.ID
	d.fieldMap["company_id"] = d.CompanyID
	d.fieldMap["updated_by"] = d.UpdatedBy
	d.fieldMap["created_at"] = d.CreatedAt
	d.fieldMap["updated_at"] = d.UpdatedAt
	d.fieldMap["apply_best_only"] = d.ApplyBestOnly
	d.fieldMap["apply_sequence"] = d.ApplySequence
}

func (d discountSetting) clone(db *gorm.DB) discountSetting {
	d.discountSettingDo.ReplaceConnPool(db.Statement.ConnPool)
	return d
}

func (d discountSetting) replaceDB(db *gorm.DB) discountSetting {
	d.discountSettingDo.ReplaceDB(db)
	return d
}

type discountSettingDo struct{ gen.DO }

type IDiscountSettingDo interface {
	gen.SubQuery
	Debug() IDiscountSettingDo
	WithContext(ctx context.Context) IDiscountSettingDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IDiscountSettingDo
	WriteDB() IDiscountSettingDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IDiscountSettingDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IDiscountSettingDo
	Not(conds ...gen.Condition) IDiscountSettingDo
	Or(conds ...gen.Condition) IDiscountSettingDo
	Select(conds ...field.Expr) IDiscountSettingDo
	Where(conds ...gen.Condition) IDiscountSettingDo
	Order(conds ...field.Expr) IDiscountSettingDo
	Distinct(cols ...field.Expr) IDiscountSettingDo
	Omit(cols ...field.Expr) IDiscountSettingDo
	Join(table schema.Tabler, on ...field.Expr) IDiscountSettingDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IDiscountSettingDo
	RightJoin(table schema.Tabler, on ...field.Expr) IDiscountSettingDo
	Group(cols ...field.Expr) IDiscountSettingDo
	Having(conds ...gen.Condition) IDiscountSettingDo
	Limit(limit int) IDiscountSettingDo
	Offset(offset int) IDiscountSettingDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IDiscountSettingDo
	Unscoped() IDiscountSettingDo
	Create(values ...*models.DiscountSetting) error
	CreateInBatches(values []*models.DiscountSetting, batchSize int) error
	Save(values ...*models.DiscountSetting) error
	First() (*models.DiscountSetting, error)
	Take() (*models.DiscountSetting, error)
	Last() (*models.DiscountSetting, error)
	Find() ([]*models.DiscountSetting, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.DiscountSetting, err error)
	FindInBatches(result *[]*models.DiscountSetting, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.DiscountSetting) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IDiscountSettingDo
	Assign(attrs ...field.AssignExpr) IDiscountSettingDo
	Joins(fields ...field.RelationField) IDiscountSettingDo
	Preload(fields ...field.RelationField) IDiscountSettingDo
	FirstOrInit() (*models.DiscountSetting, error)
	FirstOrCreate() (*models.DiscountSetting, error)
	FindByPage(offset int, limit int) (result []*models.DiscountSetting, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IDiscountSettingDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (d discountSettingDo) Debug() IDiscountSettingDo {
	return d.withDO(d.DO.Debug())
}

func (d discountSettingDo) WithContext(ctx context.Context) IDiscountSettingDo {
	return d.withDO(d.DO.WithContext(ctx))
}

func (d discountSettingDo) ReadDB() IDiscountSettingDo {
	return d.Clauses(dbresolver.Read)
}

func (d discountSettingDo) WriteDB() IDiscountSettingDo {
	return d.Clauses(dbresolver.Write)
}

func (d discountSettingDo) Session(config *gorm.Session) IDiscountSettingDo {
	return d.withDO(d.DO.Session(config))
}

func (d discountSettingDo) Clauses(conds ...clause.Expression) IDiscountSettingDo {
	return d.withDO(d.DO.Clauses(conds...))
}

func (d discountSettingDo) Returning(value interface{}, columns ...string) IDiscountSettingDo {
	return d.withDO(d.DO.Returning(value, columns...))
}

func (d discountSettingDo) Not(conds ...gen.Condition) IDiscountSettingDo {
	return d.withDO(d.DO.Not(conds...))
}

func (d discountSettingDo) Or(conds ...gen.Condition) IDiscountSettingDo {
	return d.withDO(d.DO.Or(conds...))
}

func (d discountSettingDo) Select(conds ...field.Expr) IDiscountSettingDo {
	return d.withDO(d.DO.Select(conds...))
}

func (d discountSettingDo) Where(conds ...gen.Condition) IDiscountSettingDo {
	return d.withDO(d.DO.Where(conds...))
}

func (d discountSettingDo) Order(conds ...field.Expr) IDiscountSettingDo {
	return d.withDO(d.DO.Order(conds...))
}

func (d discountSettingDo) Distinct(cols ...field.Expr) IDiscountSettingDo {
	return d.withDO(d.DO.Distinct(cols...))
}

func (d discountSettingDo) Omit(cols ...field.Expr) IDiscountSettingDo {
	return d.withDO(d.DO.Omit(cols...))
}

func (d discountSettingDo) Join(table schema.Tabler, on ...field.Expr) IDiscountSettingDo {
	return d.withDO(d.DO.Join(table, on...))
}

func (d discountSettingDo) LeftJoin(table schema.Tabler, on ...field.Expr) IDiscountSettingDo {
	return d.withDO(d.DO.LeftJoin(table, on...))
}

func (d discountSettingDo) RightJoin(table schema.Tabler, on ...field.Expr) IDiscountSettingDo {
	return d.withDO(d.DO.RightJoin(table, on...))
}

func (d discountSettingDo) Group(cols ...field.Expr) IDiscountSettingDo {
	return d.withDO(d.DO.Group(cols...))
}

func (d discountSettingDo) Having(conds ...gen.Condition) IDiscountSettingDo {
	return d.withDO(d.DO.Having(conds...))
}

func (d discountSettingDo) Limit(limit int) IDiscountSettingDo {
	return d.withDO(d.DO.Limit(limit))
}

func (d discountSettingDo) Offset(offset int) IDiscountSettingDo {
	return d.withDO(d.DO.Offset(offset))
}

func (d discountSettingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IDiscountSettingDo {
	return d.withDO(d.DO.Scopes(funcs...))
}

func (d discountSettingDo) Unscoped() IDiscountSettingDo {
	return d.withDO(d.DO.Unscoped())
}

func (d discountSettingDo) Create(values ...*models.DiscountSetting) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Create(values)
}

func (d discountSettingDo) CreateInBatches(values []*models.DiscountSetting, batchSize int) error {
	return d.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (d discountSettingDo) Save(values ...*models.DiscountSetting) error {
	if len(values) == 0 {
		return nil
	}
	return d.DO.Save(values)
}

func (d discountSettingDo) First() (*models.DiscountSetting, error) {
	if result, err := d.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.DiscountSetting), nil
	}
}

func (d discountSettingDo) Take() (*models.DiscountSetting, error) {
	if result, err := d.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.DiscountSetting), nil
	}
}

func (d discountSettingDo) Last() (*models.DiscountSetting, error) {
	if result, err := d.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.DiscountSetting), nil
	}
}

func (d discountSettingDo) Find() ([]*models.DiscountSetting, error) {
	result, err := d.DO.Find()
	return result.([]*models.DiscountSetting), err
}

func (d discountSettingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.DiscountSetting, err error) {
	buf := make([]*models.DiscountSetting, 0, batchSize)
	err = d.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (d discountSettingDo) FindInBatches(result *[]*models.DiscountSetting, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return d.DO.FindInBatches(result, batchSize, fc)
}

func (d discountSettingDo) Attrs(attrs ...field.AssignExpr) IDiscountSettingDo {
	return d.withDO(d.DO.Attrs(attrs...))
}

func (d discountSettingDo) Assign(attrs ...field.AssignExpr) IDiscountSettingDo {
	return d.withDO(d.DO.Assign(attrs...))
}

func (d discountSettingDo) Joins(fields ...field.RelationField) IDiscountSettingDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Joins(_f))
	}
	return &d
}

func (d discountSettingDo) Preload(fields ...field.RelationField) IDiscountSettingDo {
	for _, _f := range fields {
		d = *d.withDO(d.DO.Preload(_f))
	}
	return &d
}

func (d discountSettingDo) FirstOrInit() (*models.DiscountSetting, error) {
	if result, err := d.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.DiscountSetting), nil
	}
}

func (d discountSettingDo) FirstOrCreate() (*models.DiscountSetting, error) {
	if result, err := d.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.DiscountSetting), nil
	}
}

func (d discountSettingDo) FindByPage(offset int, limit int) (result []*models.DiscountSetting, count int64, err error) {
	result, err = d.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = d.Offset(-1).Limit(-1).Count()
	return
}

func (d discountSettingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = d.Count()
	if err != nil {
		return
	}

	err = d.Offset(offset).Limit(limit).Scan(result)
	return
}

func (d discountSettingDo) Scan(result interface{}) (err error) {
	return d.DO.Scan(result)
}

func (d discountSettingDo) Delete(models ...*models.DiscountSetting) (result gen.ResultInfo, err error) {
	return d.DO.Delete(models)
}

func (d *discountSettingDo) withDO(do gen.Dao) *discountSettingDo {
	d.DO = *do.(*gen.DO)
	return d
}
