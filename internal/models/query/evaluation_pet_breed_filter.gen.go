// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newEvaluationPetBreedFilter(db *gorm.DB, opts ...gen.DOOption) evaluationPetBreedFilter {
	_evaluationPetBreedFilter := evaluationPetBreedFilter{}

	_evaluationPetBreedFilter.evaluationPetBreedFilterDo.UseDB(db, opts...)
	_evaluationPetBreedFilter.evaluationPetBreedFilterDo.UseModel(&models.EvaluationPetBreedFilter{})

	tableName := _evaluationPetBreedFilter.evaluationPetBreedFilterDo.TableName()
	_evaluationPetBreedFilter.ALL = field.NewAsterisk(tableName)
	_evaluationPetBreedFilter.ID = field.NewInt64(tableName, "id")
	_evaluationPetBreedFilter.EvaluationID = field.NewInt64(tableName, "evaluation_id")
	_evaluationPetBreedFilter.PetType = field.NewField(tableName, "pet_type")
	_evaluationPetBreedFilter.IsAllBreed = field.NewBool(tableName, "is_all_breed")
	_evaluationPetBreedFilter.BreedNames = field.NewField(tableName, "breed_names")
	_evaluationPetBreedFilter.CreatedAt = field.NewTime(tableName, "created_at")
	_evaluationPetBreedFilter.UpdatedAt = field.NewTime(tableName, "updated_at")
	_evaluationPetBreedFilter.DeletedAt = field.NewField(tableName, "deleted_at")

	_evaluationPetBreedFilter.fillFieldMap()

	return _evaluationPetBreedFilter
}

type evaluationPetBreedFilter struct {
	evaluationPetBreedFilterDo evaluationPetBreedFilterDo

	ALL          field.Asterisk
	ID           field.Int64
	EvaluationID field.Int64 // evaluation.id
	PetType      field.Field // Pet type enum, 1-Dog, 2-Cat,..., 11-Other
	IsAllBreed   field.Bool  // If true, all breeds are selected
	BreedNames   field.Field // Breed names, if is_all_breed is true, this field is empty
	CreatedAt    field.Time
	UpdatedAt    field.Time
	DeletedAt    field.Field

	fieldMap map[string]field.Expr
}

func (e evaluationPetBreedFilter) Table(newTableName string) *evaluationPetBreedFilter {
	e.evaluationPetBreedFilterDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e evaluationPetBreedFilter) As(alias string) *evaluationPetBreedFilter {
	e.evaluationPetBreedFilterDo.DO = *(e.evaluationPetBreedFilterDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *evaluationPetBreedFilter) updateTableName(table string) *evaluationPetBreedFilter {
	e.ALL = field.NewAsterisk(table)
	e.ID = field.NewInt64(table, "id")
	e.EvaluationID = field.NewInt64(table, "evaluation_id")
	e.PetType = field.NewField(table, "pet_type")
	e.IsAllBreed = field.NewBool(table, "is_all_breed")
	e.BreedNames = field.NewField(table, "breed_names")
	e.CreatedAt = field.NewTime(table, "created_at")
	e.UpdatedAt = field.NewTime(table, "updated_at")
	e.DeletedAt = field.NewField(table, "deleted_at")

	e.fillFieldMap()

	return e
}

func (e *evaluationPetBreedFilter) WithContext(ctx context.Context) IEvaluationPetBreedFilterDo {
	return e.evaluationPetBreedFilterDo.WithContext(ctx)
}

func (e evaluationPetBreedFilter) TableName() string { return e.evaluationPetBreedFilterDo.TableName() }

func (e evaluationPetBreedFilter) Alias() string { return e.evaluationPetBreedFilterDo.Alias() }

func (e evaluationPetBreedFilter) Columns(cols ...field.Expr) gen.Columns {
	return e.evaluationPetBreedFilterDo.Columns(cols...)
}

func (e *evaluationPetBreedFilter) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *evaluationPetBreedFilter) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 8)
	e.fieldMap["id"] = e.ID
	e.fieldMap["evaluation_id"] = e.EvaluationID
	e.fieldMap["pet_type"] = e.PetType
	e.fieldMap["is_all_breed"] = e.IsAllBreed
	e.fieldMap["breed_names"] = e.BreedNames
	e.fieldMap["created_at"] = e.CreatedAt
	e.fieldMap["updated_at"] = e.UpdatedAt
	e.fieldMap["deleted_at"] = e.DeletedAt
}

func (e evaluationPetBreedFilter) clone(db *gorm.DB) evaluationPetBreedFilter {
	e.evaluationPetBreedFilterDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e evaluationPetBreedFilter) replaceDB(db *gorm.DB) evaluationPetBreedFilter {
	e.evaluationPetBreedFilterDo.ReplaceDB(db)
	return e
}

type evaluationPetBreedFilterDo struct{ gen.DO }

type IEvaluationPetBreedFilterDo interface {
	gen.SubQuery
	Debug() IEvaluationPetBreedFilterDo
	WithContext(ctx context.Context) IEvaluationPetBreedFilterDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IEvaluationPetBreedFilterDo
	WriteDB() IEvaluationPetBreedFilterDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IEvaluationPetBreedFilterDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IEvaluationPetBreedFilterDo
	Not(conds ...gen.Condition) IEvaluationPetBreedFilterDo
	Or(conds ...gen.Condition) IEvaluationPetBreedFilterDo
	Select(conds ...field.Expr) IEvaluationPetBreedFilterDo
	Where(conds ...gen.Condition) IEvaluationPetBreedFilterDo
	Order(conds ...field.Expr) IEvaluationPetBreedFilterDo
	Distinct(cols ...field.Expr) IEvaluationPetBreedFilterDo
	Omit(cols ...field.Expr) IEvaluationPetBreedFilterDo
	Join(table schema.Tabler, on ...field.Expr) IEvaluationPetBreedFilterDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IEvaluationPetBreedFilterDo
	RightJoin(table schema.Tabler, on ...field.Expr) IEvaluationPetBreedFilterDo
	Group(cols ...field.Expr) IEvaluationPetBreedFilterDo
	Having(conds ...gen.Condition) IEvaluationPetBreedFilterDo
	Limit(limit int) IEvaluationPetBreedFilterDo
	Offset(offset int) IEvaluationPetBreedFilterDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IEvaluationPetBreedFilterDo
	Unscoped() IEvaluationPetBreedFilterDo
	Create(values ...*models.EvaluationPetBreedFilter) error
	CreateInBatches(values []*models.EvaluationPetBreedFilter, batchSize int) error
	Save(values ...*models.EvaluationPetBreedFilter) error
	First() (*models.EvaluationPetBreedFilter, error)
	Take() (*models.EvaluationPetBreedFilter, error)
	Last() (*models.EvaluationPetBreedFilter, error)
	Find() ([]*models.EvaluationPetBreedFilter, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.EvaluationPetBreedFilter, err error)
	FindInBatches(result *[]*models.EvaluationPetBreedFilter, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.EvaluationPetBreedFilter) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IEvaluationPetBreedFilterDo
	Assign(attrs ...field.AssignExpr) IEvaluationPetBreedFilterDo
	Joins(fields ...field.RelationField) IEvaluationPetBreedFilterDo
	Preload(fields ...field.RelationField) IEvaluationPetBreedFilterDo
	FirstOrInit() (*models.EvaluationPetBreedFilter, error)
	FirstOrCreate() (*models.EvaluationPetBreedFilter, error)
	FindByPage(offset int, limit int) (result []*models.EvaluationPetBreedFilter, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IEvaluationPetBreedFilterDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (e evaluationPetBreedFilterDo) Debug() IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Debug())
}

func (e evaluationPetBreedFilterDo) WithContext(ctx context.Context) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e evaluationPetBreedFilterDo) ReadDB() IEvaluationPetBreedFilterDo {
	return e.Clauses(dbresolver.Read)
}

func (e evaluationPetBreedFilterDo) WriteDB() IEvaluationPetBreedFilterDo {
	return e.Clauses(dbresolver.Write)
}

func (e evaluationPetBreedFilterDo) Session(config *gorm.Session) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Session(config))
}

func (e evaluationPetBreedFilterDo) Clauses(conds ...clause.Expression) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e evaluationPetBreedFilterDo) Returning(value interface{}, columns ...string) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e evaluationPetBreedFilterDo) Not(conds ...gen.Condition) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e evaluationPetBreedFilterDo) Or(conds ...gen.Condition) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e evaluationPetBreedFilterDo) Select(conds ...field.Expr) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e evaluationPetBreedFilterDo) Where(conds ...gen.Condition) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e evaluationPetBreedFilterDo) Order(conds ...field.Expr) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e evaluationPetBreedFilterDo) Distinct(cols ...field.Expr) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e evaluationPetBreedFilterDo) Omit(cols ...field.Expr) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e evaluationPetBreedFilterDo) Join(table schema.Tabler, on ...field.Expr) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e evaluationPetBreedFilterDo) LeftJoin(table schema.Tabler, on ...field.Expr) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e evaluationPetBreedFilterDo) RightJoin(table schema.Tabler, on ...field.Expr) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e evaluationPetBreedFilterDo) Group(cols ...field.Expr) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e evaluationPetBreedFilterDo) Having(conds ...gen.Condition) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e evaluationPetBreedFilterDo) Limit(limit int) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e evaluationPetBreedFilterDo) Offset(offset int) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e evaluationPetBreedFilterDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e evaluationPetBreedFilterDo) Unscoped() IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Unscoped())
}

func (e evaluationPetBreedFilterDo) Create(values ...*models.EvaluationPetBreedFilter) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e evaluationPetBreedFilterDo) CreateInBatches(values []*models.EvaluationPetBreedFilter, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e evaluationPetBreedFilterDo) Save(values ...*models.EvaluationPetBreedFilter) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e evaluationPetBreedFilterDo) First() (*models.EvaluationPetBreedFilter, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.EvaluationPetBreedFilter), nil
	}
}

func (e evaluationPetBreedFilterDo) Take() (*models.EvaluationPetBreedFilter, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.EvaluationPetBreedFilter), nil
	}
}

func (e evaluationPetBreedFilterDo) Last() (*models.EvaluationPetBreedFilter, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.EvaluationPetBreedFilter), nil
	}
}

func (e evaluationPetBreedFilterDo) Find() ([]*models.EvaluationPetBreedFilter, error) {
	result, err := e.DO.Find()
	return result.([]*models.EvaluationPetBreedFilter), err
}

func (e evaluationPetBreedFilterDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.EvaluationPetBreedFilter, err error) {
	buf := make([]*models.EvaluationPetBreedFilter, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e evaluationPetBreedFilterDo) FindInBatches(result *[]*models.EvaluationPetBreedFilter, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e evaluationPetBreedFilterDo) Attrs(attrs ...field.AssignExpr) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e evaluationPetBreedFilterDo) Assign(attrs ...field.AssignExpr) IEvaluationPetBreedFilterDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e evaluationPetBreedFilterDo) Joins(fields ...field.RelationField) IEvaluationPetBreedFilterDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e evaluationPetBreedFilterDo) Preload(fields ...field.RelationField) IEvaluationPetBreedFilterDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e evaluationPetBreedFilterDo) FirstOrInit() (*models.EvaluationPetBreedFilter, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.EvaluationPetBreedFilter), nil
	}
}

func (e evaluationPetBreedFilterDo) FirstOrCreate() (*models.EvaluationPetBreedFilter, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.EvaluationPetBreedFilter), nil
	}
}

func (e evaluationPetBreedFilterDo) FindByPage(offset int, limit int) (result []*models.EvaluationPetBreedFilter, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e evaluationPetBreedFilterDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e evaluationPetBreedFilterDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e evaluationPetBreedFilterDo) Delete(models ...*models.EvaluationPetBreedFilter) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *evaluationPetBreedFilterDo) withDO(do gen.Dao) *evaluationPetBreedFilterDo {
	e.DO = *do.(*gen.DO)
	return e
}
