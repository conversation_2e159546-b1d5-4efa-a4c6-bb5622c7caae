// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newCustomizeCareType(db *gorm.DB, opts ...gen.DOOption) customizeCareType {
	_customizeCareType := customizeCareType{}

	_customizeCareType.customizeCareTypeDo.UseDB(db, opts...)
	_customizeCareType.customizeCareTypeDo.UseModel(&models.CustomizeCareType{})

	tableName := _customizeCareType.customizeCareTypeDo.TableName()
	_customizeCareType.ALL = field.NewAsterisk(tableName)
	_customizeCareType.ID = field.NewInt64(tableName, "id")
	_customizeCareType.Name = field.NewString(tableName, "name")
	_customizeCareType.CompanyID = field.NewInt64(tableName, "company_id")
	_customizeCareType.ServiceItemType = field.NewField(tableName, "service_item_type")
	_customizeCareType.Sort = field.NewInt32(tableName, "sort")
	_customizeCareType.UpdatedBy = field.NewInt64(tableName, "updated_by")
	_customizeCareType.CreatedAt = field.NewTime(tableName, "created_at")
	_customizeCareType.UpdatedAt = field.NewTime(tableName, "updated_at")

	_customizeCareType.fillFieldMap()

	return _customizeCareType
}

type customizeCareType struct {
	customizeCareTypeDo customizeCareTypeDo

	ALL             field.Asterisk
	ID              field.Int64
	Name            field.String
	CompanyID       field.Int64
	ServiceItemType field.Field
	Sort            field.Int32
	UpdatedBy       field.Int64
	CreatedAt       field.Time
	UpdatedAt       field.Time

	fieldMap map[string]field.Expr
}

func (c customizeCareType) Table(newTableName string) *customizeCareType {
	c.customizeCareTypeDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c customizeCareType) As(alias string) *customizeCareType {
	c.customizeCareTypeDo.DO = *(c.customizeCareTypeDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *customizeCareType) updateTableName(table string) *customizeCareType {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.Name = field.NewString(table, "name")
	c.CompanyID = field.NewInt64(table, "company_id")
	c.ServiceItemType = field.NewField(table, "service_item_type")
	c.Sort = field.NewInt32(table, "sort")
	c.UpdatedBy = field.NewInt64(table, "updated_by")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedAt = field.NewTime(table, "updated_at")

	c.fillFieldMap()

	return c
}

func (c *customizeCareType) WithContext(ctx context.Context) ICustomizeCareTypeDo {
	return c.customizeCareTypeDo.WithContext(ctx)
}

func (c customizeCareType) TableName() string { return c.customizeCareTypeDo.TableName() }

func (c customizeCareType) Alias() string { return c.customizeCareTypeDo.Alias() }

func (c customizeCareType) Columns(cols ...field.Expr) gen.Columns {
	return c.customizeCareTypeDo.Columns(cols...)
}

func (c *customizeCareType) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *customizeCareType) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 8)
	c.fieldMap["id"] = c.ID
	c.fieldMap["name"] = c.Name
	c.fieldMap["company_id"] = c.CompanyID
	c.fieldMap["service_item_type"] = c.ServiceItemType
	c.fieldMap["sort"] = c.Sort
	c.fieldMap["updated_by"] = c.UpdatedBy
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
}

func (c customizeCareType) clone(db *gorm.DB) customizeCareType {
	c.customizeCareTypeDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c customizeCareType) replaceDB(db *gorm.DB) customizeCareType {
	c.customizeCareTypeDo.ReplaceDB(db)
	return c
}

type customizeCareTypeDo struct{ gen.DO }

type ICustomizeCareTypeDo interface {
	gen.SubQuery
	Debug() ICustomizeCareTypeDo
	WithContext(ctx context.Context) ICustomizeCareTypeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICustomizeCareTypeDo
	WriteDB() ICustomizeCareTypeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICustomizeCareTypeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICustomizeCareTypeDo
	Not(conds ...gen.Condition) ICustomizeCareTypeDo
	Or(conds ...gen.Condition) ICustomizeCareTypeDo
	Select(conds ...field.Expr) ICustomizeCareTypeDo
	Where(conds ...gen.Condition) ICustomizeCareTypeDo
	Order(conds ...field.Expr) ICustomizeCareTypeDo
	Distinct(cols ...field.Expr) ICustomizeCareTypeDo
	Omit(cols ...field.Expr) ICustomizeCareTypeDo
	Join(table schema.Tabler, on ...field.Expr) ICustomizeCareTypeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICustomizeCareTypeDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICustomizeCareTypeDo
	Group(cols ...field.Expr) ICustomizeCareTypeDo
	Having(conds ...gen.Condition) ICustomizeCareTypeDo
	Limit(limit int) ICustomizeCareTypeDo
	Offset(offset int) ICustomizeCareTypeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICustomizeCareTypeDo
	Unscoped() ICustomizeCareTypeDo
	Create(values ...*models.CustomizeCareType) error
	CreateInBatches(values []*models.CustomizeCareType, batchSize int) error
	Save(values ...*models.CustomizeCareType) error
	First() (*models.CustomizeCareType, error)
	Take() (*models.CustomizeCareType, error)
	Last() (*models.CustomizeCareType, error)
	Find() ([]*models.CustomizeCareType, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.CustomizeCareType, err error)
	FindInBatches(result *[]*models.CustomizeCareType, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.CustomizeCareType) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICustomizeCareTypeDo
	Assign(attrs ...field.AssignExpr) ICustomizeCareTypeDo
	Joins(fields ...field.RelationField) ICustomizeCareTypeDo
	Preload(fields ...field.RelationField) ICustomizeCareTypeDo
	FirstOrInit() (*models.CustomizeCareType, error)
	FirstOrCreate() (*models.CustomizeCareType, error)
	FindByPage(offset int, limit int) (result []*models.CustomizeCareType, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICustomizeCareTypeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c customizeCareTypeDo) Debug() ICustomizeCareTypeDo {
	return c.withDO(c.DO.Debug())
}

func (c customizeCareTypeDo) WithContext(ctx context.Context) ICustomizeCareTypeDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c customizeCareTypeDo) ReadDB() ICustomizeCareTypeDo {
	return c.Clauses(dbresolver.Read)
}

func (c customizeCareTypeDo) WriteDB() ICustomizeCareTypeDo {
	return c.Clauses(dbresolver.Write)
}

func (c customizeCareTypeDo) Session(config *gorm.Session) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Session(config))
}

func (c customizeCareTypeDo) Clauses(conds ...clause.Expression) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c customizeCareTypeDo) Returning(value interface{}, columns ...string) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c customizeCareTypeDo) Not(conds ...gen.Condition) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c customizeCareTypeDo) Or(conds ...gen.Condition) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c customizeCareTypeDo) Select(conds ...field.Expr) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c customizeCareTypeDo) Where(conds ...gen.Condition) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c customizeCareTypeDo) Order(conds ...field.Expr) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c customizeCareTypeDo) Distinct(cols ...field.Expr) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c customizeCareTypeDo) Omit(cols ...field.Expr) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c customizeCareTypeDo) Join(table schema.Tabler, on ...field.Expr) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c customizeCareTypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICustomizeCareTypeDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c customizeCareTypeDo) RightJoin(table schema.Tabler, on ...field.Expr) ICustomizeCareTypeDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c customizeCareTypeDo) Group(cols ...field.Expr) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c customizeCareTypeDo) Having(conds ...gen.Condition) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c customizeCareTypeDo) Limit(limit int) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c customizeCareTypeDo) Offset(offset int) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c customizeCareTypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c customizeCareTypeDo) Unscoped() ICustomizeCareTypeDo {
	return c.withDO(c.DO.Unscoped())
}

func (c customizeCareTypeDo) Create(values ...*models.CustomizeCareType) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c customizeCareTypeDo) CreateInBatches(values []*models.CustomizeCareType, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c customizeCareTypeDo) Save(values ...*models.CustomizeCareType) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c customizeCareTypeDo) First() (*models.CustomizeCareType, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.CustomizeCareType), nil
	}
}

func (c customizeCareTypeDo) Take() (*models.CustomizeCareType, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.CustomizeCareType), nil
	}
}

func (c customizeCareTypeDo) Last() (*models.CustomizeCareType, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.CustomizeCareType), nil
	}
}

func (c customizeCareTypeDo) Find() ([]*models.CustomizeCareType, error) {
	result, err := c.DO.Find()
	return result.([]*models.CustomizeCareType), err
}

func (c customizeCareTypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.CustomizeCareType, err error) {
	buf := make([]*models.CustomizeCareType, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c customizeCareTypeDo) FindInBatches(result *[]*models.CustomizeCareType, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c customizeCareTypeDo) Attrs(attrs ...field.AssignExpr) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c customizeCareTypeDo) Assign(attrs ...field.AssignExpr) ICustomizeCareTypeDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c customizeCareTypeDo) Joins(fields ...field.RelationField) ICustomizeCareTypeDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c customizeCareTypeDo) Preload(fields ...field.RelationField) ICustomizeCareTypeDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c customizeCareTypeDo) FirstOrInit() (*models.CustomizeCareType, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.CustomizeCareType), nil
	}
}

func (c customizeCareTypeDo) FirstOrCreate() (*models.CustomizeCareType, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.CustomizeCareType), nil
	}
}

func (c customizeCareTypeDo) FindByPage(offset int, limit int) (result []*models.CustomizeCareType, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c customizeCareTypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c customizeCareTypeDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c customizeCareTypeDo) Delete(models ...*models.CustomizeCareType) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *customizeCareTypeDo) withDO(do gen.Dao) *customizeCareTypeDo {
	c.DO = *do.(*gen.DO)
	return c
}
