// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                          = new(Query)
	CustomizeCareType          *customizeCareType
	DiscountSetting            *discountSetting
	Evaluation                 *evaluation
	EvaluationLocationOverride *evaluationLocationOverride
	EvaluationPetBreedFilter   *evaluationPetBreedFilter
	LodgingType                *lodgingType
	LodgingUnit                *lodgingUnit
	Playgroup                  *playgroup
	PricingRuleRecord          *pricingRuleRecord
	ServiceBundleSaleMapping   *serviceBundleSaleMapping
	ServiceStaffAvailability   *serviceStaffAvailability
	ServiceStaffOverrideRule   *serviceStaffOverrideRule
	VaccineRequirement         *vaccineRequirement
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	CustomizeCareType = &Q.CustomizeCareType
	DiscountSetting = &Q.DiscountSetting
	Evaluation = &Q.Evaluation
	EvaluationLocationOverride = &Q.EvaluationLocationOverride
	EvaluationPetBreedFilter = &Q.EvaluationPetBreedFilter
	LodgingType = &Q.LodgingType
	LodgingUnit = &Q.LodgingUnit
	Playgroup = &Q.Playgroup
	PricingRuleRecord = &Q.PricingRuleRecord
	ServiceBundleSaleMapping = &Q.ServiceBundleSaleMapping
	ServiceStaffAvailability = &Q.ServiceStaffAvailability
	ServiceStaffOverrideRule = &Q.ServiceStaffOverrideRule
	VaccineRequirement = &Q.VaccineRequirement
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                         db,
		CustomizeCareType:          newCustomizeCareType(db, opts...),
		DiscountSetting:            newDiscountSetting(db, opts...),
		Evaluation:                 newEvaluation(db, opts...),
		EvaluationLocationOverride: newEvaluationLocationOverride(db, opts...),
		EvaluationPetBreedFilter:   newEvaluationPetBreedFilter(db, opts...),
		LodgingType:                newLodgingType(db, opts...),
		LodgingUnit:                newLodgingUnit(db, opts...),
		Playgroup:                  newPlaygroup(db, opts...),
		PricingRuleRecord:          newPricingRuleRecord(db, opts...),
		ServiceBundleSaleMapping:   newServiceBundleSaleMapping(db, opts...),
		ServiceStaffAvailability:   newServiceStaffAvailability(db, opts...),
		ServiceStaffOverrideRule:   newServiceStaffOverrideRule(db, opts...),
		VaccineRequirement:         newVaccineRequirement(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	CustomizeCareType          customizeCareType
	DiscountSetting            discountSetting
	Evaluation                 evaluation
	EvaluationLocationOverride evaluationLocationOverride
	EvaluationPetBreedFilter   evaluationPetBreedFilter
	LodgingType                lodgingType
	LodgingUnit                lodgingUnit
	Playgroup                  playgroup
	PricingRuleRecord          pricingRuleRecord
	ServiceBundleSaleMapping   serviceBundleSaleMapping
	ServiceStaffAvailability   serviceStaffAvailability
	ServiceStaffOverrideRule   serviceStaffOverrideRule
	VaccineRequirement         vaccineRequirement
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                         db,
		CustomizeCareType:          q.CustomizeCareType.clone(db),
		DiscountSetting:            q.DiscountSetting.clone(db),
		Evaluation:                 q.Evaluation.clone(db),
		EvaluationLocationOverride: q.EvaluationLocationOverride.clone(db),
		EvaluationPetBreedFilter:   q.EvaluationPetBreedFilter.clone(db),
		LodgingType:                q.LodgingType.clone(db),
		LodgingUnit:                q.LodgingUnit.clone(db),
		Playgroup:                  q.Playgroup.clone(db),
		PricingRuleRecord:          q.PricingRuleRecord.clone(db),
		ServiceBundleSaleMapping:   q.ServiceBundleSaleMapping.clone(db),
		ServiceStaffAvailability:   q.ServiceStaffAvailability.clone(db),
		ServiceStaffOverrideRule:   q.ServiceStaffOverrideRule.clone(db),
		VaccineRequirement:         q.VaccineRequirement.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                         db,
		CustomizeCareType:          q.CustomizeCareType.replaceDB(db),
		DiscountSetting:            q.DiscountSetting.replaceDB(db),
		Evaluation:                 q.Evaluation.replaceDB(db),
		EvaluationLocationOverride: q.EvaluationLocationOverride.replaceDB(db),
		EvaluationPetBreedFilter:   q.EvaluationPetBreedFilter.replaceDB(db),
		LodgingType:                q.LodgingType.replaceDB(db),
		LodgingUnit:                q.LodgingUnit.replaceDB(db),
		Playgroup:                  q.Playgroup.replaceDB(db),
		PricingRuleRecord:          q.PricingRuleRecord.replaceDB(db),
		ServiceBundleSaleMapping:   q.ServiceBundleSaleMapping.replaceDB(db),
		ServiceStaffAvailability:   q.ServiceStaffAvailability.replaceDB(db),
		ServiceStaffOverrideRule:   q.ServiceStaffOverrideRule.replaceDB(db),
		VaccineRequirement:         q.VaccineRequirement.replaceDB(db),
	}
}

type queryCtx struct {
	CustomizeCareType          ICustomizeCareTypeDo
	DiscountSetting            IDiscountSettingDo
	Evaluation                 IEvaluationDo
	EvaluationLocationOverride IEvaluationLocationOverrideDo
	EvaluationPetBreedFilter   IEvaluationPetBreedFilterDo
	LodgingType                ILodgingTypeDo
	LodgingUnit                ILodgingUnitDo
	Playgroup                  IPlaygroupDo
	PricingRuleRecord          IPricingRuleRecordDo
	ServiceBundleSaleMapping   IServiceBundleSaleMappingDo
	ServiceStaffAvailability   IServiceStaffAvailabilityDo
	ServiceStaffOverrideRule   IServiceStaffOverrideRuleDo
	VaccineRequirement         IVaccineRequirementDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		CustomizeCareType:          q.CustomizeCareType.WithContext(ctx),
		DiscountSetting:            q.DiscountSetting.WithContext(ctx),
		Evaluation:                 q.Evaluation.WithContext(ctx),
		EvaluationLocationOverride: q.EvaluationLocationOverride.WithContext(ctx),
		EvaluationPetBreedFilter:   q.EvaluationPetBreedFilter.WithContext(ctx),
		LodgingType:                q.LodgingType.WithContext(ctx),
		LodgingUnit:                q.LodgingUnit.WithContext(ctx),
		Playgroup:                  q.Playgroup.WithContext(ctx),
		PricingRuleRecord:          q.PricingRuleRecord.WithContext(ctx),
		ServiceBundleSaleMapping:   q.ServiceBundleSaleMapping.WithContext(ctx),
		ServiceStaffAvailability:   q.ServiceStaffAvailability.WithContext(ctx),
		ServiceStaffOverrideRule:   q.ServiceStaffOverrideRule.WithContext(ctx),
		VaccineRequirement:         q.VaccineRequirement.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
