// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newServiceStaffOverrideRule(db *gorm.DB, opts ...gen.DOOption) serviceStaffOverrideRule {
	_serviceStaffOverrideRule := serviceStaffOverrideRule{}

	_serviceStaffOverrideRule.serviceStaffOverrideRuleDo.UseDB(db, opts...)
	_serviceStaffOverrideRule.serviceStaffOverrideRuleDo.UseModel(&models.ServiceStaffOverrideRule{})

	tableName := _serviceStaffOverrideRule.serviceStaffOverrideRuleDo.TableName()
	_serviceStaffOverrideRule.ALL = field.NewAsterisk(tableName)
	_serviceStaffOverrideRule.ID = field.NewInt64(tableName, "id")
	_serviceStaffOverrideRule.CompanyID = field.NewInt64(tableName, "company_id")
	_serviceStaffOverrideRule.BusinessID = field.NewInt64(tableName, "business_id")
	_serviceStaffOverrideRule.StaffID = field.NewInt64(tableName, "staff_id")
	_serviceStaffOverrideRule.ServiceID = field.NewInt64(tableName, "service_id")
	_serviceStaffOverrideRule.Price = field.NewFloat64(tableName, "price")
	_serviceStaffOverrideRule.Duration = field.NewInt32(tableName, "duration")
	_serviceStaffOverrideRule.UpdatedBy = field.NewInt64(tableName, "updated_by")
	_serviceStaffOverrideRule.CreatedAt = field.NewTime(tableName, "created_at")
	_serviceStaffOverrideRule.UpdatedAt = field.NewTime(tableName, "updated_at")

	_serviceStaffOverrideRule.fillFieldMap()

	return _serviceStaffOverrideRule
}

type serviceStaffOverrideRule struct {
	serviceStaffOverrideRuleDo serviceStaffOverrideRuleDo

	ALL        field.Asterisk
	ID         field.Int64
	CompanyID  field.Int64
	BusinessID field.Int64
	StaffID    field.Int64
	ServiceID  field.Int64
	Price      field.Float64 // customized price for the service
	Duration   field.Int32   // customized duration for the service
	UpdatedBy  field.Int64
	CreatedAt  field.Time
	UpdatedAt  field.Time

	fieldMap map[string]field.Expr
}

func (s serviceStaffOverrideRule) Table(newTableName string) *serviceStaffOverrideRule {
	s.serviceStaffOverrideRuleDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceStaffOverrideRule) As(alias string) *serviceStaffOverrideRule {
	s.serviceStaffOverrideRuleDo.DO = *(s.serviceStaffOverrideRuleDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceStaffOverrideRule) updateTableName(table string) *serviceStaffOverrideRule {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.CompanyID = field.NewInt64(table, "company_id")
	s.BusinessID = field.NewInt64(table, "business_id")
	s.StaffID = field.NewInt64(table, "staff_id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.Price = field.NewFloat64(table, "price")
	s.Duration = field.NewInt32(table, "duration")
	s.UpdatedBy = field.NewInt64(table, "updated_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")

	s.fillFieldMap()

	return s
}

func (s *serviceStaffOverrideRule) WithContext(ctx context.Context) IServiceStaffOverrideRuleDo {
	return s.serviceStaffOverrideRuleDo.WithContext(ctx)
}

func (s serviceStaffOverrideRule) TableName() string { return s.serviceStaffOverrideRuleDo.TableName() }

func (s serviceStaffOverrideRule) Alias() string { return s.serviceStaffOverrideRuleDo.Alias() }

func (s serviceStaffOverrideRule) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceStaffOverrideRuleDo.Columns(cols...)
}

func (s *serviceStaffOverrideRule) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceStaffOverrideRule) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 10)
	s.fieldMap["id"] = s.ID
	s.fieldMap["company_id"] = s.CompanyID
	s.fieldMap["business_id"] = s.BusinessID
	s.fieldMap["staff_id"] = s.StaffID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["price"] = s.Price
	s.fieldMap["duration"] = s.Duration
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
}

func (s serviceStaffOverrideRule) clone(db *gorm.DB) serviceStaffOverrideRule {
	s.serviceStaffOverrideRuleDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceStaffOverrideRule) replaceDB(db *gorm.DB) serviceStaffOverrideRule {
	s.serviceStaffOverrideRuleDo.ReplaceDB(db)
	return s
}

type serviceStaffOverrideRuleDo struct{ gen.DO }

type IServiceStaffOverrideRuleDo interface {
	gen.SubQuery
	Debug() IServiceStaffOverrideRuleDo
	WithContext(ctx context.Context) IServiceStaffOverrideRuleDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IServiceStaffOverrideRuleDo
	WriteDB() IServiceStaffOverrideRuleDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IServiceStaffOverrideRuleDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IServiceStaffOverrideRuleDo
	Not(conds ...gen.Condition) IServiceStaffOverrideRuleDo
	Or(conds ...gen.Condition) IServiceStaffOverrideRuleDo
	Select(conds ...field.Expr) IServiceStaffOverrideRuleDo
	Where(conds ...gen.Condition) IServiceStaffOverrideRuleDo
	Order(conds ...field.Expr) IServiceStaffOverrideRuleDo
	Distinct(cols ...field.Expr) IServiceStaffOverrideRuleDo
	Omit(cols ...field.Expr) IServiceStaffOverrideRuleDo
	Join(table schema.Tabler, on ...field.Expr) IServiceStaffOverrideRuleDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IServiceStaffOverrideRuleDo
	RightJoin(table schema.Tabler, on ...field.Expr) IServiceStaffOverrideRuleDo
	Group(cols ...field.Expr) IServiceStaffOverrideRuleDo
	Having(conds ...gen.Condition) IServiceStaffOverrideRuleDo
	Limit(limit int) IServiceStaffOverrideRuleDo
	Offset(offset int) IServiceStaffOverrideRuleDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IServiceStaffOverrideRuleDo
	Unscoped() IServiceStaffOverrideRuleDo
	Create(values ...*models.ServiceStaffOverrideRule) error
	CreateInBatches(values []*models.ServiceStaffOverrideRule, batchSize int) error
	Save(values ...*models.ServiceStaffOverrideRule) error
	First() (*models.ServiceStaffOverrideRule, error)
	Take() (*models.ServiceStaffOverrideRule, error)
	Last() (*models.ServiceStaffOverrideRule, error)
	Find() ([]*models.ServiceStaffOverrideRule, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.ServiceStaffOverrideRule, err error)
	FindInBatches(result *[]*models.ServiceStaffOverrideRule, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.ServiceStaffOverrideRule) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IServiceStaffOverrideRuleDo
	Assign(attrs ...field.AssignExpr) IServiceStaffOverrideRuleDo
	Joins(fields ...field.RelationField) IServiceStaffOverrideRuleDo
	Preload(fields ...field.RelationField) IServiceStaffOverrideRuleDo
	FirstOrInit() (*models.ServiceStaffOverrideRule, error)
	FirstOrCreate() (*models.ServiceStaffOverrideRule, error)
	FindByPage(offset int, limit int) (result []*models.ServiceStaffOverrideRule, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IServiceStaffOverrideRuleDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s serviceStaffOverrideRuleDo) Debug() IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceStaffOverrideRuleDo) WithContext(ctx context.Context) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceStaffOverrideRuleDo) ReadDB() IServiceStaffOverrideRuleDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceStaffOverrideRuleDo) WriteDB() IServiceStaffOverrideRuleDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceStaffOverrideRuleDo) Session(config *gorm.Session) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceStaffOverrideRuleDo) Clauses(conds ...clause.Expression) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceStaffOverrideRuleDo) Returning(value interface{}, columns ...string) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceStaffOverrideRuleDo) Not(conds ...gen.Condition) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceStaffOverrideRuleDo) Or(conds ...gen.Condition) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceStaffOverrideRuleDo) Select(conds ...field.Expr) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceStaffOverrideRuleDo) Where(conds ...gen.Condition) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceStaffOverrideRuleDo) Order(conds ...field.Expr) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceStaffOverrideRuleDo) Distinct(cols ...field.Expr) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceStaffOverrideRuleDo) Omit(cols ...field.Expr) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceStaffOverrideRuleDo) Join(table schema.Tabler, on ...field.Expr) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceStaffOverrideRuleDo) LeftJoin(table schema.Tabler, on ...field.Expr) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceStaffOverrideRuleDo) RightJoin(table schema.Tabler, on ...field.Expr) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceStaffOverrideRuleDo) Group(cols ...field.Expr) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceStaffOverrideRuleDo) Having(conds ...gen.Condition) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceStaffOverrideRuleDo) Limit(limit int) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceStaffOverrideRuleDo) Offset(offset int) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceStaffOverrideRuleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceStaffOverrideRuleDo) Unscoped() IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceStaffOverrideRuleDo) Create(values ...*models.ServiceStaffOverrideRule) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceStaffOverrideRuleDo) CreateInBatches(values []*models.ServiceStaffOverrideRule, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceStaffOverrideRuleDo) Save(values ...*models.ServiceStaffOverrideRule) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceStaffOverrideRuleDo) First() (*models.ServiceStaffOverrideRule, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceStaffOverrideRule), nil
	}
}

func (s serviceStaffOverrideRuleDo) Take() (*models.ServiceStaffOverrideRule, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceStaffOverrideRule), nil
	}
}

func (s serviceStaffOverrideRuleDo) Last() (*models.ServiceStaffOverrideRule, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceStaffOverrideRule), nil
	}
}

func (s serviceStaffOverrideRuleDo) Find() ([]*models.ServiceStaffOverrideRule, error) {
	result, err := s.DO.Find()
	return result.([]*models.ServiceStaffOverrideRule), err
}

func (s serviceStaffOverrideRuleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.ServiceStaffOverrideRule, err error) {
	buf := make([]*models.ServiceStaffOverrideRule, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceStaffOverrideRuleDo) FindInBatches(result *[]*models.ServiceStaffOverrideRule, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceStaffOverrideRuleDo) Attrs(attrs ...field.AssignExpr) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceStaffOverrideRuleDo) Assign(attrs ...field.AssignExpr) IServiceStaffOverrideRuleDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceStaffOverrideRuleDo) Joins(fields ...field.RelationField) IServiceStaffOverrideRuleDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceStaffOverrideRuleDo) Preload(fields ...field.RelationField) IServiceStaffOverrideRuleDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceStaffOverrideRuleDo) FirstOrInit() (*models.ServiceStaffOverrideRule, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceStaffOverrideRule), nil
	}
}

func (s serviceStaffOverrideRuleDo) FirstOrCreate() (*models.ServiceStaffOverrideRule, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceStaffOverrideRule), nil
	}
}

func (s serviceStaffOverrideRuleDo) FindByPage(offset int, limit int) (result []*models.ServiceStaffOverrideRule, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceStaffOverrideRuleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceStaffOverrideRuleDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceStaffOverrideRuleDo) Delete(models ...*models.ServiceStaffOverrideRule) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceStaffOverrideRuleDo) withDO(do gen.Dao) *serviceStaffOverrideRuleDo {
	s.DO = *do.(*gen.DO)
	return s
}
