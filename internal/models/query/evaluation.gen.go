// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newEvaluation(db *gorm.DB, opts ...gen.DOOption) evaluation {
	_evaluation := evaluation{}

	_evaluation.evaluationDo.UseDB(db, opts...)
	_evaluation.evaluationDo.UseModel(&models.Evaluation{})

	tableName := _evaluation.evaluationDo.TableName()
	_evaluation.ALL = field.NewAsterisk(tableName)
	_evaluation.ID = field.NewInt64(tableName, "id")
	_evaluation.Name = field.NewString(tableName, "name")
	_evaluation.CompanyID = field.NewInt64(tableName, "company_id")
	_evaluation.AvailableForAllBusiness = field.NewBool(tableName, "available_for_all_business")
	_evaluation.AvailableBusinessIDList = field.NewField(tableName, "available_business_id_list")
	_evaluation.ServiceItemTypeList = field.NewField(tableName, "service_item_type_list")
	_evaluation.Price = field.NewFloat64(tableName, "price")
	_evaluation.Duration = field.NewInt32(tableName, "duration")
	_evaluation.ColorCode = field.NewString(tableName, "color_code")
	_evaluation.IsActive = field.NewBool(tableName, "is_active")
	_evaluation.LodgingFilter = field.NewBool(tableName, "lodging_filter")
	_evaluation.AllowedLodgingList = field.NewField(tableName, "allowed_lodging_list")
	_evaluation.Description = field.NewString(tableName, "description")
	_evaluation.OnlineBookingAlias = field.NewString(tableName, "online_booking_alias")
	_evaluation.IsOnlineBookAvailable = field.NewBool(tableName, "is_online_book_available")
	_evaluation.IsAllStaff = field.NewBool(tableName, "is_all_staff")
	_evaluation.AllowedStaffList = field.NewField(tableName, "allowed_staff_list")
	_evaluation.AllowStaffAutoAssign = field.NewBool(tableName, "allow_staff_auto_assign")
	_evaluation.IsResettable = field.NewBool(tableName, "is_resettable")
	_evaluation.ResetIntervalDays = field.NewInt32(tableName, "reset_interval_days")
	_evaluation.BreedFilter = field.NewBool(tableName, "breed_filter")
	_evaluation.TaxID = field.NewInt64(tableName, "tax_id")
	_evaluation.CreatedAt = field.NewTime(tableName, "created_at")
	_evaluation.UpdatedAt = field.NewTime(tableName, "updated_at")
	_evaluation.DeletedAt = field.NewField(tableName, "deleted_at")
	_evaluation.Source = field.NewField(tableName, "source")

	_evaluation.fillFieldMap()

	return _evaluation
}

type evaluation struct {
	evaluationDo evaluationDo

	ALL                     field.Asterisk
	ID                      field.Int64
	Name                    field.String
	CompanyID               field.Int64
	AvailableForAllBusiness field.Bool
	AvailableBusinessIDList field.Field
	ServiceItemTypeList     field.Field
	Price                   field.Float64
	Duration                field.Int32
	ColorCode               field.String
	IsActive                field.Bool
	LodgingFilter           field.Bool
	AllowedLodgingList      field.Field // allowed lodging id list, only when require_dedicated_lodging and lodging_filter is true
	Description             field.String
	OnlineBookingAlias      field.String // alias shown in ob flow
	IsOnlineBookAvailable   field.Bool
	IsAllStaff              field.Bool
	AllowedStaffList        field.Field // allowed staff id list, only when is_all_staff is true
	AllowStaffAutoAssign    field.Bool  // whether to support automatic allocation of staff
	IsResettable            field.Bool
	ResetIntervalDays       field.Int32
	BreedFilter             field.Bool // If true, the evaluation is filtered by pet type and breed
	TaxID                   field.Int64
	CreatedAt               field.Time
	UpdatedAt               field.Time
	DeletedAt               field.Field
	Source                  field.Field // 1-MoeGo Platform 2-Enterprise Hub

	fieldMap map[string]field.Expr
}

func (e evaluation) Table(newTableName string) *evaluation {
	e.evaluationDo.UseTable(newTableName)
	return e.updateTableName(newTableName)
}

func (e evaluation) As(alias string) *evaluation {
	e.evaluationDo.DO = *(e.evaluationDo.As(alias).(*gen.DO))
	return e.updateTableName(alias)
}

func (e *evaluation) updateTableName(table string) *evaluation {
	e.ALL = field.NewAsterisk(table)
	e.ID = field.NewInt64(table, "id")
	e.Name = field.NewString(table, "name")
	e.CompanyID = field.NewInt64(table, "company_id")
	e.AvailableForAllBusiness = field.NewBool(table, "available_for_all_business")
	e.AvailableBusinessIDList = field.NewField(table, "available_business_id_list")
	e.ServiceItemTypeList = field.NewField(table, "service_item_type_list")
	e.Price = field.NewFloat64(table, "price")
	e.Duration = field.NewInt32(table, "duration")
	e.ColorCode = field.NewString(table, "color_code")
	e.IsActive = field.NewBool(table, "is_active")
	e.LodgingFilter = field.NewBool(table, "lodging_filter")
	e.AllowedLodgingList = field.NewField(table, "allowed_lodging_list")
	e.Description = field.NewString(table, "description")
	e.OnlineBookingAlias = field.NewString(table, "online_booking_alias")
	e.IsOnlineBookAvailable = field.NewBool(table, "is_online_book_available")
	e.IsAllStaff = field.NewBool(table, "is_all_staff")
	e.AllowedStaffList = field.NewField(table, "allowed_staff_list")
	e.AllowStaffAutoAssign = field.NewBool(table, "allow_staff_auto_assign")
	e.IsResettable = field.NewBool(table, "is_resettable")
	e.ResetIntervalDays = field.NewInt32(table, "reset_interval_days")
	e.BreedFilter = field.NewBool(table, "breed_filter")
	e.TaxID = field.NewInt64(table, "tax_id")
	e.CreatedAt = field.NewTime(table, "created_at")
	e.UpdatedAt = field.NewTime(table, "updated_at")
	e.DeletedAt = field.NewField(table, "deleted_at")
	e.Source = field.NewField(table, "source")

	e.fillFieldMap()

	return e
}

func (e *evaluation) WithContext(ctx context.Context) IEvaluationDo {
	return e.evaluationDo.WithContext(ctx)
}

func (e evaluation) TableName() string { return e.evaluationDo.TableName() }

func (e evaluation) Alias() string { return e.evaluationDo.Alias() }

func (e evaluation) Columns(cols ...field.Expr) gen.Columns { return e.evaluationDo.Columns(cols...) }

func (e *evaluation) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := e.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (e *evaluation) fillFieldMap() {
	e.fieldMap = make(map[string]field.Expr, 26)
	e.fieldMap["id"] = e.ID
	e.fieldMap["name"] = e.Name
	e.fieldMap["company_id"] = e.CompanyID
	e.fieldMap["available_for_all_business"] = e.AvailableForAllBusiness
	e.fieldMap["available_business_id_list"] = e.AvailableBusinessIDList
	e.fieldMap["service_item_type_list"] = e.ServiceItemTypeList
	e.fieldMap["price"] = e.Price
	e.fieldMap["duration"] = e.Duration
	e.fieldMap["color_code"] = e.ColorCode
	e.fieldMap["is_active"] = e.IsActive
	e.fieldMap["lodging_filter"] = e.LodgingFilter
	e.fieldMap["allowed_lodging_list"] = e.AllowedLodgingList
	e.fieldMap["description"] = e.Description
	e.fieldMap["online_booking_alias"] = e.OnlineBookingAlias
	e.fieldMap["is_online_book_available"] = e.IsOnlineBookAvailable
	e.fieldMap["is_all_staff"] = e.IsAllStaff
	e.fieldMap["allowed_staff_list"] = e.AllowedStaffList
	e.fieldMap["allow_staff_auto_assign"] = e.AllowStaffAutoAssign
	e.fieldMap["is_resettable"] = e.IsResettable
	e.fieldMap["reset_interval_days"] = e.ResetIntervalDays
	e.fieldMap["breed_filter"] = e.BreedFilter
	e.fieldMap["tax_id"] = e.TaxID
	e.fieldMap["created_at"] = e.CreatedAt
	e.fieldMap["updated_at"] = e.UpdatedAt
	e.fieldMap["deleted_at"] = e.DeletedAt
	e.fieldMap["source"] = e.Source
}

func (e evaluation) clone(db *gorm.DB) evaluation {
	e.evaluationDo.ReplaceConnPool(db.Statement.ConnPool)
	return e
}

func (e evaluation) replaceDB(db *gorm.DB) evaluation {
	e.evaluationDo.ReplaceDB(db)
	return e
}

type evaluationDo struct{ gen.DO }

type IEvaluationDo interface {
	gen.SubQuery
	Debug() IEvaluationDo
	WithContext(ctx context.Context) IEvaluationDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IEvaluationDo
	WriteDB() IEvaluationDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IEvaluationDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IEvaluationDo
	Not(conds ...gen.Condition) IEvaluationDo
	Or(conds ...gen.Condition) IEvaluationDo
	Select(conds ...field.Expr) IEvaluationDo
	Where(conds ...gen.Condition) IEvaluationDo
	Order(conds ...field.Expr) IEvaluationDo
	Distinct(cols ...field.Expr) IEvaluationDo
	Omit(cols ...field.Expr) IEvaluationDo
	Join(table schema.Tabler, on ...field.Expr) IEvaluationDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IEvaluationDo
	RightJoin(table schema.Tabler, on ...field.Expr) IEvaluationDo
	Group(cols ...field.Expr) IEvaluationDo
	Having(conds ...gen.Condition) IEvaluationDo
	Limit(limit int) IEvaluationDo
	Offset(offset int) IEvaluationDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IEvaluationDo
	Unscoped() IEvaluationDo
	Create(values ...*models.Evaluation) error
	CreateInBatches(values []*models.Evaluation, batchSize int) error
	Save(values ...*models.Evaluation) error
	First() (*models.Evaluation, error)
	Take() (*models.Evaluation, error)
	Last() (*models.Evaluation, error)
	Find() ([]*models.Evaluation, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Evaluation, err error)
	FindInBatches(result *[]*models.Evaluation, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.Evaluation) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IEvaluationDo
	Assign(attrs ...field.AssignExpr) IEvaluationDo
	Joins(fields ...field.RelationField) IEvaluationDo
	Preload(fields ...field.RelationField) IEvaluationDo
	FirstOrInit() (*models.Evaluation, error)
	FirstOrCreate() (*models.Evaluation, error)
	FindByPage(offset int, limit int) (result []*models.Evaluation, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IEvaluationDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (e evaluationDo) Debug() IEvaluationDo {
	return e.withDO(e.DO.Debug())
}

func (e evaluationDo) WithContext(ctx context.Context) IEvaluationDo {
	return e.withDO(e.DO.WithContext(ctx))
}

func (e evaluationDo) ReadDB() IEvaluationDo {
	return e.Clauses(dbresolver.Read)
}

func (e evaluationDo) WriteDB() IEvaluationDo {
	return e.Clauses(dbresolver.Write)
}

func (e evaluationDo) Session(config *gorm.Session) IEvaluationDo {
	return e.withDO(e.DO.Session(config))
}

func (e evaluationDo) Clauses(conds ...clause.Expression) IEvaluationDo {
	return e.withDO(e.DO.Clauses(conds...))
}

func (e evaluationDo) Returning(value interface{}, columns ...string) IEvaluationDo {
	return e.withDO(e.DO.Returning(value, columns...))
}

func (e evaluationDo) Not(conds ...gen.Condition) IEvaluationDo {
	return e.withDO(e.DO.Not(conds...))
}

func (e evaluationDo) Or(conds ...gen.Condition) IEvaluationDo {
	return e.withDO(e.DO.Or(conds...))
}

func (e evaluationDo) Select(conds ...field.Expr) IEvaluationDo {
	return e.withDO(e.DO.Select(conds...))
}

func (e evaluationDo) Where(conds ...gen.Condition) IEvaluationDo {
	return e.withDO(e.DO.Where(conds...))
}

func (e evaluationDo) Order(conds ...field.Expr) IEvaluationDo {
	return e.withDO(e.DO.Order(conds...))
}

func (e evaluationDo) Distinct(cols ...field.Expr) IEvaluationDo {
	return e.withDO(e.DO.Distinct(cols...))
}

func (e evaluationDo) Omit(cols ...field.Expr) IEvaluationDo {
	return e.withDO(e.DO.Omit(cols...))
}

func (e evaluationDo) Join(table schema.Tabler, on ...field.Expr) IEvaluationDo {
	return e.withDO(e.DO.Join(table, on...))
}

func (e evaluationDo) LeftJoin(table schema.Tabler, on ...field.Expr) IEvaluationDo {
	return e.withDO(e.DO.LeftJoin(table, on...))
}

func (e evaluationDo) RightJoin(table schema.Tabler, on ...field.Expr) IEvaluationDo {
	return e.withDO(e.DO.RightJoin(table, on...))
}

func (e evaluationDo) Group(cols ...field.Expr) IEvaluationDo {
	return e.withDO(e.DO.Group(cols...))
}

func (e evaluationDo) Having(conds ...gen.Condition) IEvaluationDo {
	return e.withDO(e.DO.Having(conds...))
}

func (e evaluationDo) Limit(limit int) IEvaluationDo {
	return e.withDO(e.DO.Limit(limit))
}

func (e evaluationDo) Offset(offset int) IEvaluationDo {
	return e.withDO(e.DO.Offset(offset))
}

func (e evaluationDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IEvaluationDo {
	return e.withDO(e.DO.Scopes(funcs...))
}

func (e evaluationDo) Unscoped() IEvaluationDo {
	return e.withDO(e.DO.Unscoped())
}

func (e evaluationDo) Create(values ...*models.Evaluation) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Create(values)
}

func (e evaluationDo) CreateInBatches(values []*models.Evaluation, batchSize int) error {
	return e.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (e evaluationDo) Save(values ...*models.Evaluation) error {
	if len(values) == 0 {
		return nil
	}
	return e.DO.Save(values)
}

func (e evaluationDo) First() (*models.Evaluation, error) {
	if result, err := e.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.Evaluation), nil
	}
}

func (e evaluationDo) Take() (*models.Evaluation, error) {
	if result, err := e.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.Evaluation), nil
	}
}

func (e evaluationDo) Last() (*models.Evaluation, error) {
	if result, err := e.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.Evaluation), nil
	}
}

func (e evaluationDo) Find() ([]*models.Evaluation, error) {
	result, err := e.DO.Find()
	return result.([]*models.Evaluation), err
}

func (e evaluationDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.Evaluation, err error) {
	buf := make([]*models.Evaluation, 0, batchSize)
	err = e.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (e evaluationDo) FindInBatches(result *[]*models.Evaluation, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return e.DO.FindInBatches(result, batchSize, fc)
}

func (e evaluationDo) Attrs(attrs ...field.AssignExpr) IEvaluationDo {
	return e.withDO(e.DO.Attrs(attrs...))
}

func (e evaluationDo) Assign(attrs ...field.AssignExpr) IEvaluationDo {
	return e.withDO(e.DO.Assign(attrs...))
}

func (e evaluationDo) Joins(fields ...field.RelationField) IEvaluationDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Joins(_f))
	}
	return &e
}

func (e evaluationDo) Preload(fields ...field.RelationField) IEvaluationDo {
	for _, _f := range fields {
		e = *e.withDO(e.DO.Preload(_f))
	}
	return &e
}

func (e evaluationDo) FirstOrInit() (*models.Evaluation, error) {
	if result, err := e.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.Evaluation), nil
	}
}

func (e evaluationDo) FirstOrCreate() (*models.Evaluation, error) {
	if result, err := e.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.Evaluation), nil
	}
}

func (e evaluationDo) FindByPage(offset int, limit int) (result []*models.Evaluation, count int64, err error) {
	result, err = e.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = e.Offset(-1).Limit(-1).Count()
	return
}

func (e evaluationDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = e.Count()
	if err != nil {
		return
	}

	err = e.Offset(offset).Limit(limit).Scan(result)
	return
}

func (e evaluationDo) Scan(result interface{}) (err error) {
	return e.DO.Scan(result)
}

func (e evaluationDo) Delete(models ...*models.Evaluation) (result gen.ResultInfo, err error) {
	return e.DO.Delete(models)
}

func (e *evaluationDo) withDO(do gen.Dao) *evaluationDo {
	e.DO = *do.(*gen.DO)
	return e
}
