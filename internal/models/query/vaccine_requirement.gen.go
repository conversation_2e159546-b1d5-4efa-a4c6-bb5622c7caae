// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newVaccineRequirement(db *gorm.DB, opts ...gen.DOOption) vaccineRequirement {
	_vaccineRequirement := vaccineRequirement{}

	_vaccineRequirement.vaccineRequirementDo.UseDB(db, opts...)
	_vaccineRequirement.vaccineRequirementDo.UseModel(&models.VaccineRequirement{})

	tableName := _vaccineRequirement.vaccineRequirementDo.TableName()
	_vaccineRequirement.ALL = field.NewAsterisk(tableName)
	_vaccineRequirement.ID = field.NewInt64(tableName, "id")
	_vaccineRequirement.CompanyID = field.NewInt64(tableName, "company_id")
	_vaccineRequirement.ServiceItemType = field.NewField(tableName, "service_item_type")
	_vaccineRequirement.VaccineID = field.NewInt64(tableName, "vaccine_id")

	_vaccineRequirement.fillFieldMap()

	return _vaccineRequirement
}

type vaccineRequirement struct {
	vaccineRequirementDo vaccineRequirementDo

	ALL             field.Asterisk
	ID              field.Int64
	CompanyID       field.Int64
	ServiceItemType field.Field
	VaccineID       field.Int64

	fieldMap map[string]field.Expr
}

func (v vaccineRequirement) Table(newTableName string) *vaccineRequirement {
	v.vaccineRequirementDo.UseTable(newTableName)
	return v.updateTableName(newTableName)
}

func (v vaccineRequirement) As(alias string) *vaccineRequirement {
	v.vaccineRequirementDo.DO = *(v.vaccineRequirementDo.As(alias).(*gen.DO))
	return v.updateTableName(alias)
}

func (v *vaccineRequirement) updateTableName(table string) *vaccineRequirement {
	v.ALL = field.NewAsterisk(table)
	v.ID = field.NewInt64(table, "id")
	v.CompanyID = field.NewInt64(table, "company_id")
	v.ServiceItemType = field.NewField(table, "service_item_type")
	v.VaccineID = field.NewInt64(table, "vaccine_id")

	v.fillFieldMap()

	return v
}

func (v *vaccineRequirement) WithContext(ctx context.Context) IVaccineRequirementDo {
	return v.vaccineRequirementDo.WithContext(ctx)
}

func (v vaccineRequirement) TableName() string { return v.vaccineRequirementDo.TableName() }

func (v vaccineRequirement) Alias() string { return v.vaccineRequirementDo.Alias() }

func (v vaccineRequirement) Columns(cols ...field.Expr) gen.Columns {
	return v.vaccineRequirementDo.Columns(cols...)
}

func (v *vaccineRequirement) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := v.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (v *vaccineRequirement) fillFieldMap() {
	v.fieldMap = make(map[string]field.Expr, 4)
	v.fieldMap["id"] = v.ID
	v.fieldMap["company_id"] = v.CompanyID
	v.fieldMap["service_item_type"] = v.ServiceItemType
	v.fieldMap["vaccine_id"] = v.VaccineID
}

func (v vaccineRequirement) clone(db *gorm.DB) vaccineRequirement {
	v.vaccineRequirementDo.ReplaceConnPool(db.Statement.ConnPool)
	return v
}

func (v vaccineRequirement) replaceDB(db *gorm.DB) vaccineRequirement {
	v.vaccineRequirementDo.ReplaceDB(db)
	return v
}

type vaccineRequirementDo struct{ gen.DO }

type IVaccineRequirementDo interface {
	gen.SubQuery
	Debug() IVaccineRequirementDo
	WithContext(ctx context.Context) IVaccineRequirementDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IVaccineRequirementDo
	WriteDB() IVaccineRequirementDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IVaccineRequirementDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IVaccineRequirementDo
	Not(conds ...gen.Condition) IVaccineRequirementDo
	Or(conds ...gen.Condition) IVaccineRequirementDo
	Select(conds ...field.Expr) IVaccineRequirementDo
	Where(conds ...gen.Condition) IVaccineRequirementDo
	Order(conds ...field.Expr) IVaccineRequirementDo
	Distinct(cols ...field.Expr) IVaccineRequirementDo
	Omit(cols ...field.Expr) IVaccineRequirementDo
	Join(table schema.Tabler, on ...field.Expr) IVaccineRequirementDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IVaccineRequirementDo
	RightJoin(table schema.Tabler, on ...field.Expr) IVaccineRequirementDo
	Group(cols ...field.Expr) IVaccineRequirementDo
	Having(conds ...gen.Condition) IVaccineRequirementDo
	Limit(limit int) IVaccineRequirementDo
	Offset(offset int) IVaccineRequirementDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IVaccineRequirementDo
	Unscoped() IVaccineRequirementDo
	Create(values ...*models.VaccineRequirement) error
	CreateInBatches(values []*models.VaccineRequirement, batchSize int) error
	Save(values ...*models.VaccineRequirement) error
	First() (*models.VaccineRequirement, error)
	Take() (*models.VaccineRequirement, error)
	Last() (*models.VaccineRequirement, error)
	Find() ([]*models.VaccineRequirement, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.VaccineRequirement, err error)
	FindInBatches(result *[]*models.VaccineRequirement, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.VaccineRequirement) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IVaccineRequirementDo
	Assign(attrs ...field.AssignExpr) IVaccineRequirementDo
	Joins(fields ...field.RelationField) IVaccineRequirementDo
	Preload(fields ...field.RelationField) IVaccineRequirementDo
	FirstOrInit() (*models.VaccineRequirement, error)
	FirstOrCreate() (*models.VaccineRequirement, error)
	FindByPage(offset int, limit int) (result []*models.VaccineRequirement, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IVaccineRequirementDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (v vaccineRequirementDo) Debug() IVaccineRequirementDo {
	return v.withDO(v.DO.Debug())
}

func (v vaccineRequirementDo) WithContext(ctx context.Context) IVaccineRequirementDo {
	return v.withDO(v.DO.WithContext(ctx))
}

func (v vaccineRequirementDo) ReadDB() IVaccineRequirementDo {
	return v.Clauses(dbresolver.Read)
}

func (v vaccineRequirementDo) WriteDB() IVaccineRequirementDo {
	return v.Clauses(dbresolver.Write)
}

func (v vaccineRequirementDo) Session(config *gorm.Session) IVaccineRequirementDo {
	return v.withDO(v.DO.Session(config))
}

func (v vaccineRequirementDo) Clauses(conds ...clause.Expression) IVaccineRequirementDo {
	return v.withDO(v.DO.Clauses(conds...))
}

func (v vaccineRequirementDo) Returning(value interface{}, columns ...string) IVaccineRequirementDo {
	return v.withDO(v.DO.Returning(value, columns...))
}

func (v vaccineRequirementDo) Not(conds ...gen.Condition) IVaccineRequirementDo {
	return v.withDO(v.DO.Not(conds...))
}

func (v vaccineRequirementDo) Or(conds ...gen.Condition) IVaccineRequirementDo {
	return v.withDO(v.DO.Or(conds...))
}

func (v vaccineRequirementDo) Select(conds ...field.Expr) IVaccineRequirementDo {
	return v.withDO(v.DO.Select(conds...))
}

func (v vaccineRequirementDo) Where(conds ...gen.Condition) IVaccineRequirementDo {
	return v.withDO(v.DO.Where(conds...))
}

func (v vaccineRequirementDo) Order(conds ...field.Expr) IVaccineRequirementDo {
	return v.withDO(v.DO.Order(conds...))
}

func (v vaccineRequirementDo) Distinct(cols ...field.Expr) IVaccineRequirementDo {
	return v.withDO(v.DO.Distinct(cols...))
}

func (v vaccineRequirementDo) Omit(cols ...field.Expr) IVaccineRequirementDo {
	return v.withDO(v.DO.Omit(cols...))
}

func (v vaccineRequirementDo) Join(table schema.Tabler, on ...field.Expr) IVaccineRequirementDo {
	return v.withDO(v.DO.Join(table, on...))
}

func (v vaccineRequirementDo) LeftJoin(table schema.Tabler, on ...field.Expr) IVaccineRequirementDo {
	return v.withDO(v.DO.LeftJoin(table, on...))
}

func (v vaccineRequirementDo) RightJoin(table schema.Tabler, on ...field.Expr) IVaccineRequirementDo {
	return v.withDO(v.DO.RightJoin(table, on...))
}

func (v vaccineRequirementDo) Group(cols ...field.Expr) IVaccineRequirementDo {
	return v.withDO(v.DO.Group(cols...))
}

func (v vaccineRequirementDo) Having(conds ...gen.Condition) IVaccineRequirementDo {
	return v.withDO(v.DO.Having(conds...))
}

func (v vaccineRequirementDo) Limit(limit int) IVaccineRequirementDo {
	return v.withDO(v.DO.Limit(limit))
}

func (v vaccineRequirementDo) Offset(offset int) IVaccineRequirementDo {
	return v.withDO(v.DO.Offset(offset))
}

func (v vaccineRequirementDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IVaccineRequirementDo {
	return v.withDO(v.DO.Scopes(funcs...))
}

func (v vaccineRequirementDo) Unscoped() IVaccineRequirementDo {
	return v.withDO(v.DO.Unscoped())
}

func (v vaccineRequirementDo) Create(values ...*models.VaccineRequirement) error {
	if len(values) == 0 {
		return nil
	}
	return v.DO.Create(values)
}

func (v vaccineRequirementDo) CreateInBatches(values []*models.VaccineRequirement, batchSize int) error {
	return v.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (v vaccineRequirementDo) Save(values ...*models.VaccineRequirement) error {
	if len(values) == 0 {
		return nil
	}
	return v.DO.Save(values)
}

func (v vaccineRequirementDo) First() (*models.VaccineRequirement, error) {
	if result, err := v.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.VaccineRequirement), nil
	}
}

func (v vaccineRequirementDo) Take() (*models.VaccineRequirement, error) {
	if result, err := v.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.VaccineRequirement), nil
	}
}

func (v vaccineRequirementDo) Last() (*models.VaccineRequirement, error) {
	if result, err := v.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.VaccineRequirement), nil
	}
}

func (v vaccineRequirementDo) Find() ([]*models.VaccineRequirement, error) {
	result, err := v.DO.Find()
	return result.([]*models.VaccineRequirement), err
}

func (v vaccineRequirementDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.VaccineRequirement, err error) {
	buf := make([]*models.VaccineRequirement, 0, batchSize)
	err = v.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (v vaccineRequirementDo) FindInBatches(result *[]*models.VaccineRequirement, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return v.DO.FindInBatches(result, batchSize, fc)
}

func (v vaccineRequirementDo) Attrs(attrs ...field.AssignExpr) IVaccineRequirementDo {
	return v.withDO(v.DO.Attrs(attrs...))
}

func (v vaccineRequirementDo) Assign(attrs ...field.AssignExpr) IVaccineRequirementDo {
	return v.withDO(v.DO.Assign(attrs...))
}

func (v vaccineRequirementDo) Joins(fields ...field.RelationField) IVaccineRequirementDo {
	for _, _f := range fields {
		v = *v.withDO(v.DO.Joins(_f))
	}
	return &v
}

func (v vaccineRequirementDo) Preload(fields ...field.RelationField) IVaccineRequirementDo {
	for _, _f := range fields {
		v = *v.withDO(v.DO.Preload(_f))
	}
	return &v
}

func (v vaccineRequirementDo) FirstOrInit() (*models.VaccineRequirement, error) {
	if result, err := v.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.VaccineRequirement), nil
	}
}

func (v vaccineRequirementDo) FirstOrCreate() (*models.VaccineRequirement, error) {
	if result, err := v.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.VaccineRequirement), nil
	}
}

func (v vaccineRequirementDo) FindByPage(offset int, limit int) (result []*models.VaccineRequirement, count int64, err error) {
	result, err = v.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = v.Offset(-1).Limit(-1).Count()
	return
}

func (v vaccineRequirementDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = v.Count()
	if err != nil {
		return
	}

	err = v.Offset(offset).Limit(limit).Scan(result)
	return
}

func (v vaccineRequirementDo) Scan(result interface{}) (err error) {
	return v.DO.Scan(result)
}

func (v vaccineRequirementDo) Delete(models ...*models.VaccineRequirement) (result gen.ResultInfo, err error) {
	return v.DO.Delete(models)
}

func (v *vaccineRequirementDo) withDO(do gen.Dao) *vaccineRequirementDo {
	v.DO = *do.(*gen.DO)
	return v
}
