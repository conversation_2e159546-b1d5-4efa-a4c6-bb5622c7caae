// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newServiceStaffAvailability(db *gorm.DB, opts ...gen.DOOption) serviceStaffAvailability {
	_serviceStaffAvailability := serviceStaffAvailability{}

	_serviceStaffAvailability.serviceStaffAvailabilityDo.UseDB(db, opts...)
	_serviceStaffAvailability.serviceStaffAvailabilityDo.UseModel(&models.ServiceStaffAvailability{})

	tableName := _serviceStaffAvailability.serviceStaffAvailabilityDo.TableName()
	_serviceStaffAvailability.ALL = field.NewAsterisk(tableName)
	_serviceStaffAvailability.ID = field.NewInt64(tableName, "id")
	_serviceStaffAvailability.ServiceID = field.NewInt64(tableName, "service_id")
	_serviceStaffAvailability.StaffID = field.NewInt64(tableName, "staff_id")
	_serviceStaffAvailability.CreatedAt = field.NewTime(tableName, "created_at")
	_serviceStaffAvailability.UpdatedAt = field.NewTime(tableName, "updated_at")

	_serviceStaffAvailability.fillFieldMap()

	return _serviceStaffAvailability
}

type serviceStaffAvailability struct {
	serviceStaffAvailabilityDo serviceStaffAvailabilityDo

	ALL       field.Asterisk
	ID        field.Int64
	ServiceID field.Int64
	StaffID   field.Int64 // available staff id
	CreatedAt field.Time
	UpdatedAt field.Time

	fieldMap map[string]field.Expr
}

func (s serviceStaffAvailability) Table(newTableName string) *serviceStaffAvailability {
	s.serviceStaffAvailabilityDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s serviceStaffAvailability) As(alias string) *serviceStaffAvailability {
	s.serviceStaffAvailabilityDo.DO = *(s.serviceStaffAvailabilityDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *serviceStaffAvailability) updateTableName(table string) *serviceStaffAvailability {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ServiceID = field.NewInt64(table, "service_id")
	s.StaffID = field.NewInt64(table, "staff_id")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")

	s.fillFieldMap()

	return s
}

func (s *serviceStaffAvailability) WithContext(ctx context.Context) IServiceStaffAvailabilityDo {
	return s.serviceStaffAvailabilityDo.WithContext(ctx)
}

func (s serviceStaffAvailability) TableName() string { return s.serviceStaffAvailabilityDo.TableName() }

func (s serviceStaffAvailability) Alias() string { return s.serviceStaffAvailabilityDo.Alias() }

func (s serviceStaffAvailability) Columns(cols ...field.Expr) gen.Columns {
	return s.serviceStaffAvailabilityDo.Columns(cols...)
}

func (s *serviceStaffAvailability) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *serviceStaffAvailability) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 5)
	s.fieldMap["id"] = s.ID
	s.fieldMap["service_id"] = s.ServiceID
	s.fieldMap["staff_id"] = s.StaffID
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
}

func (s serviceStaffAvailability) clone(db *gorm.DB) serviceStaffAvailability {
	s.serviceStaffAvailabilityDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s serviceStaffAvailability) replaceDB(db *gorm.DB) serviceStaffAvailability {
	s.serviceStaffAvailabilityDo.ReplaceDB(db)
	return s
}

type serviceStaffAvailabilityDo struct{ gen.DO }

type IServiceStaffAvailabilityDo interface {
	gen.SubQuery
	Debug() IServiceStaffAvailabilityDo
	WithContext(ctx context.Context) IServiceStaffAvailabilityDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IServiceStaffAvailabilityDo
	WriteDB() IServiceStaffAvailabilityDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IServiceStaffAvailabilityDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IServiceStaffAvailabilityDo
	Not(conds ...gen.Condition) IServiceStaffAvailabilityDo
	Or(conds ...gen.Condition) IServiceStaffAvailabilityDo
	Select(conds ...field.Expr) IServiceStaffAvailabilityDo
	Where(conds ...gen.Condition) IServiceStaffAvailabilityDo
	Order(conds ...field.Expr) IServiceStaffAvailabilityDo
	Distinct(cols ...field.Expr) IServiceStaffAvailabilityDo
	Omit(cols ...field.Expr) IServiceStaffAvailabilityDo
	Join(table schema.Tabler, on ...field.Expr) IServiceStaffAvailabilityDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IServiceStaffAvailabilityDo
	RightJoin(table schema.Tabler, on ...field.Expr) IServiceStaffAvailabilityDo
	Group(cols ...field.Expr) IServiceStaffAvailabilityDo
	Having(conds ...gen.Condition) IServiceStaffAvailabilityDo
	Limit(limit int) IServiceStaffAvailabilityDo
	Offset(offset int) IServiceStaffAvailabilityDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IServiceStaffAvailabilityDo
	Unscoped() IServiceStaffAvailabilityDo
	Create(values ...*models.ServiceStaffAvailability) error
	CreateInBatches(values []*models.ServiceStaffAvailability, batchSize int) error
	Save(values ...*models.ServiceStaffAvailability) error
	First() (*models.ServiceStaffAvailability, error)
	Take() (*models.ServiceStaffAvailability, error)
	Last() (*models.ServiceStaffAvailability, error)
	Find() ([]*models.ServiceStaffAvailability, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.ServiceStaffAvailability, err error)
	FindInBatches(result *[]*models.ServiceStaffAvailability, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.ServiceStaffAvailability) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IServiceStaffAvailabilityDo
	Assign(attrs ...field.AssignExpr) IServiceStaffAvailabilityDo
	Joins(fields ...field.RelationField) IServiceStaffAvailabilityDo
	Preload(fields ...field.RelationField) IServiceStaffAvailabilityDo
	FirstOrInit() (*models.ServiceStaffAvailability, error)
	FirstOrCreate() (*models.ServiceStaffAvailability, error)
	FindByPage(offset int, limit int) (result []*models.ServiceStaffAvailability, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IServiceStaffAvailabilityDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s serviceStaffAvailabilityDo) Debug() IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Debug())
}

func (s serviceStaffAvailabilityDo) WithContext(ctx context.Context) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s serviceStaffAvailabilityDo) ReadDB() IServiceStaffAvailabilityDo {
	return s.Clauses(dbresolver.Read)
}

func (s serviceStaffAvailabilityDo) WriteDB() IServiceStaffAvailabilityDo {
	return s.Clauses(dbresolver.Write)
}

func (s serviceStaffAvailabilityDo) Session(config *gorm.Session) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Session(config))
}

func (s serviceStaffAvailabilityDo) Clauses(conds ...clause.Expression) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s serviceStaffAvailabilityDo) Returning(value interface{}, columns ...string) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s serviceStaffAvailabilityDo) Not(conds ...gen.Condition) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s serviceStaffAvailabilityDo) Or(conds ...gen.Condition) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s serviceStaffAvailabilityDo) Select(conds ...field.Expr) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s serviceStaffAvailabilityDo) Where(conds ...gen.Condition) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s serviceStaffAvailabilityDo) Order(conds ...field.Expr) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s serviceStaffAvailabilityDo) Distinct(cols ...field.Expr) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s serviceStaffAvailabilityDo) Omit(cols ...field.Expr) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s serviceStaffAvailabilityDo) Join(table schema.Tabler, on ...field.Expr) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s serviceStaffAvailabilityDo) LeftJoin(table schema.Tabler, on ...field.Expr) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s serviceStaffAvailabilityDo) RightJoin(table schema.Tabler, on ...field.Expr) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s serviceStaffAvailabilityDo) Group(cols ...field.Expr) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s serviceStaffAvailabilityDo) Having(conds ...gen.Condition) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s serviceStaffAvailabilityDo) Limit(limit int) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s serviceStaffAvailabilityDo) Offset(offset int) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s serviceStaffAvailabilityDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s serviceStaffAvailabilityDo) Unscoped() IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Unscoped())
}

func (s serviceStaffAvailabilityDo) Create(values ...*models.ServiceStaffAvailability) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s serviceStaffAvailabilityDo) CreateInBatches(values []*models.ServiceStaffAvailability, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s serviceStaffAvailabilityDo) Save(values ...*models.ServiceStaffAvailability) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s serviceStaffAvailabilityDo) First() (*models.ServiceStaffAvailability, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceStaffAvailability), nil
	}
}

func (s serviceStaffAvailabilityDo) Take() (*models.ServiceStaffAvailability, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceStaffAvailability), nil
	}
}

func (s serviceStaffAvailabilityDo) Last() (*models.ServiceStaffAvailability, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceStaffAvailability), nil
	}
}

func (s serviceStaffAvailabilityDo) Find() ([]*models.ServiceStaffAvailability, error) {
	result, err := s.DO.Find()
	return result.([]*models.ServiceStaffAvailability), err
}

func (s serviceStaffAvailabilityDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.ServiceStaffAvailability, err error) {
	buf := make([]*models.ServiceStaffAvailability, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s serviceStaffAvailabilityDo) FindInBatches(result *[]*models.ServiceStaffAvailability, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s serviceStaffAvailabilityDo) Attrs(attrs ...field.AssignExpr) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s serviceStaffAvailabilityDo) Assign(attrs ...field.AssignExpr) IServiceStaffAvailabilityDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s serviceStaffAvailabilityDo) Joins(fields ...field.RelationField) IServiceStaffAvailabilityDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s serviceStaffAvailabilityDo) Preload(fields ...field.RelationField) IServiceStaffAvailabilityDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s serviceStaffAvailabilityDo) FirstOrInit() (*models.ServiceStaffAvailability, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceStaffAvailability), nil
	}
}

func (s serviceStaffAvailabilityDo) FirstOrCreate() (*models.ServiceStaffAvailability, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.ServiceStaffAvailability), nil
	}
}

func (s serviceStaffAvailabilityDo) FindByPage(offset int, limit int) (result []*models.ServiceStaffAvailability, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s serviceStaffAvailabilityDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s serviceStaffAvailabilityDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s serviceStaffAvailabilityDo) Delete(models ...*models.ServiceStaffAvailability) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *serviceStaffAvailabilityDo) withDO(do gen.Dao) *serviceStaffAvailabilityDo {
	s.DO = *do.(*gen.DO)
	return s
}
