// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func newPricingRuleRecord(db *gorm.DB, opts ...gen.DOOption) pricingRuleRecord {
	_pricingRuleRecord := pricingRuleRecord{}

	_pricingRuleRecord.pricingRuleRecordDo.UseDB(db, opts...)
	_pricingRuleRecord.pricingRuleRecordDo.UseModel(&models.PricingRuleRecord{})

	tableName := _pricingRuleRecord.pricingRuleRecordDo.TableName()
	_pricingRuleRecord.ALL = field.NewAsterisk(tableName)
	_pricingRuleRecord.ID = field.NewInt64(tableName, "id")
	_pricingRuleRecord.CompanyID = field.NewInt64(tableName, "company_id")
	_pricingRuleRecord.RuleType = field.NewField(tableName, "rule_type")
	_pricingRuleRecord.RuleName = field.NewString(tableName, "rule_name")
	_pricingRuleRecord.IsActive = field.NewBool(tableName, "is_active")
	_pricingRuleRecord.AllBoardingApplicable = field.NewBool(tableName, "all_boarding_applicable")
	_pricingRuleRecord.SelectedBoardingServices = field.NewField(tableName, "selected_boarding_services")
	_pricingRuleRecord.AllDaycareApplicable = field.NewBool(tableName, "all_daycare_applicable")
	_pricingRuleRecord.SelectedDaycareServices = field.NewField(tableName, "selected_daycare_services")
	_pricingRuleRecord.RuleApplyType = field.NewField(tableName, "rule_apply_type")
	_pricingRuleRecord.NeedInSameLodging = field.NewBool(tableName, "need_in_same_lodging")
	_pricingRuleRecord.RuleConfiguration = field.NewField(tableName, "rule_configuration")
	_pricingRuleRecord.UpdatedBy = field.NewInt64(tableName, "updated_by")
	_pricingRuleRecord.CreatedAt = field.NewTime(tableName, "created_at")
	_pricingRuleRecord.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pricingRuleRecord.DeletedAt = field.NewField(tableName, "deleted_at")
	_pricingRuleRecord.IsChargePerLodging = field.NewBool(tableName, "is_charge_per_lodging")
	_pricingRuleRecord.Source = field.NewField(tableName, "source")
	_pricingRuleRecord.AllGroomingApplicable = field.NewBool(tableName, "all_grooming_applicable")
	_pricingRuleRecord.SelectedGroomingServices = field.NewField(tableName, "selected_grooming_services")
	_pricingRuleRecord.AllAddonApplicable = field.NewBool(tableName, "all_addon_applicable")
	_pricingRuleRecord.SelectedAddonServices = field.NewField(tableName, "selected_addon_services")

	_pricingRuleRecord.fillFieldMap()

	return _pricingRuleRecord
}

type pricingRuleRecord struct {
	pricingRuleRecordDo pricingRuleRecordDo

	ALL                      field.Asterisk
	ID                       field.Int64
	CompanyID                field.Int64
	RuleType                 field.Field // rule type, 1-multi pets, 2-multi duration, 3-peak date
	RuleName                 field.String
	IsActive                 field.Bool
	AllBoardingApplicable    field.Bool
	SelectedBoardingServices field.Field
	AllDaycareApplicable     field.Bool
	SelectedDaycareServices  field.Field
	RuleApplyType            field.Field // rule apply type, 1-each pet, 2-additional pets, 3-all pets(deprecated), 4-first pet
	NeedInSameLodging        field.Bool
	RuleConfiguration        field.Field
	UpdatedBy                field.Int64
	CreatedAt                field.Time
	UpdatedAt                field.Time
	DeletedAt                field.Field
	IsChargePerLodging       field.Bool
	Source                   field.Field // 1-MoeGo Platform 2-Enterprise Hub
	AllGroomingApplicable    field.Bool
	SelectedGroomingServices field.Field
	AllAddonApplicable       field.Bool
	SelectedAddonServices    field.Field

	fieldMap map[string]field.Expr
}

func (p pricingRuleRecord) Table(newTableName string) *pricingRuleRecord {
	p.pricingRuleRecordDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pricingRuleRecord) As(alias string) *pricingRuleRecord {
	p.pricingRuleRecordDo.DO = *(p.pricingRuleRecordDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pricingRuleRecord) updateTableName(table string) *pricingRuleRecord {
	p.ALL = field.NewAsterisk(table)
	p.ID = field.NewInt64(table, "id")
	p.CompanyID = field.NewInt64(table, "company_id")
	p.RuleType = field.NewField(table, "rule_type")
	p.RuleName = field.NewString(table, "rule_name")
	p.IsActive = field.NewBool(table, "is_active")
	p.AllBoardingApplicable = field.NewBool(table, "all_boarding_applicable")
	p.SelectedBoardingServices = field.NewField(table, "selected_boarding_services")
	p.AllDaycareApplicable = field.NewBool(table, "all_daycare_applicable")
	p.SelectedDaycareServices = field.NewField(table, "selected_daycare_services")
	p.RuleApplyType = field.NewField(table, "rule_apply_type")
	p.NeedInSameLodging = field.NewBool(table, "need_in_same_lodging")
	p.RuleConfiguration = field.NewField(table, "rule_configuration")
	p.UpdatedBy = field.NewInt64(table, "updated_by")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.DeletedAt = field.NewField(table, "deleted_at")
	p.IsChargePerLodging = field.NewBool(table, "is_charge_per_lodging")
	p.Source = field.NewField(table, "source")
	p.AllGroomingApplicable = field.NewBool(table, "all_grooming_applicable")
	p.SelectedGroomingServices = field.NewField(table, "selected_grooming_services")
	p.AllAddonApplicable = field.NewBool(table, "all_addon_applicable")
	p.SelectedAddonServices = field.NewField(table, "selected_addon_services")

	p.fillFieldMap()

	return p
}

func (p *pricingRuleRecord) WithContext(ctx context.Context) IPricingRuleRecordDo {
	return p.pricingRuleRecordDo.WithContext(ctx)
}

func (p pricingRuleRecord) TableName() string { return p.pricingRuleRecordDo.TableName() }

func (p pricingRuleRecord) Alias() string { return p.pricingRuleRecordDo.Alias() }

func (p pricingRuleRecord) Columns(cols ...field.Expr) gen.Columns {
	return p.pricingRuleRecordDo.Columns(cols...)
}

func (p *pricingRuleRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pricingRuleRecord) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 22)
	p.fieldMap["id"] = p.ID
	p.fieldMap["company_id"] = p.CompanyID
	p.fieldMap["rule_type"] = p.RuleType
	p.fieldMap["rule_name"] = p.RuleName
	p.fieldMap["is_active"] = p.IsActive
	p.fieldMap["all_boarding_applicable"] = p.AllBoardingApplicable
	p.fieldMap["selected_boarding_services"] = p.SelectedBoardingServices
	p.fieldMap["all_daycare_applicable"] = p.AllDaycareApplicable
	p.fieldMap["selected_daycare_services"] = p.SelectedDaycareServices
	p.fieldMap["rule_apply_type"] = p.RuleApplyType
	p.fieldMap["need_in_same_lodging"] = p.NeedInSameLodging
	p.fieldMap["rule_configuration"] = p.RuleConfiguration
	p.fieldMap["updated_by"] = p.UpdatedBy
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["deleted_at"] = p.DeletedAt
	p.fieldMap["is_charge_per_lodging"] = p.IsChargePerLodging
	p.fieldMap["source"] = p.Source
	p.fieldMap["all_grooming_applicable"] = p.AllGroomingApplicable
	p.fieldMap["selected_grooming_services"] = p.SelectedGroomingServices
	p.fieldMap["all_addon_applicable"] = p.AllAddonApplicable
	p.fieldMap["selected_addon_services"] = p.SelectedAddonServices
}

func (p pricingRuleRecord) clone(db *gorm.DB) pricingRuleRecord {
	p.pricingRuleRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p pricingRuleRecord) replaceDB(db *gorm.DB) pricingRuleRecord {
	p.pricingRuleRecordDo.ReplaceDB(db)
	return p
}

type pricingRuleRecordDo struct{ gen.DO }

type IPricingRuleRecordDo interface {
	gen.SubQuery
	Debug() IPricingRuleRecordDo
	WithContext(ctx context.Context) IPricingRuleRecordDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPricingRuleRecordDo
	WriteDB() IPricingRuleRecordDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPricingRuleRecordDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPricingRuleRecordDo
	Not(conds ...gen.Condition) IPricingRuleRecordDo
	Or(conds ...gen.Condition) IPricingRuleRecordDo
	Select(conds ...field.Expr) IPricingRuleRecordDo
	Where(conds ...gen.Condition) IPricingRuleRecordDo
	Order(conds ...field.Expr) IPricingRuleRecordDo
	Distinct(cols ...field.Expr) IPricingRuleRecordDo
	Omit(cols ...field.Expr) IPricingRuleRecordDo
	Join(table schema.Tabler, on ...field.Expr) IPricingRuleRecordDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPricingRuleRecordDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPricingRuleRecordDo
	Group(cols ...field.Expr) IPricingRuleRecordDo
	Having(conds ...gen.Condition) IPricingRuleRecordDo
	Limit(limit int) IPricingRuleRecordDo
	Offset(offset int) IPricingRuleRecordDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPricingRuleRecordDo
	Unscoped() IPricingRuleRecordDo
	Create(values ...*models.PricingRuleRecord) error
	CreateInBatches(values []*models.PricingRuleRecord, batchSize int) error
	Save(values ...*models.PricingRuleRecord) error
	First() (*models.PricingRuleRecord, error)
	Take() (*models.PricingRuleRecord, error)
	Last() (*models.PricingRuleRecord, error)
	Find() ([]*models.PricingRuleRecord, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PricingRuleRecord, err error)
	FindInBatches(result *[]*models.PricingRuleRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*models.PricingRuleRecord) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPricingRuleRecordDo
	Assign(attrs ...field.AssignExpr) IPricingRuleRecordDo
	Joins(fields ...field.RelationField) IPricingRuleRecordDo
	Preload(fields ...field.RelationField) IPricingRuleRecordDo
	FirstOrInit() (*models.PricingRuleRecord, error)
	FirstOrCreate() (*models.PricingRuleRecord, error)
	FindByPage(offset int, limit int) (result []*models.PricingRuleRecord, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPricingRuleRecordDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pricingRuleRecordDo) Debug() IPricingRuleRecordDo {
	return p.withDO(p.DO.Debug())
}

func (p pricingRuleRecordDo) WithContext(ctx context.Context) IPricingRuleRecordDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pricingRuleRecordDo) ReadDB() IPricingRuleRecordDo {
	return p.Clauses(dbresolver.Read)
}

func (p pricingRuleRecordDo) WriteDB() IPricingRuleRecordDo {
	return p.Clauses(dbresolver.Write)
}

func (p pricingRuleRecordDo) Session(config *gorm.Session) IPricingRuleRecordDo {
	return p.withDO(p.DO.Session(config))
}

func (p pricingRuleRecordDo) Clauses(conds ...clause.Expression) IPricingRuleRecordDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pricingRuleRecordDo) Returning(value interface{}, columns ...string) IPricingRuleRecordDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pricingRuleRecordDo) Not(conds ...gen.Condition) IPricingRuleRecordDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pricingRuleRecordDo) Or(conds ...gen.Condition) IPricingRuleRecordDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pricingRuleRecordDo) Select(conds ...field.Expr) IPricingRuleRecordDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pricingRuleRecordDo) Where(conds ...gen.Condition) IPricingRuleRecordDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pricingRuleRecordDo) Order(conds ...field.Expr) IPricingRuleRecordDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pricingRuleRecordDo) Distinct(cols ...field.Expr) IPricingRuleRecordDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pricingRuleRecordDo) Omit(cols ...field.Expr) IPricingRuleRecordDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pricingRuleRecordDo) Join(table schema.Tabler, on ...field.Expr) IPricingRuleRecordDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pricingRuleRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPricingRuleRecordDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pricingRuleRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) IPricingRuleRecordDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pricingRuleRecordDo) Group(cols ...field.Expr) IPricingRuleRecordDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pricingRuleRecordDo) Having(conds ...gen.Condition) IPricingRuleRecordDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pricingRuleRecordDo) Limit(limit int) IPricingRuleRecordDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pricingRuleRecordDo) Offset(offset int) IPricingRuleRecordDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pricingRuleRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPricingRuleRecordDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pricingRuleRecordDo) Unscoped() IPricingRuleRecordDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pricingRuleRecordDo) Create(values ...*models.PricingRuleRecord) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pricingRuleRecordDo) CreateInBatches(values []*models.PricingRuleRecord, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pricingRuleRecordDo) Save(values ...*models.PricingRuleRecord) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pricingRuleRecordDo) First() (*models.PricingRuleRecord, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*models.PricingRuleRecord), nil
	}
}

func (p pricingRuleRecordDo) Take() (*models.PricingRuleRecord, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*models.PricingRuleRecord), nil
	}
}

func (p pricingRuleRecordDo) Last() (*models.PricingRuleRecord, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*models.PricingRuleRecord), nil
	}
}

func (p pricingRuleRecordDo) Find() ([]*models.PricingRuleRecord, error) {
	result, err := p.DO.Find()
	return result.([]*models.PricingRuleRecord), err
}

func (p pricingRuleRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*models.PricingRuleRecord, err error) {
	buf := make([]*models.PricingRuleRecord, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pricingRuleRecordDo) FindInBatches(result *[]*models.PricingRuleRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pricingRuleRecordDo) Attrs(attrs ...field.AssignExpr) IPricingRuleRecordDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pricingRuleRecordDo) Assign(attrs ...field.AssignExpr) IPricingRuleRecordDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pricingRuleRecordDo) Joins(fields ...field.RelationField) IPricingRuleRecordDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pricingRuleRecordDo) Preload(fields ...field.RelationField) IPricingRuleRecordDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pricingRuleRecordDo) FirstOrInit() (*models.PricingRuleRecord, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*models.PricingRuleRecord), nil
	}
}

func (p pricingRuleRecordDo) FirstOrCreate() (*models.PricingRuleRecord, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*models.PricingRuleRecord), nil
	}
}

func (p pricingRuleRecordDo) FindByPage(offset int, limit int) (result []*models.PricingRuleRecord, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pricingRuleRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pricingRuleRecordDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pricingRuleRecordDo) Delete(models ...*models.PricingRuleRecord) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pricingRuleRecordDo) withDO(do gen.Dao) *pricingRuleRecordDo {
	p.DO = *do.(*gen.DO)
	return p
}
