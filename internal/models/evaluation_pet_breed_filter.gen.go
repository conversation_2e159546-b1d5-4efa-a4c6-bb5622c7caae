// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package models

import (
	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	"time"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

const TableNameEvaluationPetBreedFilter = "evaluation_pet_breed_filter"

// EvaluationPetBreedFilter mapped from table <evaluation_pet_breed_filter>
type EvaluationPetBreedFilter struct {
	ID           int64              `gorm:"column:id;not null" json:"id"`
	EvaluationID int64              `gorm:"column:evaluation_id;not null;comment:evaluation.id" json:"evaluation_id"`                                                                     // evaluation.id
	PetType      customerpb.PetType `gorm:"column:pet_type;not null;comment:Pet type enum, 1-Dog, 2-Cat,..., 11-Other" json:"pet_type"`                                                   // Pet type enum, 1-<PERSON>, 2-<PERSON>,..., 11-Other
	IsAllBreed   bool               `gorm:"column:is_all_breed;not null;comment:If true, all breeds are selected" json:"is_all_breed"`                                                    // If true, all breeds are selected
	BreedNames   pq.StringArray     `gorm:"column:breed_names;type:text[];not null;default:ARRAY[];comment:Breed names, if is_all_breed is true, this field is empty" json:"breed_names"` // Breed names, if is_all_breed is true, this field is empty
	CreatedAt    *time.Time         `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt    *time.Time         `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
	DeletedAt    gorm.DeletedAt     `gorm:"column:deleted_at" json:"deleted_at"`
}

// TableName EvaluationPetBreedFilter's table name
func (*EvaluationPetBreedFilter) TableName() string {
	return TableNameEvaluationPetBreedFilter
}
