package models

import (
	"time"

	"gorm.io/gorm"
)

const TableNameGroupClassSession = "group_class_session"

type GroupClassSession struct {
	ID          int64          `gorm:"column:id;primaryKey;autoIncrement:true"`
	CompanyID   int64          `gorm:"column:company_id"`
	BusinessID  int64          `gorm:"column:business_id"`
	InstanceID  int64          `gorm:"column:instance_id"`
	StartTime   *time.Time     `gorm:"column:start_time"`
	DurationMin int64          `gorm:"column:duration_min"`
	IsModified  *bool          `gorm:"column:is_modified"`
	CreatedAt   *time.Time     `gorm:"column:created_at"`
	UpdatedAt   *time.Time     `gorm:"column:updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"column:deleted_at"`
}

func (*GroupClassSession) TableName() string {
	return TableNameGroupClassSession
}
