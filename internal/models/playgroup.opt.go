package models

import (
	"time"

	"gorm.io/gorm"
)

type PlaygroupWhereOpt struct {
	ID             *int64  `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	IDs            []int64 `gorm:"column:id;query_expr:in"`
	ExcludedID     *int64  `gorm:"column:id;query_expr:!="`
	ExcludedIDs    []int64 `gorm:"column:id;query_expr:not in"`
	Name           *string `gorm:"column:name;not null" json:"name"`
	CompanyID      *int64  `gorm:"column:company_id;not null" json:"company_id"`
	IncludeDeleted *bool   `gorm:"-" json:"include_deleted"`
}

type PlaygroupUpdateOpt struct {
	Name           *string    `gorm:"column:name;not null" json:"name"`
	ColorCode      *string    `gorm:"column:color_code;not null" json:"color_code"`
	MaxPetCapacity *int32     `gorm:"column:max_pet_capacity;not null" json:"max_pet_capacity"`
	Description    *string    `gorm:"column:description;not null" json:"description"`
	Sort           *int32     `gorm:"column:sort;not null" json:"sort"`
	UpdatedBy      *int64     `gorm:"column:updated_by;not null" json:"updated_by"`
	UpdatedAt      *time.Time `gorm:"column:updated_at;not null" json:"updated_at"`
}

type PlaygroupDeleteOpt struct {
	DeletedBy *int64         `gorm:"column:deleted_by;not null" json:"deleted_by"`
	UpdatedAt *time.Time     `gorm:"column:updated_at;not null" json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at" json:"deleted_at"`
}
