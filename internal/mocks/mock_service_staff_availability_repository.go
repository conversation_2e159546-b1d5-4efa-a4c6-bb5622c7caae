// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: ServiceStaffAvailabilityRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_service_staff_availability_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceStaffAvailabilityRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	gomock "go.uber.org/mock/gomock"
)

// MockServiceStaffAvailabilityRepository is a mock of ServiceStaffAvailabilityRepository interface.
type MockServiceStaffAvailabilityRepository struct {
	ctrl     *gomock.Controller
	recorder *MockServiceStaffAvailabilityRepositoryMockRecorder
	isgomock struct{}
}

// MockServiceStaffAvailabilityRepositoryMockRecorder is the mock recorder for MockServiceStaffAvailabilityRepository.
type MockServiceStaffAvailabilityRepositoryMockRecorder struct {
	mock *MockServiceStaffAvailabilityRepository
}

// NewMockServiceStaffAvailabilityRepository creates a new mock instance.
func NewMockServiceStaffAvailabilityRepository(ctrl *gomock.Controller) *MockServiceStaffAvailabilityRepository {
	mock := &MockServiceStaffAvailabilityRepository{ctrl: ctrl}
	mock.recorder = &MockServiceStaffAvailabilityRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceStaffAvailabilityRepository) EXPECT() *MockServiceStaffAvailabilityRepositoryMockRecorder {
	return m.recorder
}

// BatchCreateStaffAvailability mocks base method.
func (m *MockServiceStaffAvailabilityRepository) BatchCreateStaffAvailability(ctx context.Context, serviceId int64, staffId []int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateStaffAvailability", ctx, serviceId, staffId)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateStaffAvailability indicates an expected call of BatchCreateStaffAvailability.
func (mr *MockServiceStaffAvailabilityRepositoryMockRecorder) BatchCreateStaffAvailability(ctx, serviceId, staffId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateStaffAvailability", reflect.TypeOf((*MockServiceStaffAvailabilityRepository)(nil).BatchCreateStaffAvailability), ctx, serviceId, staffId)
}

// GetAvailableStaffRulesByServiceIDList mocks base method.
func (m *MockServiceStaffAvailabilityRepository) GetAvailableStaffRulesByServiceIDList(ctx context.Context, serviceIds []int64) ([]do.ServiceStaffAvailabilityDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailableStaffRulesByServiceIDList", ctx, serviceIds)
	ret0, _ := ret[0].([]do.ServiceStaffAvailabilityDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableStaffRulesByServiceIDList indicates an expected call of GetAvailableStaffRulesByServiceIDList.
func (mr *MockServiceStaffAvailabilityRepositoryMockRecorder) GetAvailableStaffRulesByServiceIDList(ctx, serviceIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableStaffRulesByServiceIDList", reflect.TypeOf((*MockServiceStaffAvailabilityRepository)(nil).GetAvailableStaffRulesByServiceIDList), ctx, serviceIds)
}

// GetAvailableStaffsByServiceID mocks base method.
func (m *MockServiceStaffAvailabilityRepository) GetAvailableStaffsByServiceID(ctx context.Context, serviceId int64) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAvailableStaffsByServiceID", ctx, serviceId)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableStaffsByServiceID indicates an expected call of GetAvailableStaffsByServiceID.
func (mr *MockServiceStaffAvailabilityRepositoryMockRecorder) GetAvailableStaffsByServiceID(ctx, serviceId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableStaffsByServiceID", reflect.TypeOf((*MockServiceStaffAvailabilityRepository)(nil).GetAvailableStaffsByServiceID), ctx, serviceId)
}

// RemoveServiceStaffAvailability mocks base method.
func (m *MockServiceStaffAvailabilityRepository) RemoveServiceStaffAvailability(ctx context.Context, serviceId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveServiceStaffAvailability", ctx, serviceId)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveServiceStaffAvailability indicates an expected call of RemoveServiceStaffAvailability.
func (mr *MockServiceStaffAvailabilityRepositoryMockRecorder) RemoveServiceStaffAvailability(ctx, serviceId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveServiceStaffAvailability", reflect.TypeOf((*MockServiceStaffAvailabilityRepository)(nil).RemoveServiceStaffAvailability), ctx, serviceId)
}

// RemoveServiceStaffAvailabilityByStaffIds mocks base method.
func (m *MockServiceStaffAvailabilityRepository) RemoveServiceStaffAvailabilityByStaffIds(ctx context.Context, serviceId int64, staffIds []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveServiceStaffAvailabilityByStaffIds", ctx, serviceId, staffIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveServiceStaffAvailabilityByStaffIds indicates an expected call of RemoveServiceStaffAvailabilityByStaffIds.
func (mr *MockServiceStaffAvailabilityRepositoryMockRecorder) RemoveServiceStaffAvailabilityByStaffIds(ctx, serviceId, staffIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveServiceStaffAvailabilityByStaffIds", reflect.TypeOf((*MockServiceStaffAvailabilityRepository)(nil).RemoveServiceStaffAvailabilityByStaffIds), ctx, serviceId, staffIds)
}

// UpdateServiceStaffAvailability mocks base method.
func (m *MockServiceStaffAvailabilityRepository) UpdateServiceStaffAvailability(ctx context.Context, serviceId int64, staffId []int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateServiceStaffAvailability", ctx, serviceId, staffId)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateServiceStaffAvailability indicates an expected call of UpdateServiceStaffAvailability.
func (mr *MockServiceStaffAvailabilityRepositoryMockRecorder) UpdateServiceStaffAvailability(ctx, serviceId, staffId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateServiceStaffAvailability", reflect.TypeOf((*MockServiceStaffAvailabilityRepository)(nil).UpdateServiceStaffAvailability), ctx, serviceId, staffId)
}
