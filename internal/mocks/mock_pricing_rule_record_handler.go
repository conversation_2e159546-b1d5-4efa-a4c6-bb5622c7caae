// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/service (interfaces: PricingRuleRecordHandler)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_pricing_rule_record_handler.go github.com/MoeGolibrary/moego-svc-offering/internal/service PricingRuleRecordHandler
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	gomock "go.uber.org/mock/gomock"
)

// MockPricingRuleRecordHandler is a mock of PricingRuleRecordHandler interface.
type MockPricingRuleRecordHandler struct {
	ctrl     *gomock.Controller
	recorder *MockPricingRuleRecordHandlerMockRecorder
	isgomock struct{}
}

// MockPricingRuleRecordHandlerMockRecorder is the mock recorder for MockPricingRuleRecordHandler.
type MockPricingRuleRecordHandlerMockRecorder struct {
	mock *MockPricingRuleRecordHandler
}

// NewMockPricingRuleRecordHandler creates a new mock instance.
func NewMockPricingRuleRecordHandler(ctrl *gomock.Controller) *MockPricingRuleRecordHandler {
	mock := &MockPricingRuleRecordHandler{ctrl: ctrl}
	mock.recorder = &MockPricingRuleRecordHandlerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPricingRuleRecordHandler) EXPECT() *MockPricingRuleRecordHandlerMockRecorder {
	return m.recorder
}

// Calculate mocks base method.
func (m *MockPricingRuleRecordHandler) Calculate(ctx context.Context, request *do.CalculateCompositeDO) (*do.CalculateCompositeResultDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Calculate", ctx, request)
	ret0, _ := ret[0].(*do.CalculateCompositeResultDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Calculate indicates an expected call of Calculate.
func (mr *MockPricingRuleRecordHandlerMockRecorder) Calculate(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Calculate", reflect.TypeOf((*MockPricingRuleRecordHandler)(nil).Calculate), ctx, request)
}

// Delete mocks base method.
func (m *MockPricingRuleRecordHandler) Delete(ctx context.Context, companyId, pricingRuleId, deleteBy int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, companyId, pricingRuleId, deleteBy)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockPricingRuleRecordHandlerMockRecorder) Delete(ctx, companyId, pricingRuleId, deleteBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockPricingRuleRecordHandler)(nil).Delete), ctx, companyId, pricingRuleId, deleteBy)
}

// GetById mocks base method.
func (m *MockPricingRuleRecordHandler) GetById(ctx context.Context, companyId, pricingRuleId int64) (*do.PricingRuleRecordDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, companyId, pricingRuleId)
	ret0, _ := ret[0].(*do.PricingRuleRecordDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockPricingRuleRecordHandlerMockRecorder) GetById(ctx, companyId, pricingRuleId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockPricingRuleRecordHandler)(nil).GetById), ctx, companyId, pricingRuleId)
}

// ListByPage mocks base method.
func (m *MockPricingRuleRecordHandler) ListByPage(ctx context.Context, whereOpt *models.PricingRuleRecordWhereOpt, pagination *utilsV2.PaginationRequest) ([]*do.PricingRuleRecordDO, int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByPage", ctx, whereOpt, pagination)
	ret0, _ := ret[0].([]*do.PricingRuleRecordDO)
	ret1, _ := ret[1].(int32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListByPage indicates an expected call of ListByPage.
func (mr *MockPricingRuleRecordHandlerMockRecorder) ListByPage(ctx, whereOpt, pagination any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByPage", reflect.TypeOf((*MockPricingRuleRecordHandler)(nil).ListByPage), ctx, whereOpt, pagination)
}

// ListServiceAddressOverrideRules mocks base method.
func (m *MockPricingRuleRecordHandler) ListServiceAddressOverrideRules(ctx context.Context, companyId int64, conditions map[int64][]do.AddressOverrideCondition) (map[int64][]do.AddressOverrideRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListServiceAddressOverrideRules", ctx, companyId, conditions)
	ret0, _ := ret[0].(map[int64][]do.AddressOverrideRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListServiceAddressOverrideRules indicates an expected call of ListServiceAddressOverrideRules.
func (mr *MockPricingRuleRecordHandlerMockRecorder) ListServiceAddressOverrideRules(ctx, companyId, conditions any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListServiceAddressOverrideRules", reflect.TypeOf((*MockPricingRuleRecordHandler)(nil).ListServiceAddressOverrideRules), ctx, companyId, conditions)
}

// Upsert mocks base method.
func (m *MockPricingRuleRecordHandler) Upsert(ctx context.Context, pricingRuleDo *do.PricingRuleRecordDO) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, pricingRuleDo)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upsert indicates an expected call of Upsert.
func (mr *MockPricingRuleRecordHandlerMockRecorder) Upsert(ctx, pricingRuleDo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockPricingRuleRecordHandler)(nil).Upsert), ctx, pricingRuleDo)
}
