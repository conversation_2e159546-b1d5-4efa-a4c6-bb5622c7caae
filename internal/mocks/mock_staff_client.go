// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/clients (interfaces: StaffClient)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_staff_client.go github.com/MoeGolibrary/moego-svc-offering/internal/clients StaffClient
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockStaffClient is a mock of StaffClient interface.
type MockStaffClient struct {
	ctrl     *gomock.Controller
	recorder *MockStaffClientMockRecorder
}

// MockStaffClientMockRecorder is the mock recorder for MockStaffClient.
type MockStaffClientMockRecorder struct {
	mock *MockStaffClient
}

// NewMockStaffClient creates a new mock instance.
func NewMockStaffClient(ctrl *gomock.Controller) *MockStaffClient {
	mock := &MockStaffClient{ctrl: ctrl}
	mock.recorder = &MockStaffClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStaffClient) EXPECT() *MockStaffClientMockRecorder {
	return m.recorder
}

// ListAllStaffIds mocks base method.
func (m *MockStaffClient) ListAllStaffIds(arg0 context.Context, arg1 int64, arg2 *int64) ([]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListAllStaffIds", arg0, arg1, arg2)
	ret0, _ := ret[0].([]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAllStaffIds indicates an expected call of ListAllStaffIds.
func (mr *MockStaffClientMockRecorder) ListAllStaffIds(arg0, arg1, arg2 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAllStaffIds", reflect.TypeOf((*MockStaffClient)(nil).ListAllStaffIds), arg0, arg1, arg2)
}
