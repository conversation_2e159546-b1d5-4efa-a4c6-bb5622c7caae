// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: EvaluationPetBreedFilterRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_evaluation_pet_breed_filter_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository EvaluationPetBreedFilterRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockEvaluationPetBreedFilterRepository is a mock of EvaluationPetBreedFilterRepository interface.
type MockEvaluationPetBreedFilterRepository struct {
	ctrl     *gomock.Controller
	recorder *MockEvaluationPetBreedFilterRepositoryMockRecorder
	isgomock struct{}
}

// MockEvaluationPetBreedFilterRepositoryMockRecorder is the mock recorder for MockEvaluationPetBreedFilterRepository.
type MockEvaluationPetBreedFilterRepositoryMockRecorder struct {
	mock *MockEvaluationPetBreedFilterRepository
}

// NewMockEvaluationPetBreedFilterRepository creates a new mock instance.
func NewMockEvaluationPetBreedFilterRepository(ctrl *gomock.Controller) *MockEvaluationPetBreedFilterRepository {
	mock := &MockEvaluationPetBreedFilterRepository{ctrl: ctrl}
	mock.recorder = &MockEvaluationPetBreedFilterRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEvaluationPetBreedFilterRepository) EXPECT() *MockEvaluationPetBreedFilterRepositoryMockRecorder {
	return m.recorder
}

// BatchCreateFilter mocks base method.
func (m *MockEvaluationPetBreedFilterRepository) BatchCreateFilter(arg0 context.Context, arg1 *gorm.DB, arg2 int64, arg3 []do.PetBreedFilter) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateFilter", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreateFilter indicates an expected call of BatchCreateFilter.
func (mr *MockEvaluationPetBreedFilterRepositoryMockRecorder) BatchCreateFilter(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateFilter", reflect.TypeOf((*MockEvaluationPetBreedFilterRepository)(nil).BatchCreateFilter), arg0, arg1, arg2, arg3)
}

// BatchUpdateFilter mocks base method.
func (m *MockEvaluationPetBreedFilterRepository) BatchUpdateFilter(arg0 context.Context, arg1 *gorm.DB, arg2 int64, arg3 []do.PetBreedFilter) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateFilter", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateFilter indicates an expected call of BatchUpdateFilter.
func (mr *MockEvaluationPetBreedFilterRepositoryMockRecorder) BatchUpdateFilter(arg0, arg1, arg2, arg3 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateFilter", reflect.TypeOf((*MockEvaluationPetBreedFilterRepository)(nil).BatchUpdateFilter), arg0, arg1, arg2, arg3)
}

// ListByEvaluationID mocks base method.
func (m *MockEvaluationPetBreedFilterRepository) ListByEvaluationID(arg0 context.Context, arg1 int64) ([]models.EvaluationPetBreedFilter, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByEvaluationID", arg0, arg1)
	ret0, _ := ret[0].([]models.EvaluationPetBreedFilter)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByEvaluationID indicates an expected call of ListByEvaluationID.
func (mr *MockEvaluationPetBreedFilterRepositoryMockRecorder) ListByEvaluationID(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByEvaluationID", reflect.TypeOf((*MockEvaluationPetBreedFilterRepository)(nil).ListByEvaluationID), arg0, arg1)
}

// ListByEvaluationIDs mocks base method.
func (m *MockEvaluationPetBreedFilterRepository) ListByEvaluationIDs(arg0 context.Context, arg1 []int64) ([]models.EvaluationPetBreedFilter, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByEvaluationIDs", arg0, arg1)
	ret0, _ := ret[0].([]models.EvaluationPetBreedFilter)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByEvaluationIDs indicates an expected call of ListByEvaluationIDs.
func (mr *MockEvaluationPetBreedFilterRepositoryMockRecorder) ListByEvaluationIDs(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByEvaluationIDs", reflect.TypeOf((*MockEvaluationPetBreedFilterRepository)(nil).ListByEvaluationIDs), arg0, arg1)
}
