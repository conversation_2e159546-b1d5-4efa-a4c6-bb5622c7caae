// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: ServicePetCodeAvailabilityRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_service_pet_code_availability_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServicePetCodeAvailabilityRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockServicePetCodeAvailabilityRepository is a mock of ServicePetCodeAvailabilityRepository interface.
type MockServicePetCodeAvailabilityRepository struct {
	ctrl     *gomock.Controller
	recorder *MockServicePetCodeAvailabilityRepositoryMockRecorder
	isgomock struct{}
}

// MockServicePetCodeAvailabilityRepositoryMockRecorder is the mock recorder for MockServicePetCodeAvailabilityRepository.
type MockServicePetCodeAvailabilityRepositoryMockRecorder struct {
	mock *MockServicePetCodeAvailabilityRepository
}

// NewMockServicePetCodeAvailabilityRepository creates a new mock instance.
func NewMockServicePetCodeAvailabilityRepository(ctrl *gomock.Controller) *MockServicePetCodeAvailabilityRepository {
	mock := &MockServicePetCodeAvailabilityRepository{ctrl: ctrl}
	mock.recorder = &MockServicePetCodeAvailabilityRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServicePetCodeAvailabilityRepository) EXPECT() *MockServicePetCodeAvailabilityRepositoryMockRecorder {
	return m.recorder
}

// List mocks base method.
func (m *MockServicePetCodeAvailabilityRepository) List(ctx context.Context, opt *models.ServicePetCodeFilterWhereOpt) ([]*models.MoeServicePetCodeFilter, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, opt)
	ret0, _ := ret[0].([]*models.MoeServicePetCodeFilter)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockServicePetCodeAvailabilityRepositoryMockRecorder) List(ctx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockServicePetCodeAvailabilityRepository)(nil).List), ctx, opt)
}

// Upsert mocks base method.
func (m *MockServicePetCodeAvailabilityRepository) Upsert(ctx context.Context, tx *gorm.DB, model *models.MoeServicePetCodeFilter) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, tx, model)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockServicePetCodeAvailabilityRepositoryMockRecorder) Upsert(ctx, tx, model any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockServicePetCodeAvailabilityRepository)(nil).Upsert), ctx, tx, model)
}
