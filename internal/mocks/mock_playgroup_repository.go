// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: PlaygroupRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_playgroup_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository PlaygroupRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	gomock "go.uber.org/mock/gomock"
)

// MockPlaygroupRepository is a mock of PlaygroupRepository interface.
type MockPlaygroupRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPlaygroupRepositoryMockRecorder
	isgomock struct{}
}

// MockPlaygroupRepositoryMockRecorder is the mock recorder for MockPlaygroupRepository.
type MockPlaygroupRepositoryMockRecorder struct {
	mock *MockPlaygroupRepository
}

// NewMockPlaygroupRepository creates a new mock instance.
func NewMockPlaygroupRepository(ctrl *gomock.Controller) *MockPlaygroupRepository {
	mock := &MockPlaygroupRepository{ctrl: ctrl}
	mock.recorder = &MockPlaygroupRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPlaygroupRepository) EXPECT() *MockPlaygroupRepositoryMockRecorder {
	return m.recorder
}

// BatchUpdatePlaygroupSort mocks base method.
func (m *MockPlaygroupRepository) BatchUpdatePlaygroupSort(ctx context.Context, companyID int64, updates map[int64]int32, operatorID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdatePlaygroupSort", ctx, companyID, updates, operatorID)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdatePlaygroupSort indicates an expected call of BatchUpdatePlaygroupSort.
func (mr *MockPlaygroupRepositoryMockRecorder) BatchUpdatePlaygroupSort(ctx, companyID, updates, operatorID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdatePlaygroupSort", reflect.TypeOf((*MockPlaygroupRepository)(nil).BatchUpdatePlaygroupSort), ctx, companyID, updates, operatorID)
}

// CreatePlaygroup mocks base method.
func (m *MockPlaygroupRepository) CreatePlaygroup(ctx context.Context, playgroup *models.Playgroup) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePlaygroup", ctx, playgroup)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePlaygroup indicates an expected call of CreatePlaygroup.
func (mr *MockPlaygroupRepositoryMockRecorder) CreatePlaygroup(ctx, playgroup any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePlaygroup", reflect.TypeOf((*MockPlaygroupRepository)(nil).CreatePlaygroup), ctx, playgroup)
}

// DeletePlaygroup mocks base method.
func (m *MockPlaygroupRepository) DeletePlaygroup(ctx context.Context, whereOpt *models.PlaygroupWhereOpt, deletedBy int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePlaygroup", ctx, whereOpt, deletedBy)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePlaygroup indicates an expected call of DeletePlaygroup.
func (mr *MockPlaygroupRepositoryMockRecorder) DeletePlaygroup(ctx, whereOpt, deletedBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePlaygroup", reflect.TypeOf((*MockPlaygroupRepository)(nil).DeletePlaygroup), ctx, whereOpt, deletedBy)
}

// GetMaxSortPlaygroupByCompanyID mocks base method.
func (m *MockPlaygroupRepository) GetMaxSortPlaygroupByCompanyID(ctx context.Context, companyID int64) (*models.Playgroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxSortPlaygroupByCompanyID", ctx, companyID)
	ret0, _ := ret[0].(*models.Playgroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxSortPlaygroupByCompanyID indicates an expected call of GetMaxSortPlaygroupByCompanyID.
func (mr *MockPlaygroupRepositoryMockRecorder) GetMaxSortPlaygroupByCompanyID(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxSortPlaygroupByCompanyID", reflect.TypeOf((*MockPlaygroupRepository)(nil).GetMaxSortPlaygroupByCompanyID), ctx, companyID)
}

// GetPlaygroupByID mocks base method.
func (m *MockPlaygroupRepository) GetPlaygroupByID(ctx context.Context, where *models.PlaygroupWhereOpt) (*models.Playgroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlaygroupByID", ctx, where)
	ret0, _ := ret[0].(*models.Playgroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlaygroupByID indicates an expected call of GetPlaygroupByID.
func (mr *MockPlaygroupRepositoryMockRecorder) GetPlaygroupByID(ctx, where any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlaygroupByID", reflect.TypeOf((*MockPlaygroupRepository)(nil).GetPlaygroupByID), ctx, where)
}

// GetPlaygroupByName mocks base method.
func (m *MockPlaygroupRepository) GetPlaygroupByName(ctx context.Context, where *models.PlaygroupWhereOpt) (*models.Playgroup, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPlaygroupByName", ctx, where)
	ret0, _ := ret[0].(*models.Playgroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPlaygroupByName indicates an expected call of GetPlaygroupByName.
func (mr *MockPlaygroupRepositoryMockRecorder) GetPlaygroupByName(ctx, where any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPlaygroupByName", reflect.TypeOf((*MockPlaygroupRepository)(nil).GetPlaygroupByName), ctx, where)
}

// ListPlaygroup mocks base method.
func (m *MockPlaygroupRepository) ListPlaygroup(ctx context.Context, where *models.PlaygroupWhereOpt, pagination *utilsV2.PaginationRequest) ([]*models.Playgroup, int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPlaygroup", ctx, where, pagination)
	ret0, _ := ret[0].([]*models.Playgroup)
	ret1, _ := ret[1].(int32)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListPlaygroup indicates an expected call of ListPlaygroup.
func (mr *MockPlaygroupRepositoryMockRecorder) ListPlaygroup(ctx, where, pagination any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPlaygroup", reflect.TypeOf((*MockPlaygroupRepository)(nil).ListPlaygroup), ctx, where, pagination)
}

// UpdatePlaygroup mocks base method.
func (m *MockPlaygroupRepository) UpdatePlaygroup(ctx context.Context, where *models.PlaygroupWhereOpt, updateOpt *models.PlaygroupUpdateOpt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePlaygroup", ctx, where, updateOpt)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePlaygroup indicates an expected call of UpdatePlaygroup.
func (mr *MockPlaygroupRepositoryMockRecorder) UpdatePlaygroup(ctx, where, updateOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePlaygroup", reflect.TypeOf((*MockPlaygroupRepository)(nil).UpdatePlaygroup), ctx, where, updateOpt)
}
