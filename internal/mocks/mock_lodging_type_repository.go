// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: LodgingTypeRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_lodging_type_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository LodgingTypeRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	repository "github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockLodgingTypeRepository is a mock of LodgingTypeRepository interface.
type MockLodgingTypeRepository struct {
	ctrl     *gomock.Controller
	recorder *MockLodgingTypeRepositoryMockRecorder
	isgomock struct{}
}

// MockLodgingTypeRepositoryMockRecorder is the mock recorder for MockLodgingTypeRepository.
type MockLodgingTypeRepositoryMockRecorder struct {
	mock *MockLodgingTypeRepository
}

// NewMockLodgingTypeRepository creates a new mock instance.
func NewMockLodgingTypeRepository(ctrl *gomock.Controller) *MockLodgingTypeRepository {
	mock := &MockLodgingTypeRepository{ctrl: ctrl}
	mock.recorder = &MockLodgingTypeRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLodgingTypeRepository) EXPECT() *MockLodgingTypeRepositoryMockRecorder {
	return m.recorder
}

// Add mocks base method.
func (m *MockLodgingTypeRepository) Add(ctx context.Context, do *do.LodgingTypeDO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Add", ctx, do)
	ret0, _ := ret[0].(error)
	return ret0
}

// Add indicates an expected call of Add.
func (mr *MockLodgingTypeRepositoryMockRecorder) Add(ctx, do any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Add", reflect.TypeOf((*MockLodgingTypeRepository)(nil).Add), ctx, do)
}

// BatchUpdateById mocks base method.
func (m *MockLodgingTypeRepository) BatchUpdateById(ctx context.Context, companyID *int64, updateOpts []*do.LodgingTypeUpdateByIDOpt) ([]*do.LodgingTypeDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateById", ctx, companyID, updateOpts)
	ret0, _ := ret[0].([]*do.LodgingTypeDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateById indicates an expected call of BatchUpdateById.
func (mr *MockLodgingTypeRepositoryMockRecorder) BatchUpdateById(ctx, companyID, updateOpts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateById", reflect.TypeOf((*MockLodgingTypeRepository)(nil).BatchUpdateById), ctx, companyID, updateOpts)
}

// Delete mocks base method.
func (m *MockLodgingTypeRepository) Delete(ctx context.Context, id, deletedBy int64, companyID *int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id, deletedBy, companyID)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockLodgingTypeRepositoryMockRecorder) Delete(ctx, id, deletedBy, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockLodgingTypeRepository)(nil).Delete), ctx, id, deletedBy, companyID)
}

// GetMaxSort mocks base method.
func (m *MockLodgingTypeRepository) GetMaxSort(ctx context.Context, companyId int64) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxSort", ctx, companyId)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxSort indicates an expected call of GetMaxSort.
func (mr *MockLodgingTypeRepositoryMockRecorder) GetMaxSort(ctx, companyId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxSort", reflect.TypeOf((*MockLodgingTypeRepository)(nil).GetMaxSort), ctx, companyId)
}

// List mocks base method.
func (m *MockLodgingTypeRepository) List(ctx context.Context, companyID int64) ([]*do.LodgingTypeDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, companyID)
	ret0, _ := ret[0].([]*do.LodgingTypeDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockLodgingTypeRepositoryMockRecorder) List(ctx, companyID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockLodgingTypeRepository)(nil).List), ctx, companyID)
}

// MGet mocks base method.
func (m *MockLodgingTypeRepository) MGet(ctx context.Context, idList []int64) ([]*do.LodgingTypeDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGet", ctx, idList)
	ret0, _ := ret[0].([]*do.LodgingTypeDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGet indicates an expected call of MGet.
func (mr *MockLodgingTypeRepositoryMockRecorder) MGet(ctx, idList any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGet", reflect.TypeOf((*MockLodgingTypeRepository)(nil).MGet), ctx, idList)
}

// Update mocks base method.
func (m *MockLodgingTypeRepository) Update(ctx context.Context, id int64, companyID *int64, updateOpt *do.LodgingTypeUpdateOpt) (*do.LodgingTypeDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, id, companyID, updateOpt)
	ret0, _ := ret[0].(*do.LodgingTypeDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockLodgingTypeRepositoryMockRecorder) Update(ctx, id, companyID, updateOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLodgingTypeRepository)(nil).Update), ctx, id, companyID, updateOpt)
}

// WithTx mocks base method.
func (m *MockLodgingTypeRepository) WithTx(tx *gorm.DB) repository.LodgingTypeRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(repository.LodgingTypeRepository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockLodgingTypeRepositoryMockRecorder) WithTx(tx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockLodgingTypeRepository)(nil).WithTx), tx)
}
