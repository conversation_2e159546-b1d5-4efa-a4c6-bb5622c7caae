// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: ServiceAvailabilityRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_service_availability_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceAvailabilityRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockServiceAvailabilityRepository is a mock of ServiceAvailabilityRepository interface.
type MockServiceAvailabilityRepository struct {
	ctrl     *gomock.Controller
	recorder *MockServiceAvailabilityRepositoryMockRecorder
	isgomock struct{}
}

// MockServiceAvailabilityRepositoryMockRecorder is the mock recorder for MockServiceAvailabilityRepository.
type MockServiceAvailabilityRepositoryMockRecorder struct {
	mock *MockServiceAvailabilityRepository
}

// NewMockServiceAvailabilityRepository creates a new mock instance.
func NewMockServiceAvailabilityRepository(ctrl *gomock.Controller) *MockServiceAvailabilityRepository {
	mock := &MockServiceAvailabilityRepository{ctrl: ctrl}
	mock.recorder = &MockServiceAvailabilityRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceAvailabilityRepository) EXPECT() *MockServiceAvailabilityRepositoryMockRecorder {
	return m.recorder
}

// RemoveLodgingFilter mocks base method.
func (m *MockServiceAvailabilityRepository) RemoveLodgingFilter(ctx context.Context, companyId, lodgingTypeId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveLodgingFilter", ctx, companyId, lodgingTypeId)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveLodgingFilter indicates an expected call of RemoveLodgingFilter.
func (mr *MockServiceAvailabilityRepositoryMockRecorder) RemoveLodgingFilter(ctx, companyId, lodgingTypeId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveLodgingFilter", reflect.TypeOf((*MockServiceAvailabilityRepository)(nil).RemoveLodgingFilter), ctx, companyId, lodgingTypeId)
}

// RemovePetBreedFilter mocks base method.
func (m *MockServiceAvailabilityRepository) RemovePetBreedFilter(ctx context.Context, companyId int64, petBreed string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemovePetBreedFilter", ctx, companyId, petBreed)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemovePetBreedFilter indicates an expected call of RemovePetBreedFilter.
func (mr *MockServiceAvailabilityRepositoryMockRecorder) RemovePetBreedFilter(ctx, companyId, petBreed any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemovePetBreedFilter", reflect.TypeOf((*MockServiceAvailabilityRepository)(nil).RemovePetBreedFilter), ctx, companyId, petBreed)
}

// RemovePetCoatTypeFilter mocks base method.
func (m *MockServiceAvailabilityRepository) RemovePetCoatTypeFilter(ctx context.Context, companyId, petCoatTypeId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemovePetCoatTypeFilter", ctx, companyId, petCoatTypeId)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemovePetCoatTypeFilter indicates an expected call of RemovePetCoatTypeFilter.
func (mr *MockServiceAvailabilityRepositoryMockRecorder) RemovePetCoatTypeFilter(ctx, companyId, petCoatTypeId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemovePetCoatTypeFilter", reflect.TypeOf((*MockServiceAvailabilityRepository)(nil).RemovePetCoatTypeFilter), ctx, companyId, petCoatTypeId)
}

// RemovePetSizeFilter mocks base method.
func (m *MockServiceAvailabilityRepository) RemovePetSizeFilter(ctx context.Context, companyId, petSizeId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemovePetSizeFilter", ctx, companyId, petSizeId)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemovePetSizeFilter indicates an expected call of RemovePetSizeFilter.
func (mr *MockServiceAvailabilityRepositoryMockRecorder) RemovePetSizeFilter(ctx, companyId, petSizeId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemovePetSizeFilter", reflect.TypeOf((*MockServiceAvailabilityRepository)(nil).RemovePetSizeFilter), ctx, companyId, petSizeId)
}

// RemovePetTypeFilter mocks base method.
func (m *MockServiceAvailabilityRepository) RemovePetTypeFilter(ctx context.Context, companyId int64, petType customerpb.PetType) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemovePetTypeFilter", ctx, companyId, petType)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemovePetTypeFilter indicates an expected call of RemovePetTypeFilter.
func (mr *MockServiceAvailabilityRepositoryMockRecorder) RemovePetTypeFilter(ctx, companyId, petType any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemovePetTypeFilter", reflect.TypeOf((*MockServiceAvailabilityRepository)(nil).RemovePetTypeFilter), ctx, companyId, petType)
}

// RemoveServiceFilter mocks base method.
func (m *MockServiceAvailabilityRepository) RemoveServiceFilter(ctx context.Context, companyId, serviceId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveServiceFilter", ctx, companyId, serviceId)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveServiceFilter indicates an expected call of RemoveServiceFilter.
func (mr *MockServiceAvailabilityRepositoryMockRecorder) RemoveServiceFilter(ctx, companyId, serviceId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveServiceFilter", reflect.TypeOf((*MockServiceAvailabilityRepository)(nil).RemoveServiceFilter), ctx, companyId, serviceId)
}
