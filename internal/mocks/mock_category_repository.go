// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: CategoryRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_category_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository CategoryRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	gomock "go.uber.org/mock/gomock"
)

// MockCategoryRepository is a mock of CategoryRepository interface.
type MockCategoryRepository struct {
	ctrl     *gomock.Controller
	recorder *MockCategoryRepositoryMockRecorder
	isgomock struct{}
}

// MockCategoryRepositoryMockRecorder is the mock recorder for MockCategoryRepository.
type MockCategoryRepositoryMockRecorder struct {
	mock *MockCategoryRepository
}

// NewMockCategoryRepository creates a new mock instance.
func NewMockCategoryRepository(ctrl *gomock.Controller) *MockCategoryRepository {
	mock := &MockCategoryRepository{ctrl: ctrl}
	mock.recorder = &MockCategoryRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCategoryRepository) EXPECT() *MockCategoryRepositoryMockRecorder {
	return m.recorder
}

// CreateCategories mocks base method.
func (m *MockCategoryRepository) CreateCategories(ctx context.Context, categories []*do.Category) ([]*do.Category, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateCategories", ctx, categories)
	ret0, _ := ret[0].([]*do.Category)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCategories indicates an expected call of CreateCategories.
func (mr *MockCategoryRepositoryMockRecorder) CreateCategories(ctx, categories any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCategories", reflect.TypeOf((*MockCategoryRepository)(nil).CreateCategories), ctx, categories)
}

// GetCategory mocks base method.
func (m *MockCategoryRepository) GetCategory(ctx context.Context, id int64) (*models.MoeGroomingServiceCategory, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategory", ctx, id)
	ret0, _ := ret[0].(*models.MoeGroomingServiceCategory)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategory indicates an expected call of GetCategory.
func (mr *MockCategoryRepositoryMockRecorder) GetCategory(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategory", reflect.TypeOf((*MockCategoryRepository)(nil).GetCategory), ctx, id)
}

// GetCategoryByID mocks base method.
func (m *MockCategoryRepository) GetCategoryByID(ctx context.Context, categoryID int64) (*do.Category, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryByID", ctx, categoryID)
	ret0, _ := ret[0].(*do.Category)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryByID indicates an expected call of GetCategoryByID.
func (mr *MockCategoryRepositoryMockRecorder) GetCategoryByID(ctx, categoryID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryByID", reflect.TypeOf((*MockCategoryRepository)(nil).GetCategoryByID), ctx, categoryID)
}

// GetCategoryWithFilter mocks base method.
func (m *MockCategoryRepository) GetCategoryWithFilter(ctx context.Context, companyId int64, filter do.CategoryQueryFilter) ([]*do.Category, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCategoryWithFilter", ctx, companyId, filter)
	ret0, _ := ret[0].([]*do.Category)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCategoryWithFilter indicates an expected call of GetCategoryWithFilter.
func (mr *MockCategoryRepositoryMockRecorder) GetCategoryWithFilter(ctx, companyId, filter any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCategoryWithFilter", reflect.TypeOf((*MockCategoryRepository)(nil).GetCategoryWithFilter), ctx, companyId, filter)
}
