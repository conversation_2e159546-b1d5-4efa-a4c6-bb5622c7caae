// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: ServiceBusinessOverrideRuleRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_service_business_override_rule_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceBusinessOverrideRuleRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	gomock "go.uber.org/mock/gomock"
)

// MockServiceBusinessOverrideRuleRepository is a mock of ServiceBusinessOverrideRuleRepository interface.
type MockServiceBusinessOverrideRuleRepository struct {
	ctrl     *gomock.Controller
	recorder *MockServiceBusinessOverrideRuleRepositoryMockRecorder
	isgomock struct{}
}

// MockServiceBusinessOverrideRuleRepositoryMockRecorder is the mock recorder for MockServiceBusinessOverrideRuleRepository.
type MockServiceBusinessOverrideRuleRepositoryMockRecorder struct {
	mock *MockServiceBusinessOverrideRuleRepository
}

// NewMockServiceBusinessOverrideRuleRepository creates a new mock instance.
func NewMockServiceBusinessOverrideRuleRepository(ctrl *gomock.Controller) *MockServiceBusinessOverrideRuleRepository {
	mock := &MockServiceBusinessOverrideRuleRepository{ctrl: ctrl}
	mock.recorder = &MockServiceBusinessOverrideRuleRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceBusinessOverrideRuleRepository) EXPECT() *MockServiceBusinessOverrideRuleRepositoryMockRecorder {
	return m.recorder
}

// ListServiceBusinessOverrideRules mocks base method.
func (m *MockServiceBusinessOverrideRuleRepository) ListServiceBusinessOverrideRules(ctx context.Context, companyId int64, overrideConditions map[int64][]do.BusinessOverrideCondition) (map[int64][]do.BusinessOverrideRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListServiceBusinessOverrideRules", ctx, companyId, overrideConditions)
	ret0, _ := ret[0].(map[int64][]do.BusinessOverrideRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListServiceBusinessOverrideRules indicates an expected call of ListServiceBusinessOverrideRules.
func (mr *MockServiceBusinessOverrideRuleRepositoryMockRecorder) ListServiceBusinessOverrideRules(ctx, companyId, overrideConditions any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListServiceBusinessOverrideRules", reflect.TypeOf((*MockServiceBusinessOverrideRuleRepository)(nil).ListServiceBusinessOverrideRules), ctx, companyId, overrideConditions)
}
