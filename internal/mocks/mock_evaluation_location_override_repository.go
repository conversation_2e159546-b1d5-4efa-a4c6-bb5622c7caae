// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: EvaluationLocationOverrideRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination internal/mocks/mock_evaluation_location_override_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository EvaluationLocationOverrideRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	gomock "go.uber.org/mock/gomock"
)

// MockEvaluationLocationOverrideRepository is a mock of EvaluationLocationOverrideRepository interface.
type MockEvaluationLocationOverrideRepository struct {
	ctrl     *gomock.Controller
	recorder *MockEvaluationLocationOverrideRepositoryMockRecorder
	isgomock struct{}
}

// MockEvaluationLocationOverrideRepositoryMockRecorder is the mock recorder for MockEvaluationLocationOverrideRepository.
type MockEvaluationLocationOverrideRepositoryMockRecorder struct {
	mock *MockEvaluationLocationOverrideRepository
}

// NewMockEvaluationLocationOverrideRepository creates a new mock instance.
func NewMockEvaluationLocationOverrideRepository(ctrl *gomock.Controller) *MockEvaluationLocationOverrideRepository {
	mock := &MockEvaluationLocationOverrideRepository{ctrl: ctrl}
	mock.recorder = &MockEvaluationLocationOverrideRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEvaluationLocationOverrideRepository) EXPECT() *MockEvaluationLocationOverrideRepositoryMockRecorder {
	return m.recorder
}

// ListByEvaluationIDs mocks base method.
func (m *MockEvaluationLocationOverrideRepository) ListByEvaluationIDs(ctx context.Context, evaluationIDs []int64) ([]*models.EvaluationLocationOverride, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByEvaluationIDs", ctx, evaluationIDs)
	ret0, _ := ret[0].([]*models.EvaluationLocationOverride)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByEvaluationIDs indicates an expected call of ListByEvaluationIDs.
func (mr *MockEvaluationLocationOverrideRepositoryMockRecorder) ListByEvaluationIDs(ctx, evaluationIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByEvaluationIDs", reflect.TypeOf((*MockEvaluationLocationOverrideRepository)(nil).ListByEvaluationIDs), ctx, evaluationIDs)
}
