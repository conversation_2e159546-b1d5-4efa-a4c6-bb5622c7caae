// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: PricingRuleRecordRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_pricing_rule_record_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository PricingRuleRecordRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	gomock "go.uber.org/mock/gomock"
)

// MockPricingRuleRecordRepository is a mock of PricingRuleRecordRepository interface.
type MockPricingRuleRecordRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPricingRuleRecordRepositoryMockRecorder
	isgomock struct{}
}

// MockPricingRuleRecordRepositoryMockRecorder is the mock recorder for MockPricingRuleRecordRepository.
type MockPricingRuleRecordRepositoryMockRecorder struct {
	mock *MockPricingRuleRecordRepository
}

// NewMockPricingRuleRecordRepository creates a new mock instance.
func NewMockPricingRuleRecordRepository(ctrl *gomock.Controller) *MockPricingRuleRecordRepository {
	mock := &MockPricingRuleRecordRepository{ctrl: ctrl}
	mock.recorder = &MockPricingRuleRecordRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPricingRuleRecordRepository) EXPECT() *MockPricingRuleRecordRepositoryMockRecorder {
	return m.recorder
}

// Count mocks base method.
func (m *MockPricingRuleRecordRepository) Count(ctx context.Context, whereOpt *models.PricingRuleRecordWhereOpt) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Count", ctx, whereOpt)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockPricingRuleRecordRepositoryMockRecorder) Count(ctx, whereOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockPricingRuleRecordRepository)(nil).Count), ctx, whereOpt)
}

// Delete mocks base method.
func (m *MockPricingRuleRecordRepository) Delete(ctx context.Context, companyId, pricingRuleRecordId, deleteBy int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, companyId, pricingRuleRecordId, deleteBy)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockPricingRuleRecordRepositoryMockRecorder) Delete(ctx, companyId, pricingRuleRecordId, deleteBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockPricingRuleRecordRepository)(nil).Delete), ctx, companyId, pricingRuleRecordId, deleteBy)
}

// GetById mocks base method.
func (m *MockPricingRuleRecordRepository) GetById(ctx context.Context, companyId, pricingRuleId int64) (*do.PricingRuleRecordDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetById", ctx, companyId, pricingRuleId)
	ret0, _ := ret[0].(*do.PricingRuleRecordDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockPricingRuleRecordRepositoryMockRecorder) GetById(ctx, companyId, pricingRuleId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockPricingRuleRecordRepository)(nil).GetById), ctx, companyId, pricingRuleId)
}

// List mocks base method.
func (m *MockPricingRuleRecordRepository) List(ctx context.Context, whereOpt *models.PricingRuleRecordWhereOpt, pagination *utilsV2.PaginationRequest) ([]*do.PricingRuleRecordDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, whereOpt, pagination)
	ret0, _ := ret[0].([]*do.PricingRuleRecordDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockPricingRuleRecordRepositoryMockRecorder) List(ctx, whereOpt, pagination any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockPricingRuleRecordRepository)(nil).List), ctx, whereOpt, pagination)
}

// Upsert mocks base method.
func (m *MockPricingRuleRecordRepository) Upsert(ctx context.Context, pricingRuleRecordDo *do.PricingRuleRecordDO) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, pricingRuleRecordDo)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upsert indicates an expected call of Upsert.
func (mr *MockPricingRuleRecordRepositoryMockRecorder) Upsert(ctx, pricingRuleRecordDo any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockPricingRuleRecordRepository)(nil).Upsert), ctx, pricingRuleRecordDo)
}
