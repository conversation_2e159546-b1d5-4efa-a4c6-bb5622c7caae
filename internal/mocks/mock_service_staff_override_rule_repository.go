// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: ServiceStaffOverrideRuleRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_service_staff_override_rule_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceStaffOverrideRuleRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	gomock "go.uber.org/mock/gomock"
)

// MockServiceStaffOverrideRuleRepository is a mock of ServiceStaffOverrideRuleRepository interface.
type MockServiceStaffOverrideRuleRepository struct {
	ctrl     *gomock.Controller
	recorder *MockServiceStaffOverrideRuleRepositoryMockRecorder
	isgomock struct{}
}

// MockServiceStaffOverrideRuleRepositoryMockRecorder is the mock recorder for MockServiceStaffOverrideRuleRepository.
type MockServiceStaffOverrideRuleRepositoryMockRecorder struct {
	mock *MockServiceStaffOverrideRuleRepository
}

// NewMockServiceStaffOverrideRuleRepository creates a new mock instance.
func NewMockServiceStaffOverrideRuleRepository(ctrl *gomock.Controller) *MockServiceStaffOverrideRuleRepository {
	mock := &MockServiceStaffOverrideRuleRepository{ctrl: ctrl}
	mock.recorder = &MockServiceStaffOverrideRuleRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceStaffOverrideRuleRepository) EXPECT() *MockServiceStaffOverrideRuleRepositoryMockRecorder {
	return m.recorder
}

// BatchCreateStaffOverrideRule mocks base method.
func (m *MockServiceStaffOverrideRuleRepository) BatchCreateStaffOverrideRule(ctx context.Context, companyId, serviceId int64, staffOverrideRuleList []do.StaffOverrideRule) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateStaffOverrideRule", ctx, companyId, serviceId, staffOverrideRuleList)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchCreateStaffOverrideRule indicates an expected call of BatchCreateStaffOverrideRule.
func (mr *MockServiceStaffOverrideRuleRepositoryMockRecorder) BatchCreateStaffOverrideRule(ctx, companyId, serviceId, staffOverrideRuleList any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateStaffOverrideRule", reflect.TypeOf((*MockServiceStaffOverrideRuleRepository)(nil).BatchCreateStaffOverrideRule), ctx, companyId, serviceId, staffOverrideRuleList)
}

// GetStaffOverrideRules mocks base method.
func (m *MockServiceStaffOverrideRuleRepository) GetStaffOverrideRules(ctx context.Context, companyId int64, businessId *int64, serviceIds []int64) (map[int64][]do.StaffOverrideRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStaffOverrideRules", ctx, companyId, businessId, serviceIds)
	ret0, _ := ret[0].(map[int64][]do.StaffOverrideRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffOverrideRules indicates an expected call of GetStaffOverrideRules.
func (mr *MockServiceStaffOverrideRuleRepositoryMockRecorder) GetStaffOverrideRules(ctx, companyId, businessId, serviceIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffOverrideRules", reflect.TypeOf((*MockServiceStaffOverrideRuleRepository)(nil).GetStaffOverrideRules), ctx, companyId, businessId, serviceIds)
}

// GetStaffOverrideRulesByServiceId mocks base method.
func (m *MockServiceStaffOverrideRuleRepository) GetStaffOverrideRulesByServiceId(ctx context.Context, serviceId int64) ([]do.StaffOverrideRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStaffOverrideRulesByServiceId", ctx, serviceId)
	ret0, _ := ret[0].([]do.StaffOverrideRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStaffOverrideRulesByServiceId indicates an expected call of GetStaffOverrideRulesByServiceId.
func (mr *MockServiceStaffOverrideRuleRepositoryMockRecorder) GetStaffOverrideRulesByServiceId(ctx, serviceId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStaffOverrideRulesByServiceId", reflect.TypeOf((*MockServiceStaffOverrideRuleRepository)(nil).GetStaffOverrideRulesByServiceId), ctx, serviceId)
}

// ListStaffOverrideRules mocks base method.
func (m *MockServiceStaffOverrideRuleRepository) ListStaffOverrideRules(ctx context.Context, companyId int64, overrideConditions map[int64][]do.StaffOverrideCondition) (map[int64][]do.StaffOverrideRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListStaffOverrideRules", ctx, companyId, overrideConditions)
	ret0, _ := ret[0].(map[int64][]do.StaffOverrideRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListStaffOverrideRules indicates an expected call of ListStaffOverrideRules.
func (mr *MockServiceStaffOverrideRuleRepositoryMockRecorder) ListStaffOverrideRules(ctx, companyId, overrideConditions any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListStaffOverrideRules", reflect.TypeOf((*MockServiceStaffOverrideRuleRepository)(nil).ListStaffOverrideRules), ctx, companyId, overrideConditions)
}

// RemoveServiceStaffOverrideRules mocks base method.
func (m *MockServiceStaffOverrideRuleRepository) RemoveServiceStaffOverrideRules(ctx context.Context, companyId, serviceId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveServiceStaffOverrideRules", ctx, companyId, serviceId)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveServiceStaffOverrideRules indicates an expected call of RemoveServiceStaffOverrideRules.
func (mr *MockServiceStaffOverrideRuleRepositoryMockRecorder) RemoveServiceStaffOverrideRules(ctx, companyId, serviceId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveServiceStaffOverrideRules", reflect.TypeOf((*MockServiceStaffOverrideRuleRepository)(nil).RemoveServiceStaffOverrideRules), ctx, companyId, serviceId)
}

// RemoveServiceStaffOverrideRulesByStaffIds mocks base method.
func (m *MockServiceStaffOverrideRuleRepository) RemoveServiceStaffOverrideRulesByStaffIds(ctx context.Context, companyId int64, staffIds []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveServiceStaffOverrideRulesByStaffIds", ctx, companyId, staffIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveServiceStaffOverrideRulesByStaffIds indicates an expected call of RemoveServiceStaffOverrideRulesByStaffIds.
func (mr *MockServiceStaffOverrideRuleRepositoryMockRecorder) RemoveServiceStaffOverrideRulesByStaffIds(ctx, companyId, staffIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveServiceStaffOverrideRulesByStaffIds", reflect.TypeOf((*MockServiceStaffOverrideRuleRepository)(nil).RemoveServiceStaffOverrideRulesByStaffIds), ctx, companyId, staffIds)
}

// UpdateServiceStaffOverrideRules mocks base method.
func (m *MockServiceStaffOverrideRuleRepository) UpdateServiceStaffOverrideRules(ctx context.Context, companyId, serviceId int64, staffOverrideRuleList []do.StaffOverrideRule) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateServiceStaffOverrideRules", ctx, companyId, serviceId, staffOverrideRuleList)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateServiceStaffOverrideRules indicates an expected call of UpdateServiceStaffOverrideRules.
func (mr *MockServiceStaffOverrideRuleRepositoryMockRecorder) UpdateServiceStaffOverrideRules(ctx, companyId, serviceId, staffOverrideRuleList any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateServiceStaffOverrideRules", reflect.TypeOf((*MockServiceStaffOverrideRuleRepository)(nil).UpdateServiceStaffOverrideRules), ctx, companyId, serviceId, staffOverrideRuleList)
}
