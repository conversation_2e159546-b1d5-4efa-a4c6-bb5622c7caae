// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: DiscountSettingRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_discount_setting_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository DiscountSettingRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	gomock "go.uber.org/mock/gomock"
)

// MockDiscountSettingRepository is a mock of DiscountSettingRepository interface.
type MockDiscountSettingRepository struct {
	ctrl     *gomock.Controller
	recorder *MockDiscountSettingRepositoryMockRecorder
	isgomock struct{}
}

// MockDiscountSettingRepositoryMockRecorder is the mock recorder for MockDiscountSettingRepository.
type MockDiscountSettingRepositoryMockRecorder struct {
	mock *MockDiscountSettingRepository
}

// NewMockDiscountSettingRepository creates a new mock instance.
func NewMockDiscountSettingRepository(ctrl *gomock.Controller) *MockDiscountSettingRepository {
	mock := &MockDiscountSettingRepository{ctrl: ctrl}
	mock.recorder = &MockDiscountSettingRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDiscountSettingRepository) EXPECT() *MockDiscountSettingRepositoryMockRecorder {
	return m.recorder
}

// GetByCompanyId mocks base method.
func (m *MockDiscountSettingRepository) GetByCompanyId(ctx context.Context, companyId int64) (*do.DiscountSettingDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCompanyId", ctx, companyId)
	ret0, _ := ret[0].(*do.DiscountSettingDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCompanyId indicates an expected call of GetByCompanyId.
func (mr *MockDiscountSettingRepositoryMockRecorder) GetByCompanyId(ctx, companyId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCompanyId", reflect.TypeOf((*MockDiscountSettingRepository)(nil).GetByCompanyId), ctx, companyId)
}

// Upsert mocks base method.
func (m *MockDiscountSettingRepository) Upsert(ctx context.Context, discountSettingDO *do.DiscountSettingDO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, discountSettingDO)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upsert indicates an expected call of Upsert.
func (mr *MockDiscountSettingRepositoryMockRecorder) Upsert(ctx, discountSettingDO any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockDiscountSettingRepository)(nil).Upsert), ctx, discountSettingDO)
}
