// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: VaccineRequirementRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_vaccine_requirement_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository VaccineRequirementRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockVaccineRequirementRepository is a mock of VaccineRequirementRepository interface.
type MockVaccineRequirementRepository struct {
	ctrl     *gomock.Controller
	recorder *MockVaccineRequirementRepositoryMockRecorder
	isgomock struct{}
}

// MockVaccineRequirementRepositoryMockRecorder is the mock recorder for MockVaccineRequirementRepository.
type MockVaccineRequirementRepositoryMockRecorder struct {
	mock *MockVaccineRequirementRepository
}

// NewMockVaccineRequirementRepository creates a new mock instance.
func NewMockVaccineRequirementRepository(ctrl *gomock.Controller) *MockVaccineRequirementRepository {
	mock := &MockVaccineRequirementRepository{ctrl: ctrl}
	mock.recorder = &MockVaccineRequirementRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVaccineRequirementRepository) EXPECT() *MockVaccineRequirementRepositoryMockRecorder {
	return m.recorder
}

// BatchCreateVaccineRequirement mocks base method.
func (m *MockVaccineRequirementRepository) BatchCreateVaccineRequirement(ctx context.Context, tx *gorm.DB, records []*models.VaccineRequirement) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreateVaccineRequirement", ctx, tx, records)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchCreateVaccineRequirement indicates an expected call of BatchCreateVaccineRequirement.
func (mr *MockVaccineRequirementRepositoryMockRecorder) BatchCreateVaccineRequirement(ctx, tx, records any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreateVaccineRequirement", reflect.TypeOf((*MockVaccineRequirementRepository)(nil).BatchCreateVaccineRequirement), ctx, tx, records)
}

// ListVaccineRequirements mocks base method.
func (m *MockVaccineRequirementRepository) ListVaccineRequirements(ctx context.Context, tx *gorm.DB, opt *models.VaccineRequirementWhereOpt, paginationRequest *utilsV2.PaginationRequest) (int64, []*models.VaccineRequirement, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListVaccineRequirements", ctx, tx, opt, paginationRequest)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].([]*models.VaccineRequirement)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListVaccineRequirements indicates an expected call of ListVaccineRequirements.
func (mr *MockVaccineRequirementRepositoryMockRecorder) ListVaccineRequirements(ctx, tx, opt, paginationRequest any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListVaccineRequirements", reflect.TypeOf((*MockVaccineRequirementRepository)(nil).ListVaccineRequirements), ctx, tx, opt, paginationRequest)
}

// RemoveVaccineRequirement mocks base method.
func (m *MockVaccineRequirementRepository) RemoveVaccineRequirement(ctx context.Context, tx *gorm.DB, opt *models.VaccineRequirementDeleteOpt) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveVaccineRequirement", ctx, tx, opt)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveVaccineRequirement indicates an expected call of RemoveVaccineRequirement.
func (mr *MockVaccineRequirementRepositoryMockRecorder) RemoveVaccineRequirement(ctx, tx, opt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveVaccineRequirement", reflect.TypeOf((*MockVaccineRequirementRepository)(nil).RemoveVaccineRequirement), ctx, tx, opt)
}
