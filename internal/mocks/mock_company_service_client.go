// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1 (interfaces: CompanyServiceClient)
//
// Generated by this command:
//
//	mockgen -package mocks -destination internal/mocks/mock_company_service_client.go github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1 CompanyServiceClient
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockCompanyServiceClient is a mock of CompanyServiceClient interface.
type MockCompanyServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockCompanyServiceClientMockRecorder
	isgomock struct{}
}

// MockCompanyServiceClientMockRecorder is the mock recorder for MockCompanyServiceClient.
type MockCompanyServiceClientMockRecorder struct {
	mock *MockCompanyServiceClient
}

// NewMockCompanyServiceClient creates a new mock instance.
func NewMockCompanyServiceClient(ctrl *gomock.Controller) *MockCompanyServiceClient {
	mock := &MockCompanyServiceClient{ctrl: ctrl}
	mock.recorder = &MockCompanyServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCompanyServiceClient) EXPECT() *MockCompanyServiceClientMockRecorder {
	return m.recorder
}

// AddTaxRule mocks base method.
func (m *MockCompanyServiceClient) AddTaxRule(ctx context.Context, in *organizationsvcpb.AddTaxRuleRequest, opts ...grpc.CallOption) (*organizationsvcpb.AddTaxRuleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AddTaxRule", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.AddTaxRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AddTaxRule indicates an expected call of AddTaxRule.
func (mr *MockCompanyServiceClientMockRecorder) AddTaxRule(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddTaxRule", reflect.TypeOf((*MockCompanyServiceClient)(nil).AddTaxRule), varargs...)
}

// CompanyQuestionRecordSave mocks base method.
func (m *MockCompanyServiceClient) CompanyQuestionRecordSave(ctx context.Context, in *organizationsvcpb.CompanyQuestionRecordRequest, opts ...grpc.CallOption) (*organizationsvcpb.CompanyQuestionRecordResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CompanyQuestionRecordSave", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.CompanyQuestionRecordResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CompanyQuestionRecordSave indicates an expected call of CompanyQuestionRecordSave.
func (mr *MockCompanyServiceClientMockRecorder) CompanyQuestionRecordSave(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CompanyQuestionRecordSave", reflect.TypeOf((*MockCompanyServiceClient)(nil).CompanyQuestionRecordSave), varargs...)
}

// CreateCompany mocks base method.
func (m *MockCompanyServiceClient) CreateCompany(ctx context.Context, in *organizationsvcpb.CreateCompanyRequest, opts ...grpc.CallOption) (*organizationsvcpb.CreateCompanyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateCompany", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.CreateCompanyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCompany indicates an expected call of CreateCompany.
func (mr *MockCompanyServiceClientMockRecorder) CreateCompany(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCompany", reflect.TypeOf((*MockCompanyServiceClient)(nil).CreateCompany), varargs...)
}

// CreateCompanyFromEnterpriseHub mocks base method.
func (m *MockCompanyServiceClient) CreateCompanyFromEnterpriseHub(ctx context.Context, in *organizationsvcpb.CreateCompanyFromEnterpriseHubRequest, opts ...grpc.CallOption) (*organizationsvcpb.CreateCompanyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateCompanyFromEnterpriseHub", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.CreateCompanyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCompanyFromEnterpriseHub indicates an expected call of CreateCompanyFromEnterpriseHub.
func (mr *MockCompanyServiceClientMockRecorder) CreateCompanyFromEnterpriseHub(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCompanyFromEnterpriseHub", reflect.TypeOf((*MockCompanyServiceClient)(nil).CreateCompanyFromEnterpriseHub), varargs...)
}

// DeleteTaxRule mocks base method.
func (m *MockCompanyServiceClient) DeleteTaxRule(ctx context.Context, in *organizationsvcpb.DeleteTaxRuleRequest, opts ...grpc.CallOption) (*organizationsvcpb.DeleteTaxRuleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTaxRule", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.DeleteTaxRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTaxRule indicates an expected call of DeleteTaxRule.
func (mr *MockCompanyServiceClientMockRecorder) DeleteTaxRule(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTaxRule", reflect.TypeOf((*MockCompanyServiceClient)(nil).DeleteTaxRule), varargs...)
}

// GetBusinessIdForMobile mocks base method.
func (m *MockCompanyServiceClient) GetBusinessIdForMobile(ctx context.Context, in *organizationsvcpb.GetBusinessIdForMobileRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetBusinessIdForMobileResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBusinessIdForMobile", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetBusinessIdForMobileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBusinessIdForMobile indicates an expected call of GetBusinessIdForMobile.
func (mr *MockCompanyServiceClientMockRecorder) GetBusinessIdForMobile(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBusinessIdForMobile", reflect.TypeOf((*MockCompanyServiceClient)(nil).GetBusinessIdForMobile), varargs...)
}

// GetClockInOutSetting mocks base method.
func (m *MockCompanyServiceClient) GetClockInOutSetting(ctx context.Context, in *organizationsvcpb.GetClockInOutSettingRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetClockInOutSettingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClockInOutSetting", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetClockInOutSettingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClockInOutSetting indicates an expected call of GetClockInOutSetting.
func (mr *MockCompanyServiceClientMockRecorder) GetClockInOutSetting(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClockInOutSetting", reflect.TypeOf((*MockCompanyServiceClient)(nil).GetClockInOutSetting), varargs...)
}

// GetCompanyPreferenceSetting mocks base method.
func (m *MockCompanyServiceClient) GetCompanyPreferenceSetting(ctx context.Context, in *organizationsvcpb.GetCompanyPreferenceSettingRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetCompanyPreferenceSettingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCompanyPreferenceSetting", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetCompanyPreferenceSettingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCompanyPreferenceSetting indicates an expected call of GetCompanyPreferenceSetting.
func (mr *MockCompanyServiceClientMockRecorder) GetCompanyPreferenceSetting(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCompanyPreferenceSetting", reflect.TypeOf((*MockCompanyServiceClient)(nil).GetCompanyPreferenceSetting), varargs...)
}

// GetCompanyQuestionRecord mocks base method.
func (m *MockCompanyServiceClient) GetCompanyQuestionRecord(ctx context.Context, in *organizationsvcpb.CompanyQuestionRecordQueryRequest, opts ...grpc.CallOption) (*organizationsvcpb.CompanyQuestionRecordQueryResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCompanyQuestionRecord", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.CompanyQuestionRecordQueryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCompanyQuestionRecord indicates an expected call of GetCompanyQuestionRecord.
func (mr *MockCompanyServiceClientMockRecorder) GetCompanyQuestionRecord(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCompanyQuestionRecord", reflect.TypeOf((*MockCompanyServiceClient)(nil).GetCompanyQuestionRecord), varargs...)
}

// GetTaxRuleList mocks base method.
func (m *MockCompanyServiceClient) GetTaxRuleList(ctx context.Context, in *organizationsvcpb.GetTaxRuleListRequest, opts ...grpc.CallOption) (*organizationsvcpb.GetTaxRuleListResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTaxRuleList", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.GetTaxRuleListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaxRuleList indicates an expected call of GetTaxRuleList.
func (mr *MockCompanyServiceClientMockRecorder) GetTaxRuleList(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaxRuleList", reflect.TypeOf((*MockCompanyServiceClient)(nil).GetTaxRuleList), varargs...)
}

// IsCompaniesMigrate mocks base method.
func (m *MockCompanyServiceClient) IsCompaniesMigrate(ctx context.Context, in *organizationsvcpb.IsCompaniesMigrateRequest, opts ...grpc.CallOption) (*organizationsvcpb.IsCompaniesMigrateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsCompaniesMigrate", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.IsCompaniesMigrateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsCompaniesMigrate indicates an expected call of IsCompaniesMigrate.
func (mr *MockCompanyServiceClientMockRecorder) IsCompaniesMigrate(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCompaniesMigrate", reflect.TypeOf((*MockCompanyServiceClient)(nil).IsCompaniesMigrate), varargs...)
}

// IsCompanyMigrate mocks base method.
func (m *MockCompanyServiceClient) IsCompanyMigrate(ctx context.Context, in *organizationsvcpb.IsCompanyMigrateRequest, opts ...grpc.CallOption) (*organizationsvcpb.IsCompanyMigrateResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsCompanyMigrate", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.IsCompanyMigrateResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsCompanyMigrate indicates an expected call of IsCompanyMigrate.
func (mr *MockCompanyServiceClientMockRecorder) IsCompanyMigrate(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCompanyMigrate", reflect.TypeOf((*MockCompanyServiceClient)(nil).IsCompanyMigrate), varargs...)
}

// IsMoegoPayEnable mocks base method.
func (m *MockCompanyServiceClient) IsMoegoPayEnable(ctx context.Context, in *organizationsvcpb.IsMoegoPayEnableRequest, opts ...grpc.CallOption) (*organizationsvcpb.IsMoegoPayEnableResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsMoegoPayEnable", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.IsMoegoPayEnableResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsMoegoPayEnable indicates an expected call of IsMoegoPayEnable.
func (mr *MockCompanyServiceClientMockRecorder) IsMoegoPayEnable(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsMoegoPayEnable", reflect.TypeOf((*MockCompanyServiceClient)(nil).IsMoegoPayEnable), varargs...)
}

// IsRetailEnable mocks base method.
func (m *MockCompanyServiceClient) IsRetailEnable(ctx context.Context, in *organizationsvcpb.IsRetailEnableRequest, opts ...grpc.CallOption) (*organizationsvcpb.IsRetailEnableResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsRetailEnable", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.IsRetailEnableResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsRetailEnable indicates an expected call of IsRetailEnable.
func (mr *MockCompanyServiceClientMockRecorder) IsRetailEnable(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsRetailEnable", reflect.TypeOf((*MockCompanyServiceClient)(nil).IsRetailEnable), varargs...)
}

// ListCompaniesByEnterpriseId mocks base method.
func (m *MockCompanyServiceClient) ListCompaniesByEnterpriseId(ctx context.Context, in *organizationsvcpb.ListCompaniesByEnterpriseIdRequest, opts ...grpc.CallOption) (*organizationsvcpb.ListCompaniesByEnterpriseIdResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListCompaniesByEnterpriseId", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.ListCompaniesByEnterpriseIdResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCompaniesByEnterpriseId indicates an expected call of ListCompaniesByEnterpriseId.
func (mr *MockCompanyServiceClientMockRecorder) ListCompaniesByEnterpriseId(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCompaniesByEnterpriseId", reflect.TypeOf((*MockCompanyServiceClient)(nil).ListCompaniesByEnterpriseId), varargs...)
}

// ListCompany mocks base method.
func (m *MockCompanyServiceClient) ListCompany(ctx context.Context, in *organizationsvcpb.ListCompanyRequest, opts ...grpc.CallOption) (*organizationsvcpb.ListCompanyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListCompany", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.ListCompanyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCompany indicates an expected call of ListCompany.
func (mr *MockCompanyServiceClientMockRecorder) ListCompany(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCompany", reflect.TypeOf((*MockCompanyServiceClient)(nil).ListCompany), varargs...)
}

// ListCompanyPreferenceSettings mocks base method.
func (m *MockCompanyServiceClient) ListCompanyPreferenceSettings(ctx context.Context, in *organizationsvcpb.ListCompanyPreferenceSettingsRequest, opts ...grpc.CallOption) (*organizationsvcpb.ListCompanyPreferenceSettingsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListCompanyPreferenceSettings", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.ListCompanyPreferenceSettingsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCompanyPreferenceSettings indicates an expected call of ListCompanyPreferenceSettings.
func (mr *MockCompanyServiceClientMockRecorder) ListCompanyPreferenceSettings(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCompanyPreferenceSettings", reflect.TypeOf((*MockCompanyServiceClient)(nil).ListCompanyPreferenceSettings), varargs...)
}

// QueryCompaniesByIds mocks base method.
func (m *MockCompanyServiceClient) QueryCompaniesByIds(ctx context.Context, in *organizationsvcpb.QueryCompaniesByIdsRequest, opts ...grpc.CallOption) (*organizationsvcpb.QueryCompaniesByIdsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "QueryCompaniesByIds", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.QueryCompaniesByIdsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryCompaniesByIds indicates an expected call of QueryCompaniesByIds.
func (mr *MockCompanyServiceClientMockRecorder) QueryCompaniesByIds(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryCompaniesByIds", reflect.TypeOf((*MockCompanyServiceClient)(nil).QueryCompaniesByIds), varargs...)
}

// SetCompanyMigrateStatus mocks base method.
func (m *MockCompanyServiceClient) SetCompanyMigrateStatus(ctx context.Context, in *organizationsvcpb.SetCompanyMigrateStatusRequest, opts ...grpc.CallOption) (*organizationsvcpb.SetCompanyMigrateStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetCompanyMigrateStatus", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.SetCompanyMigrateStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetCompanyMigrateStatus indicates an expected call of SetCompanyMigrateStatus.
func (mr *MockCompanyServiceClientMockRecorder) SetCompanyMigrateStatus(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCompanyMigrateStatus", reflect.TypeOf((*MockCompanyServiceClient)(nil).SetCompanyMigrateStatus), varargs...)
}

// SortCompany mocks base method.
func (m *MockCompanyServiceClient) SortCompany(ctx context.Context, in *organizationsvcpb.SortCompanyRequest, opts ...grpc.CallOption) (*organizationsvcpb.SortCompanyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SortCompany", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.SortCompanyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SortCompany indicates an expected call of SortCompany.
func (mr *MockCompanyServiceClientMockRecorder) SortCompany(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortCompany", reflect.TypeOf((*MockCompanyServiceClient)(nil).SortCompany), varargs...)
}

// UpdateClockInOutSetting mocks base method.
func (m *MockCompanyServiceClient) UpdateClockInOutSetting(ctx context.Context, in *organizationsvcpb.UpdateClockInOutSettingRequest, opts ...grpc.CallOption) (*organizationsvcpb.UpdateClockInOutSettingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateClockInOutSetting", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.UpdateClockInOutSettingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateClockInOutSetting indicates an expected call of UpdateClockInOutSetting.
func (mr *MockCompanyServiceClientMockRecorder) UpdateClockInOutSetting(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateClockInOutSetting", reflect.TypeOf((*MockCompanyServiceClient)(nil).UpdateClockInOutSetting), varargs...)
}

// UpdateCompany mocks base method.
func (m *MockCompanyServiceClient) UpdateCompany(ctx context.Context, in *organizationsvcpb.UpdateCompanyRequest, opts ...grpc.CallOption) (*organizationsvcpb.UpdateCompanyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCompany", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.UpdateCompanyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCompany indicates an expected call of UpdateCompany.
func (mr *MockCompanyServiceClientMockRecorder) UpdateCompany(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCompany", reflect.TypeOf((*MockCompanyServiceClient)(nil).UpdateCompany), varargs...)
}

// UpdateCompanyPreferenceSetting mocks base method.
func (m *MockCompanyServiceClient) UpdateCompanyPreferenceSetting(ctx context.Context, in *organizationsvcpb.UpdateCompanyPreferenceSettingRequest, opts ...grpc.CallOption) (*organizationsvcpb.UpdateCompanyPreferenceSettingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCompanyPreferenceSetting", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.UpdateCompanyPreferenceSettingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCompanyPreferenceSetting indicates an expected call of UpdateCompanyPreferenceSetting.
func (mr *MockCompanyServiceClientMockRecorder) UpdateCompanyPreferenceSetting(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCompanyPreferenceSetting", reflect.TypeOf((*MockCompanyServiceClient)(nil).UpdateCompanyPreferenceSetting), varargs...)
}

// UpdateTaxRule mocks base method.
func (m *MockCompanyServiceClient) UpdateTaxRule(ctx context.Context, in *organizationsvcpb.UpdateTaxRuleRequest, opts ...grpc.CallOption) (*organizationsvcpb.UpdateTaxRuleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateTaxRule", varargs...)
	ret0, _ := ret[0].(*organizationsvcpb.UpdateTaxRuleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateTaxRule indicates an expected call of UpdateTaxRule.
func (mr *MockCompanyServiceClientMockRecorder) UpdateTaxRule(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateTaxRule", reflect.TypeOf((*MockCompanyServiceClient)(nil).UpdateTaxRule), varargs...)
}
