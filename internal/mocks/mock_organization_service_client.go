// Code generated by MockGen. DO NOT EDIT.
// Source: internal/clients/business.go

// Package mocks is a generated GoMock package.
package mocks

import (
	"context"
	"reflect"

	"go.uber.org/mock/gomock"

	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1"
)

// MockOrganizationClient is a mock of OrganizationClient interface.
type MockOrganizationClient struct {
	ctrl     *gomock.Controller
	recorder *MockOrganizationClientMockRecorder
}

// MockOrganizationClientMockRecorder is the mock recorder for MockOrganizationClient.
type MockOrganizationClientMockRecorder struct {
	mock *MockOrganizationClient
}

// NewMockOrganizationClient creates a new mock instance.
func NewMockOrganizationClient(ctrl *gomock.Controller) *MockOrganizationClient {
	mock := &MockOrganizationClient{ctrl: ctrl}
	mock.recorder = &MockOrganizationClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrganizationClient) EXPECT() *MockOrganizationClientMockRecorder {
	return m.recorder
}

// GetBusinessInfo mocks base method.
func (m *MockOrganizationClient) GetBusinessInfo(ctx context.Context, businessId int64) (*v1.BusinessModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBusinessInfo", ctx, businessId)
	ret0, _ := ret[0].(*v1.BusinessModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBusinessInfo indicates an expected call of GetBusinessInfo.
func (mr *MockOrganizationClientMockRecorder) GetBusinessInfo(ctx, businessId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBusinessInfo", reflect.TypeOf((*MockOrganizationClient)(nil).GetBusinessInfo), ctx, businessId)
}

// GetCompanyIDByLocationID mocks base method.
func (m *MockOrganizationClient) GetCompanyIDByLocationID(ctx context.Context, businessIDs []int64) (map[int64]int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCompanyIDByLocationID", ctx, businessIDs)
	ret0, _ := ret[0].(map[int64]int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCompanyIDByLocationID indicates an expected call of GetCompanyIDByLocationID.
func (mr *MockOrganizationClientMockRecorder) GetCompanyIDByLocationID(ctx, businessIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCompanyIDByLocationID", reflect.TypeOf((*MockOrganizationClient)(nil).GetCompanyIDByLocationID), ctx, businessIDs)
}
