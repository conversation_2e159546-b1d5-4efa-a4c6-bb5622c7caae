// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: ServiceBundleSaleMappingRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_service_bundle_sale_mapping_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceBundleSaleMappingRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockServiceBundleSaleMappingRepository is a mock of ServiceBundleSaleMappingRepository interface.
type MockServiceBundleSaleMappingRepository struct {
	ctrl     *gomock.Controller
	recorder *MockServiceBundleSaleMappingRepositoryMockRecorder
	isgomock struct{}
}

// MockServiceBundleSaleMappingRepositoryMockRecorder is the mock recorder for MockServiceBundleSaleMappingRepository.
type MockServiceBundleSaleMappingRepositoryMockRecorder struct {
	mock *MockServiceBundleSaleMappingRepository
}

// NewMockServiceBundleSaleMappingRepository creates a new mock instance.
func NewMockServiceBundleSaleMappingRepository(ctrl *gomock.Controller) *MockServiceBundleSaleMappingRepository {
	mock := &MockServiceBundleSaleMappingRepository{ctrl: ctrl}
	mock.recorder = &MockServiceBundleSaleMappingRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceBundleSaleMappingRepository) EXPECT() *MockServiceBundleSaleMappingRepositoryMockRecorder {
	return m.recorder
}

// BatchInsert mocks base method.
func (m *MockServiceBundleSaleMappingRepository) BatchInsert(ctx context.Context, tx *gorm.DB, companyId, parentServiceId int64, bundleServiceIds []int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchInsert", ctx, tx, companyId, parentServiceId, bundleServiceIds)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchInsert indicates an expected call of BatchInsert.
func (mr *MockServiceBundleSaleMappingRepositoryMockRecorder) BatchInsert(ctx, tx, companyId, parentServiceId, bundleServiceIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchInsert", reflect.TypeOf((*MockServiceBundleSaleMappingRepository)(nil).BatchInsert), ctx, tx, companyId, parentServiceId, bundleServiceIds)
}

// Delete mocks base method.
func (m *MockServiceBundleSaleMappingRepository) Delete(ctx context.Context, tx *gorm.DB, companyId, parentServiceId int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, tx, companyId, parentServiceId)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockServiceBundleSaleMappingRepositoryMockRecorder) Delete(ctx, tx, companyId, parentServiceId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockServiceBundleSaleMappingRepository)(nil).Delete), ctx, tx, companyId, parentServiceId)
}

// List mocks base method.
func (m *MockServiceBundleSaleMappingRepository) List(ctx context.Context, companyId int64, parentServiceIds []int64) ([]*models.ServiceBundleSaleMapping, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, companyId, parentServiceIds)
	ret0, _ := ret[0].([]*models.ServiceBundleSaleMapping)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockServiceBundleSaleMappingRepositoryMockRecorder) List(ctx, companyId, parentServiceIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockServiceBundleSaleMappingRepository)(nil).List), ctx, companyId, parentServiceIds)
}
