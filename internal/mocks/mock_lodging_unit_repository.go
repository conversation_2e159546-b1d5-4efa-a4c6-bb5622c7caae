// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: LodgingUnitRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_lodging_unit_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository LodgingUnitRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	models "github.com/MoeGolibrary/moego-svc-offering/internal/models"
	repository "github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	gomock "go.uber.org/mock/gomock"
	gorm "gorm.io/gorm"
)

// MockLodgingUnitRepository is a mock of LodgingUnitRepository interface.
type MockLodgingUnitRepository struct {
	ctrl     *gomock.Controller
	recorder *MockLodgingUnitRepositoryMockRecorder
	isgomock struct{}
}

// MockLodgingUnitRepositoryMockRecorder is the mock recorder for MockLodgingUnitRepository.
type MockLodgingUnitRepositoryMockRecorder struct {
	mock *MockLodgingUnitRepository
}

// NewMockLodgingUnitRepository creates a new mock instance.
func NewMockLodgingUnitRepository(ctrl *gomock.Controller) *MockLodgingUnitRepository {
	mock := &MockLodgingUnitRepository{ctrl: ctrl}
	mock.recorder = &MockLodgingUnitRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLodgingUnitRepository) EXPECT() *MockLodgingUnitRepositoryMockRecorder {
	return m.recorder
}

// BatchAdd mocks base method.
func (m *MockLodgingUnitRepository) BatchAdd(ctx context.Context, dos []*do.LodgingUnitDO) ([]*do.LodgingUnitDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchAdd", ctx, dos)
	ret0, _ := ret[0].([]*do.LodgingUnitDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchAdd indicates an expected call of BatchAdd.
func (mr *MockLodgingUnitRepositoryMockRecorder) BatchAdd(ctx, dos any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchAdd", reflect.TypeOf((*MockLodgingUnitRepository)(nil).BatchAdd), ctx, dos)
}

// BatchDelete mocks base method.
func (m *MockLodgingUnitRepository) BatchDelete(ctx context.Context, ids []int64, companyId, deletedBy *int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchDelete", ctx, ids, companyId, deletedBy)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchDelete indicates an expected call of BatchDelete.
func (mr *MockLodgingUnitRepositoryMockRecorder) BatchDelete(ctx, ids, companyId, deletedBy any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchDelete", reflect.TypeOf((*MockLodgingUnitRepository)(nil).BatchDelete), ctx, ids, companyId, deletedBy)
}

// BatchUpdateById mocks base method.
func (m *MockLodgingUnitRepository) BatchUpdateById(ctx context.Context, companyId *int64, updateOpts []*do.LodgingUnitUpdateByIDOpt) ([]*do.LodgingUnitDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateById", ctx, companyId, updateOpts)
	ret0, _ := ret[0].([]*do.LodgingUnitDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchUpdateById indicates an expected call of BatchUpdateById.
func (mr *MockLodgingUnitRepositoryMockRecorder) BatchUpdateById(ctx, companyId, updateOpts any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateById", reflect.TypeOf((*MockLodgingUnitRepository)(nil).BatchUpdateById), ctx, companyId, updateOpts)
}

// GetMaxSort mocks base method.
func (m *MockLodgingUnitRepository) GetMaxSort(ctx context.Context, whereOpt *do.LodgingUnitWhereOpt) (int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMaxSort", ctx, whereOpt)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMaxSort indicates an expected call of GetMaxSort.
func (mr *MockLodgingUnitRepositoryMockRecorder) GetMaxSort(ctx, whereOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMaxSort", reflect.TypeOf((*MockLodgingUnitRepository)(nil).GetMaxSort), ctx, whereOpt)
}

// IsExist mocks base method.
func (m *MockLodgingUnitRepository) IsExist(ctx context.Context, companyID *int64, whereOpt *do.LodgingUnitWhereOpt) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsExist", ctx, companyID, whereOpt)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsExist indicates an expected call of IsExist.
func (mr *MockLodgingUnitRepositoryMockRecorder) IsExist(ctx, companyID, whereOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExist", reflect.TypeOf((*MockLodgingUnitRepository)(nil).IsExist), ctx, companyID, whereOpt)
}

// List mocks base method.
func (m *MockLodgingUnitRepository) List(ctx context.Context, whereOpt *models.LodgingUnitWhereOpt) ([]*models.LodgingUnit, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, whereOpt)
	ret0, _ := ret[0].([]*models.LodgingUnit)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockLodgingUnitRepositoryMockRecorder) List(ctx, whereOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockLodgingUnitRepository)(nil).List), ctx, whereOpt)
}

// MGet mocks base method.
func (m *MockLodgingUnitRepository) MGet(ctx context.Context, idList []int64) ([]*do.LodgingUnitDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MGet", ctx, idList)
	ret0, _ := ret[0].([]*do.LodgingUnitDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MGet indicates an expected call of MGet.
func (mr *MockLodgingUnitRepositoryMockRecorder) MGet(ctx, idList any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGet", reflect.TypeOf((*MockLodgingUnitRepository)(nil).MGet), ctx, idList)
}

// Update mocks base method.
func (m *MockLodgingUnitRepository) Update(ctx context.Context, whereOpt *models.LodgingUnitWhereOpt, updateOpt *do.LodgingUnitUpdateOpt) (*do.LodgingUnitDO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, whereOpt, updateOpt)
	ret0, _ := ret[0].(*do.LodgingUnitDO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Update indicates an expected call of Update.
func (mr *MockLodgingUnitRepositoryMockRecorder) Update(ctx, whereOpt, updateOpt any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockLodgingUnitRepository)(nil).Update), ctx, whereOpt, updateOpt)
}

// WithTx mocks base method.
func (m *MockLodgingUnitRepository) WithTx(tx *gorm.DB) repository.LodgingUnitRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTx", tx)
	ret0, _ := ret[0].(repository.LodgingUnitRepository)
	return ret0
}

// WithTx indicates an expected call of WithTx.
func (mr *MockLodgingUnitRepositoryMockRecorder) WithTx(tx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTx", reflect.TypeOf((*MockLodgingUnitRepository)(nil).WithTx), tx)
}
