// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: AutoRolloverRuleRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_auto_rollover_rule_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository AutoRolloverRuleRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	gomock "go.uber.org/mock/gomock"
)

// MockAutoRolloverRuleRepository is a mock of AutoRolloverRuleRepository interface.
type MockAutoRolloverRuleRepository struct {
	ctrl     *gomock.Controller
	recorder *MockAutoRolloverRuleRepositoryMockRecorder
	isgomock struct{}
}

// MockAutoRolloverRuleRepositoryMockRecorder is the mock recorder for MockAutoRolloverRuleRepository.
type MockAutoRolloverRuleRepositoryMockRecorder struct {
	mock *MockAutoRolloverRuleRepository
}

// NewMockAutoRolloverRuleRepository creates a new mock instance.
func NewMockAutoRolloverRuleRepository(ctrl *gomock.Controller) *MockAutoRolloverRuleRepository {
	mock := &MockAutoRolloverRuleRepository{ctrl: ctrl}
	mock.recorder = &MockAutoRolloverRuleRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAutoRolloverRuleRepository) EXPECT() *MockAutoRolloverRuleRepositoryMockRecorder {
	return m.recorder
}

// BatchGetAutoRolloverRules mocks base method.
func (m *MockAutoRolloverRuleRepository) BatchGetAutoRolloverRules(ctx context.Context, serviceIds []int64) (map[int64]*do.AutoRolloverRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetAutoRolloverRules", ctx, serviceIds)
	ret0, _ := ret[0].(map[int64]*do.AutoRolloverRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetAutoRolloverRules indicates an expected call of BatchGetAutoRolloverRules.
func (mr *MockAutoRolloverRuleRepositoryMockRecorder) BatchGetAutoRolloverRules(ctx, serviceIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetAutoRolloverRules", reflect.TypeOf((*MockAutoRolloverRuleRepository)(nil).BatchGetAutoRolloverRules), ctx, serviceIds)
}
