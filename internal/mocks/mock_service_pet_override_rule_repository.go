// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-offering/internal/repository (interfaces: ServicePetOverrideRuleRepository)
//
// Generated by this command:
//
//	mockgen -package mocks -destination ../mocks/mock_service_pet_override_rule_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServicePetOverrideRuleRepository
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	do "github.com/MoeGolibrary/moego-svc-offering/internal/do"
	gomock "go.uber.org/mock/gomock"
)

// MockServicePetOverrideRuleRepository is a mock of ServicePetOverrideRuleRepository interface.
type MockServicePetOverrideRuleRepository struct {
	ctrl     *gomock.Controller
	recorder *MockServicePetOverrideRuleRepositoryMockRecorder
	isgomock struct{}
}

// MockServicePetOverrideRuleRepositoryMockRecorder is the mock recorder for MockServicePetOverrideRuleRepository.
type MockServicePetOverrideRuleRepositoryMockRecorder struct {
	mock *MockServicePetOverrideRuleRepository
}

// NewMockServicePetOverrideRuleRepository creates a new mock instance.
func NewMockServicePetOverrideRuleRepository(ctrl *gomock.Controller) *MockServicePetOverrideRuleRepository {
	mock := &MockServicePetOverrideRuleRepository{ctrl: ctrl}
	mock.recorder = &MockServicePetOverrideRuleRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServicePetOverrideRuleRepository) EXPECT() *MockServicePetOverrideRuleRepositoryMockRecorder {
	return m.recorder
}

// ListServicePetOverrideRules mocks base method.
func (m *MockServicePetOverrideRuleRepository) ListServicePetOverrideRules(ctx context.Context, companyId int64, overrideConditions map[int64][]do.PetOverrideCondition) (map[int64][]do.PetOverrideRule, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListServicePetOverrideRules", ctx, companyId, overrideConditions)
	ret0, _ := ret[0].(map[int64][]do.PetOverrideRule)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListServicePetOverrideRules indicates an expected call of ListServicePetOverrideRules.
func (mr *MockServicePetOverrideRuleRepositoryMockRecorder) ListServicePetOverrideRules(ctx, companyId, overrideConditions any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListServicePetOverrideRules", reflect.TypeOf((*MockServicePetOverrideRuleRepository)(nil).ListServicePetOverrideRules), ctx, companyId, overrideConditions)
}
