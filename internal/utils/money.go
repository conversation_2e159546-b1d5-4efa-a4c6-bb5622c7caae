package utils

import (
	"google.golang.org/genproto/googleapis/type/money"
	"math"
)

func ConvertToPBMoney(amount float64, currencyCode string) *money.Money {
	const nanoFactor = 1e9
	totalNanos := int64(math.Round(amount * nanoFactor))
	units := totalNanos / nanoFactor
	nanos := int32(totalNanos % nanoFactor)
	return &money.Money{
		CurrencyCode: currencyCode,
		Units:        units,
		Nanos:        nanos,
	}
}