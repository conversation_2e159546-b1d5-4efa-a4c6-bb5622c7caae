package utils

import (
	"errors"
	"time"

	"google.golang.org/genproto/googleapis/type/datetime"
)

func GetTimeFromGoogleDateTime(dt *datetime.DateTime) (time.Time, error) {
	if dt == nil {
		return time.Time{}, nil
	}

	var loc *time.Location
	if dt.GetTimeOffset() != nil {
		switch offset := dt.TimeOffset.(type) {
		case *datetime.DateTime_UtcOffset:
			loc = time.FixedZone("UTC", int(offset.UtcOffset.Seconds))
		case *datetime.DateTime_TimeZone:
			var err error
			loc, err = time.LoadLocation(offset.TimeZone.Id)
			if err != nil {
				return time.Time{}, err
			}
		}
	}
	if loc == nil {
		return time.Time{}, errors.New("location is nil")
	}
	return time.Date(
		int(dt.GetYear()),
		time.Month(dt.GetMonth()),
		int(dt.GetDay()),
		int(dt.GetHours()),
		int(dt.GetMinutes()),
		int(dt.GetSeconds()),
		int(dt.GetNanos()),
		loc,
	), nil
}

func GoogleDateTimeFromTime(t *time.Time) *datetime.DateTime {
	return &datetime.DateTime{
		Year:    int32(t.Year()),
		Month:   int32(t.Month()),
		Day:     int32(t.Day()),
		Hours:   int32(t.Hour()),
		Minutes: int32(t.Minute()),
		Seconds: int32(t.Second()),
		Nanos:   int32(t.Nanosecond()),
		TimeOffset: &datetime.DateTime_TimeZone{
			TimeZone: &datetime.TimeZone{
				Id: t.Location().String(),
			},
		},
	}
}
