package utils

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/money"
)

func TestConvertToPBMoney(t *testing.T) {
	type args struct {
		amount       float64
		currencyCode string
	}
	tests := []struct {
		name string
		args args
		want *money.Money
	}{
		{
			name: "positive amount with decimals",
			args: args{amount: 123.45, currencyCode: "USD"},
			want: &money.Money{CurrencyCode: "USD", Units: 123, Nanos: 450000000},
		},
		{
			name: "positive amount without decimals",
			args: args{amount: 123, currencyCode: "USD"},
			want: &money.Money{CurrencyCode: "USD", Units: 123, Nanos: 0},
		},
		{
			name: "zero amount",
			args: args{amount: 0, currencyCode: "USD"},
			want: &money.Money{CurrencyCode: "USD", Units: 0, Nanos: 0},
		},
		{
			name: "negative amount with decimals",
			args: args{amount: -123.45, currencyCode: "EUR"},
			want: &money.Money{CurrencyCode: "EUR", Units: -123, Nanos: -450000000},
		},
		{
			name: "amount with many decimal places",
			args: args{amount: 0.123456789, currencyCode: "JPY"},
			want: &money.Money{CurrencyCode: "JPY", Units: 0, Nanos: 123456789},
		},
		{
			name: "amount close to next unit",
			args: args{amount: 1.999999999, currencyCode: "GBP"},
			want: &money.Money{CurrencyCode: "GBP", Units: 1, Nanos: 999999999},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ConvertToPBMoney(tt.args.amount, tt.args.currencyCode)
			assert.Equal(t, tt.want, got)
		})
	}
}
