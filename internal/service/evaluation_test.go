package service

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"github.com/DATA-DOG/go-sqlmock"
	customerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/mocks"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"go.uber.org/mock/gomock"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestEvaluationHandler_EvaluationFilterByPet(t *testing.T) {
	// Setup
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockEvaluationRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)
	handler := &evaluationHandler{
		evaluationRepository:  mockEvaluationRepo,
		breedFilterRepository: mockBreedFilterRepo,
	}

	// Helper function to create a string pointer
	strPtr := func(s string) *string {
		return &s
	}

	// Test cases
	tests := []struct {
		name              string
		filterByPet       *offeringpb.ServiceFilterByPet
		evaluationList    []*do.EvaluationDO
		breedFilters      []models.EvaluationPetBreedFilter
		expectedIDs       []int64
		expectedErrString string
	}{
		{
			name:        "Nil filter returns all evaluations",
			filterByPet: nil,
			evaluationList: []*do.EvaluationDO{
				{Id: 1, BreedFilter: true},
				{Id: 2, BreedFilter: false},
			},
			breedFilters: nil,
			expectedIDs:  []int64{1, 2},
		},
		{
			name: "Repository error",
			filterByPet: &offeringpb.ServiceFilterByPet{
				PetType:  customerpb.PetType_PET_TYPE_DOG,
				PetBreed: strPtr("Golden Retriever"),
			},
			evaluationList: []*do.EvaluationDO{
				{Id: 1, BreedFilter: true},
			},
			expectedErrString: "database error",
		},
		{
			name: "Breed filter disabled returns all evaluations",
			filterByPet: &offeringpb.ServiceFilterByPet{
				PetType:  customerpb.PetType_PET_TYPE_DOG,
				PetBreed: strPtr("Golden Retriever"),
			},
			evaluationList: []*do.EvaluationDO{
				{Id: 1, BreedFilter: false},
			},
			breedFilters: []models.EvaluationPetBreedFilter{},
			expectedIDs:  []int64{1},
		},
		{
			name: "Empty breed filters returns all evaluations",
			filterByPet: &offeringpb.ServiceFilterByPet{
				PetType:  customerpb.PetType_PET_TYPE_DOG,
				PetBreed: strPtr("Golden Retriever"),
			},
			evaluationList: []*do.EvaluationDO{
				{Id: 1, BreedFilter: true},
			},
			breedFilters: []models.EvaluationPetBreedFilter{},
			expectedIDs:  []int64{1},
		},
		{
			name: "Pet type mismatch excludes evaluation",
			filterByPet: &offeringpb.ServiceFilterByPet{
				PetType:  customerpb.PetType_PET_TYPE_DOG,
				PetBreed: strPtr("Golden Retriever"),
			},
			evaluationList: []*do.EvaluationDO{
				{Id: 1, BreedFilter: true},
			},
			breedFilters: []models.EvaluationPetBreedFilter{
				{
					ID:           10,
					EvaluationID: 1,
					PetType:      customerpb.PetType_PET_TYPE_CAT,
					IsAllBreed:   false,
					BreedNames:   []string{"Persian", "Siamese"},
				},
			},
			expectedIDs: []int64{},
		},
		{
			name: "IsAllBreed true includes evaluation",
			filterByPet: &offeringpb.ServiceFilterByPet{
				PetType:  customerpb.PetType_PET_TYPE_DOG,
				PetBreed: strPtr("Golden Retriever"),
			},
			evaluationList: []*do.EvaluationDO{
				{Id: 1, BreedFilter: true},
			},
			breedFilters: []models.EvaluationPetBreedFilter{
				{
					ID:           10,
					EvaluationID: 1,
					PetType:      customerpb.PetType_PET_TYPE_DOG,
					IsAllBreed:   true,
					BreedNames:   []string{},
				},
			},
			expectedIDs: []int64{1},
		},
		{
			name: "Breed matches and includes evaluation",
			filterByPet: &offeringpb.ServiceFilterByPet{
				PetType:  customerpb.PetType_PET_TYPE_DOG,
				PetBreed: strPtr("Golden Retriever"),
			},
			evaluationList: []*do.EvaluationDO{
				{Id: 1, BreedFilter: true},
			},
			breedFilters: []models.EvaluationPetBreedFilter{
				{
					ID:           10,
					EvaluationID: 1,
					PetType:      customerpb.PetType_PET_TYPE_DOG,
					IsAllBreed:   false,
					BreedNames:   []string{"Poodle", "Golden Retriever", "Labrador"},
				},
			},
			expectedIDs: []int64{1},
		},
		{
			name: "Breed doesn't match and excludes evaluation",
			filterByPet: &offeringpb.ServiceFilterByPet{
				PetType:  customerpb.PetType_PET_TYPE_DOG,
				PetBreed: strPtr("Bulldog"),
			},
			evaluationList: []*do.EvaluationDO{
				{Id: 1, BreedFilter: true},
			},
			breedFilters: []models.EvaluationPetBreedFilter{
				{
					ID:           10,
					EvaluationID: 1,
					PetType:      customerpb.PetType_PET_TYPE_DOG,
					IsAllBreed:   false,
					BreedNames:   []string{"Poodle", "Golden Retriever", "Labrador"},
				},
			},
			expectedIDs: []int64{},
		},
		{
			name: "Multiple evaluations with mixed filters",
			filterByPet: &offeringpb.ServiceFilterByPet{
				PetType:  customerpb.PetType_PET_TYPE_DOG,
				PetBreed: strPtr("Poodle"),
			},
			evaluationList: []*do.EvaluationDO{
				{Id: 1, BreedFilter: true},  // Has matching breed
				{Id: 2, BreedFilter: true},  // Has all breeds
				{Id: 3, BreedFilter: true},  // Has non-matching breed
				{Id: 4, BreedFilter: true},  // Has different pet type
				{Id: 5, BreedFilter: false}, // Filter disabled
			},
			breedFilters: []models.EvaluationPetBreedFilter{
				{
					ID:           10,
					EvaluationID: 1,
					PetType:      customerpb.PetType_PET_TYPE_DOG,
					IsAllBreed:   false,
					BreedNames:   []string{"Poodle", "Golden Retriever"},
				},
				{
					ID:           11,
					EvaluationID: 2,
					PetType:      customerpb.PetType_PET_TYPE_DOG,
					IsAllBreed:   true,
					BreedNames:   []string{},
				},
				{
					ID:           12,
					EvaluationID: 3,
					PetType:      customerpb.PetType_PET_TYPE_DOG,
					IsAllBreed:   false,
					BreedNames:   []string{"Labrador", "Bulldog"},
				},
				{
					ID:           13,
					EvaluationID: 4,
					PetType:      customerpb.PetType_PET_TYPE_CAT,
					IsAllBreed:   false,
					BreedNames:   []string{"Persian", "Siamese"},
				},
			},
			expectedIDs: []int64{1, 2, 5},
		},
	}

	// Run test cases
	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			// Skip repository mock setup for nil filter case
			if tc.filterByPet != nil {
				evaluationIds := lo.Map(tc.evaluationList, func(e *do.EvaluationDO, _ int) int64 {
					return e.Id
				})

				// If we expect an error
				if tc.expectedErrString != "" {
					mockBreedFilterRepo.EXPECT().
						ListByEvaluationIDs(gomock.Any(), gomock.Eq(evaluationIds)).
						Return(nil, fmt.Errorf("%s", tc.expectedErrString)).
						Times(1)
				} else {
					mockBreedFilterRepo.EXPECT().
						ListByEvaluationIDs(gomock.Any(), gomock.Eq(evaluationIds)).
						Return(tc.breedFilters, nil).
						Times(1)
				}
			}

			// Execute the method
			filteredEvals, err := handler.evaluationFilterByPet(context.TODO(), tc.filterByPet, tc.evaluationList)

			// Assertions
			if tc.expectedErrString != "" {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tc.expectedErrString)
			} else {
				require.NoError(t, err)

				// Extract IDs from filtered evaluations for easier comparison
				filteredIDs := lo.Map(filteredEvals, func(e *do.EvaluationDO, _ int) int64 {
					return e.Id
				})

				assert.ElementsMatch(t, tc.expectedIDs, filteredIDs)
			}
		})
	}
}

func TestGetEvaluationList(t *testing.T) {
	// 创建控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建mock对象
	mockEvalRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)

	// 测试数据
	companyID := int64(123)
	mockEvaluations := []*do.EvaluationDO{
		{Id: 1, Name: "评估1", CompanyId: 123},
		{Id: 2, Name: "评估2", CompanyId: 123},
	}

	mockBreedFilters := []models.EvaluationPetBreedFilter{
		{
			ID:           10,
			EvaluationID: 1,
			PetType:      customerpb.PetType_PET_TYPE_DOG,
			IsAllBreed:   false,
			BreedNames:   []string{"Poodle", "Golden Retriever"},
		},
	}

	// 预期的输出(已经应用了converter)
	expectedEvaluations := []*do.EvaluationDO{
		{
			Id: 1, Name: "评估1", CompanyId: 123,
			PetBreedFilters: []do.PetBreedFilter{
				{
					PetType:    customerpb.PetType_PET_TYPE_DOG,
					IsAllBreed: false,
					BreedNames: []string{"Poodle", "Golden Retriever"},
				},
			},
		},
		{Id: 2, Name: "评估2", CompanyId: 123, PetBreedFilters: []do.PetBreedFilter{}},
	}

	// 设置mock期望
	evaluationIDs := lo.Map(mockEvaluations, func(e *do.EvaluationDO, _ int) int64 {
		return e.Id
	})

	mockEvalRepo.EXPECT().
		GetEvaluationList(gomock.Any(), companyID).
		Return(mockEvaluations, nil)

	mockBreedFilterRepo.EXPECT().
		ListByEvaluationIDs(gomock.Any(), evaluationIDs).
		Return(mockBreedFilters, nil)

	// 创建测试对象
	evaluationHandler := &evaluationHandler{
		evaluationRepository:  mockEvalRepo,
		breedFilterRepository: mockBreedFilterRepo,
	}

	// 执行测试
	result, err := evaluationHandler.GetEvaluationList(context.Background(), companyID)

	// 断言
	assert.NoError(t, err)
	assert.Equal(t, len(expectedEvaluations), len(result))

	// 检查第一个评估的数据
	assert.Equal(t, expectedEvaluations[0].Id, result[0].Id)
	assert.Equal(t, expectedEvaluations[0].Name, result[0].Name)
	assert.Equal(t, expectedEvaluations[0].CompanyId, result[0].CompanyId)

	// 检查第一个评估的品种过滤器
	assert.Equal(t, len(expectedEvaluations[0].PetBreedFilters), len(result[0].PetBreedFilters))
	if len(result[0].PetBreedFilters) > 0 {
		assert.Equal(t, expectedEvaluations[0].PetBreedFilters[0].PetType, result[0].PetBreedFilters[0].PetType)
		assert.Equal(t, expectedEvaluations[0].PetBreedFilters[0].IsAllBreed, result[0].PetBreedFilters[0].IsAllBreed)
		assert.ElementsMatch(t, expectedEvaluations[0].PetBreedFilters[0].BreedNames, result[0].PetBreedFilters[0].BreedNames)
	}

	// 检查第二个评估的数据
	assert.Equal(t, expectedEvaluations[1].Id, result[1].Id)
	assert.Equal(t, expectedEvaluations[1].Name, result[1].Name)
	assert.Equal(t, expectedEvaluations[1].CompanyId, result[1].CompanyId)
	assert.Empty(t, result[1].PetBreedFilters)
}

// 测试错误情况
func TestGetEvaluationListWithError(t *testing.T) {
	// 创建控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建mock对象
	mockEvalRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)

	// 测试数据
	companyID := int64(123)
	expectedErr := errors.New("数据库错误")

	// 设置mock期望
	mockEvalRepo.EXPECT().
		GetEvaluationList(gomock.Any(), companyID).
		Return(nil, expectedErr)

	// 创建测试对象
	evaluationHandler := &evaluationHandler{
		evaluationRepository:  mockEvalRepo,
		breedFilterRepository: mockBreedFilterRepo,
	}

	// 执行测试
	result, err := evaluationHandler.GetEvaluationList(context.Background(), companyID)

	// 断言
	assert.Error(t, err)
	assert.Equal(t, expectedErr.Error(), err.Error())
	assert.Nil(t, result)
}

func TestGetEvaluationDetail(t *testing.T) {
	// 创建控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 测试数据
	evaluationID := int64(1)
	mockEvaluation := &do.EvaluationDO{
		Id:        evaluationID,
		Name:      "评估1",
		CompanyId: 123,
	}

	mockBreedFilters := []models.EvaluationPetBreedFilter{
		{
			ID:           10,
			EvaluationID: evaluationID,
			PetType:      customerpb.PetType_PET_TYPE_DOG,
			IsAllBreed:   false,
			BreedNames:   []string{"Poodle", "Golden Retriever"},
		},
	}

	// 创建mock对象
	mockEvalRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)

	// 设置mock期望
	mockEvalRepo.EXPECT().
		GetEvaluationDetail(gomock.Any(), evaluationID).
		Return(mockEvaluation, nil)

	mockBreedFilterRepo.EXPECT().
		ListByEvaluationID(gomock.Any(), evaluationID).
		Return(mockBreedFilters, nil)

	// 创建测试对象
	evaluationHandler := &evaluationHandler{
		evaluationRepository:  mockEvalRepo,
		breedFilterRepository: mockBreedFilterRepo,
	}

	// 执行测试
	result, err := evaluationHandler.GetEvaluationDetail(context.Background(), evaluationID, nil)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, evaluationID, result.Id)
	assert.Equal(t, "评估1", result.Name)
	assert.Equal(t, int64(123), result.CompanyId)

	// 检查品种过滤器
	assert.NotEmpty(t, result.PetBreedFilters)
	assert.Equal(t, customerpb.PetType_PET_TYPE_DOG, result.PetBreedFilters[0].PetType)
	assert.False(t, result.PetBreedFilters[0].IsAllBreed)
	assert.ElementsMatch(t, []string{"Poodle", "Golden Retriever"}, result.PetBreedFilters[0].BreedNames)
}

func TestGetEvaluationDetailEvalRepoError(t *testing.T) {
	// 创建控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 测试数据
	evaluationID := int64(1)
	expectedErr := errors.New("数据库错误")

	// 创建mock对象
	mockEvalRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)

	// 设置mock期望
	mockEvalRepo.EXPECT().
		GetEvaluationDetail(gomock.Any(), evaluationID).
		Return(nil, expectedErr)

	// 创建测试对象
	evaluationHandler := &evaluationHandler{
		evaluationRepository:  mockEvalRepo,
		breedFilterRepository: mockBreedFilterRepo,
	}

	// 执行测试
	result, err := evaluationHandler.GetEvaluationDetail(context.Background(), evaluationID, nil)

	// 断言
	assert.Error(t, err)
	assert.Equal(t, expectedErr.Error(), err.Error())
	assert.Nil(t, result)
}

func TestGetEvaluationDetailBreedFilterRepoError(t *testing.T) {
	// 创建控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 测试数据
	evaluationID := int64(1)
	mockEvaluation := &do.EvaluationDO{
		Id:        evaluationID,
		Name:      "评估1",
		CompanyId: 123,
	}
	expectedErr := errors.New("品种过滤器查询错误")

	// 创建mock对象
	mockEvalRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)

	// 设置mock期望
	mockEvalRepo.EXPECT().
		GetEvaluationDetail(gomock.Any(), evaluationID).
		Return(mockEvaluation, nil)

	mockBreedFilterRepo.EXPECT().
		ListByEvaluationID(gomock.Any(), evaluationID).
		Return(nil, expectedErr)

	// 创建测试对象
	evaluationHandler := &evaluationHandler{
		evaluationRepository:  mockEvalRepo,
		breedFilterRepository: mockBreedFilterRepo,
	}

	// 执行测试
	result, err := evaluationHandler.GetEvaluationDetail(context.Background(), evaluationID, nil)

	// 断言
	assert.Error(t, err)
	assert.Equal(t, expectedErr.Error(), err.Error())
	assert.Nil(t, result)
}

func TestGetEvaluationDetailWithBusinessIdAndLocationOverride(t *testing.T) {
	// 创建控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 测试数据
	evaluationID := int64(1)
	businessID := int64(100)
	originalPrice := 50.0
	originalDuration := int32(30)
	overridePrice := 80.0
	overrideDuration := int32(45)

	mockEvaluation := &do.EvaluationDO{
		Id:        evaluationID,
		Name:      "评估1",
		CompanyId: 123,
		Price:     originalPrice,
		Duration:  originalDuration,
	}

	mockBreedFilters := []models.EvaluationPetBreedFilter{
		{
			ID:           10,
			EvaluationID: evaluationID,
			PetType:      customerpb.PetType_PET_TYPE_DOG,
			IsAllBreed:   false,
			BreedNames:   []string{"Poodle", "Golden Retriever"},
		},
	}

	mockLocationOverrides := []*models.EvaluationLocationOverride{
		{
			ID:           1,
			EvaluationID: evaluationID,
			BusinessID:   businessID,
			Price:        &overridePrice,
			Duration:     &overrideDuration,
		},
	}

	// 创建mock对象
	mockEvalRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)
	mockLocationOverrideRepo := mocks.NewMockEvaluationLocationOverrideRepository(ctrl)

	// 设置mock期望
	mockEvalRepo.EXPECT().
		GetEvaluationDetail(gomock.Any(), evaluationID).
		Return(mockEvaluation, nil)

	mockBreedFilterRepo.EXPECT().
		ListByEvaluationID(gomock.Any(), evaluationID).
		Return(mockBreedFilters, nil)

	mockLocationOverrideRepo.EXPECT().
		ListByEvaluationIDs(gomock.Any(), []int64{evaluationID}).
		Return(mockLocationOverrides, nil)

	// 创建测试对象
	evaluationHandler := &evaluationHandler{
		evaluationRepository:       mockEvalRepo,
		breedFilterRepository:      mockBreedFilterRepo,
		locationOverrideRepository: mockLocationOverrideRepo,
	}

	// 执行测试
	result, err := evaluationHandler.GetEvaluationDetail(context.Background(), evaluationID, &businessID)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, evaluationID, result.Id)
	assert.Equal(t, "评估1", result.Name)
	assert.Equal(t, int64(123), result.CompanyId)

	// 验证价格和时长被覆盖
	assert.Equal(t, overridePrice, result.Price)
	assert.Equal(t, overrideDuration, result.Duration)

	// 检查品种过滤器
	assert.NotEmpty(t, result.PetBreedFilters)
	assert.Equal(t, customerpb.PetType_PET_TYPE_DOG, result.PetBreedFilters[0].PetType)
	assert.False(t, result.PetBreedFilters[0].IsAllBreed)
	assert.ElementsMatch(t, []string{"Poodle", "Golden Retriever"}, result.PetBreedFilters[0].BreedNames)
}

func TestGetEvaluationDetailWithBusinessIdButNoLocationOverride(t *testing.T) {
	// 创建控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 测试数据
	evaluationID := int64(1)
	businessID := int64(100)
	originalPrice := 50.0
	originalDuration := int32(30)

	mockEvaluation := &do.EvaluationDO{
		Id:        evaluationID,
		Name:      "评估1",
		CompanyId: 123,
		Price:     originalPrice,
		Duration:  originalDuration,
	}

	mockBreedFilters := []models.EvaluationPetBreedFilter{
		{
			ID:           10,
			EvaluationID: evaluationID,
			PetType:      customerpb.PetType_PET_TYPE_DOG,
			IsAllBreed:   false,
			BreedNames:   []string{"Poodle", "Golden Retriever"},
		},
	}

	// 没有location override数据
	var mockLocationOverrides []*models.EvaluationLocationOverride

	// 创建mock对象
	mockEvalRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)
	mockLocationOverrideRepo := mocks.NewMockEvaluationLocationOverrideRepository(ctrl)

	// 设置mock期望
	mockEvalRepo.EXPECT().
		GetEvaluationDetail(gomock.Any(), evaluationID).
		Return(mockEvaluation, nil)

	mockBreedFilterRepo.EXPECT().
		ListByEvaluationID(gomock.Any(), evaluationID).
		Return(mockBreedFilters, nil)

	mockLocationOverrideRepo.EXPECT().
		ListByEvaluationIDs(gomock.Any(), []int64{evaluationID}).
		Return(mockLocationOverrides, nil)

	// 创建测试对象
	evaluationHandler := &evaluationHandler{
		evaluationRepository:       mockEvalRepo,
		breedFilterRepository:      mockBreedFilterRepo,
		locationOverrideRepository: mockLocationOverrideRepo,
	}

	// 执行测试
	result, err := evaluationHandler.GetEvaluationDetail(context.Background(), evaluationID, &businessID)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, evaluationID, result.Id)
	assert.Equal(t, "评估1", result.Name)
	assert.Equal(t, int64(123), result.CompanyId)

	// 验证价格和时长保持原值
	assert.Equal(t, originalPrice, result.Price)
	assert.Equal(t, originalDuration, result.Duration)

	// 检查品种过滤器
	assert.NotEmpty(t, result.PetBreedFilters)
	assert.Equal(t, customerpb.PetType_PET_TYPE_DOG, result.PetBreedFilters[0].PetType)
	assert.False(t, result.PetBreedFilters[0].IsAllBreed)
	assert.ElementsMatch(t, []string{"Poodle", "Golden Retriever"}, result.PetBreedFilters[0].BreedNames)
}

func TestGetEvaluationDetailWithPartialLocationOverride(t *testing.T) {
	// 创建控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 测试数据
	evaluationID := int64(1)
	businessID := int64(100)
	originalPrice := 50.0
	originalDuration := int32(30)
	overridePrice := 80.0
	// 注意：这里只覆盖价格，不覆盖时长

	mockEvaluation := &do.EvaluationDO{
		Id:        evaluationID,
		Name:      "评估1",
		CompanyId: 123,
		Price:     originalPrice,
		Duration:  originalDuration,
	}

	mockBreedFilters := []models.EvaluationPetBreedFilter{
		{
			ID:           10,
			EvaluationID: evaluationID,
			PetType:      customerpb.PetType_PET_TYPE_DOG,
			IsAllBreed:   false,
			BreedNames:   []string{"Poodle", "Golden Retriever"},
		},
	}

	mockLocationOverrides := []*models.EvaluationLocationOverride{
		{
			ID:           1,
			EvaluationID: evaluationID,
			BusinessID:   businessID,
			Price:        &overridePrice,
			Duration:     nil, // 不覆盖时长
		},
	}

	// 创建mock对象
	mockEvalRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)
	mockLocationOverrideRepo := mocks.NewMockEvaluationLocationOverrideRepository(ctrl)

	// 设置mock期望
	mockEvalRepo.EXPECT().
		GetEvaluationDetail(gomock.Any(), evaluationID).
		Return(mockEvaluation, nil)

	mockBreedFilterRepo.EXPECT().
		ListByEvaluationID(gomock.Any(), evaluationID).
		Return(mockBreedFilters, nil)

	mockLocationOverrideRepo.EXPECT().
		ListByEvaluationIDs(gomock.Any(), []int64{evaluationID}).
		Return(mockLocationOverrides, nil)

	// 创建测试对象
	evaluationHandler := &evaluationHandler{
		evaluationRepository:       mockEvalRepo,
		breedFilterRepository:      mockBreedFilterRepo,
		locationOverrideRepository: mockLocationOverrideRepo,
	}

	// 执行测试
	result, err := evaluationHandler.GetEvaluationDetail(context.Background(), evaluationID, &businessID)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, evaluationID, result.Id)
	assert.Equal(t, "评估1", result.Name)
	assert.Equal(t, int64(123), result.CompanyId)

	// 验证只有价格被覆盖，时长保持原值
	assert.Equal(t, overridePrice, result.Price)
	assert.Equal(t, originalDuration, result.Duration)

	// 检查品种过滤器
	assert.NotEmpty(t, result.PetBreedFilters)
	assert.Equal(t, customerpb.PetType_PET_TYPE_DOG, result.PetBreedFilters[0].PetType)
	assert.False(t, result.PetBreedFilters[0].IsAllBreed)
	assert.ElementsMatch(t, []string{"Poodle", "Golden Retriever"}, result.PetBreedFilters[0].BreedNames)
}

func TestGetEvaluationDetailWithBusinessIdLocationOverrideRepoError(t *testing.T) {
	// 创建控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 测试数据
	evaluationID := int64(1)
	businessID := int64(100)
	mockEvaluation := &do.EvaluationDO{
		Id:        evaluationID,
		Name:      "评估1",
		CompanyId: 123,
		Price:     50.0,
		Duration:  30,
	}

	mockBreedFilters := []models.EvaluationPetBreedFilter{
		{
			ID:           10,
			EvaluationID: evaluationID,
			PetType:      customerpb.PetType_PET_TYPE_DOG,
			IsAllBreed:   false,
			BreedNames:   []string{"Poodle", "Golden Retriever"},
		},
	}

	expectedErr := errors.New("location override repository error")

	// 创建mock对象
	mockEvalRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)
	mockLocationOverrideRepo := mocks.NewMockEvaluationLocationOverrideRepository(ctrl)

	// 设置mock期望
	mockEvalRepo.EXPECT().
		GetEvaluationDetail(gomock.Any(), evaluationID).
		Return(mockEvaluation, nil)

	mockBreedFilterRepo.EXPECT().
		ListByEvaluationID(gomock.Any(), evaluationID).
		Return(mockBreedFilters, nil)

	mockLocationOverrideRepo.EXPECT().
		ListByEvaluationIDs(gomock.Any(), []int64{evaluationID}).
		Return(nil, expectedErr)

	// 创建测试对象
	evaluationHandler := &evaluationHandler{
		evaluationRepository:       mockEvalRepo,
		breedFilterRepository:      mockBreedFilterRepo,
		locationOverrideRepository: mockLocationOverrideRepo,
	}

	// 执行测试
	result, err := evaluationHandler.GetEvaluationDetail(context.Background(), evaluationID, &businessID)

	// 断言
	assert.Error(t, err)
	assert.Equal(t, expectedErr.Error(), err.Error())
	assert.Nil(t, result)
}

func TestGetEvaluationDetailWithDifferentBusinessIdLocationOverride(t *testing.T) {
	// 创建控制器
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 测试数据
	evaluationID := int64(1)
	businessID := int64(100)
	differentBusinessID := int64(200)
	originalPrice := 50.0
	originalDuration := int32(30)
	overridePrice := 80.0
	overrideDuration := int32(45)

	mockEvaluation := &do.EvaluationDO{
		Id:        evaluationID,
		Name:      "评估1",
		CompanyId: 123,
		Price:     originalPrice,
		Duration:  originalDuration,
	}

	mockBreedFilters := []models.EvaluationPetBreedFilter{
		{
			ID:           10,
			EvaluationID: evaluationID,
			PetType:      customerpb.PetType_PET_TYPE_DOG,
			IsAllBreed:   false,
			BreedNames:   []string{"Poodle", "Golden Retriever"},
		},
	}

	// location override 存在，但是是针对不同的 businessID
	mockLocationOverrides := []*models.EvaluationLocationOverride{
		{
			ID:           1,
			EvaluationID: evaluationID,
			BusinessID:   differentBusinessID, // 不同的 businessID
			Price:        &overridePrice,
			Duration:     &overrideDuration,
		},
	}

	// 创建mock对象
	mockEvalRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)
	mockLocationOverrideRepo := mocks.NewMockEvaluationLocationOverrideRepository(ctrl)

	// 设置mock期望
	mockEvalRepo.EXPECT().
		GetEvaluationDetail(gomock.Any(), evaluationID).
		Return(mockEvaluation, nil)

	mockBreedFilterRepo.EXPECT().
		ListByEvaluationID(gomock.Any(), evaluationID).
		Return(mockBreedFilters, nil)

	mockLocationOverrideRepo.EXPECT().
		ListByEvaluationIDs(gomock.Any(), []int64{evaluationID}).
		Return(mockLocationOverrides, nil)

	// 创建测试对象
	evaluationHandler := &evaluationHandler{
		evaluationRepository:       mockEvalRepo,
		breedFilterRepository:      mockBreedFilterRepo,
		locationOverrideRepository: mockLocationOverrideRepo,
	}

	// 执行测试
	result, err := evaluationHandler.GetEvaluationDetail(context.Background(), evaluationID, &businessID)

	// 断言
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, evaluationID, result.Id)
	assert.Equal(t, "评估1", result.Name)
	assert.Equal(t, int64(123), result.CompanyId)

	// 验证价格和时长保持原值（因为 override 是针对不同的 businessID）
	assert.Equal(t, originalPrice, result.Price)
	assert.Equal(t, originalDuration, result.Duration)

	// 检查品种过滤器
	assert.NotEmpty(t, result.PetBreedFilters)
	assert.Equal(t, customerpb.PetType_PET_TYPE_DOG, result.PetBreedFilters[0].PetType)
	assert.False(t, result.PetBreedFilters[0].IsAllBreed)
	assert.ElementsMatch(t, []string{"Poodle", "Golden Retriever"}, result.PetBreedFilters[0].BreedNames)
}

func TestCreateNewEvaluation(t *testing.T) {
	// Setup
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 创建 SQL mock
	sqlDB, sqlMock, err := sqlmock.New()
	require.NoError(t, err)
	defer func(sqlDB *sql.DB) {
		_ = sqlDB.Close()
	}(sqlDB)

	// 创建 GORM 实例
	dialector := postgres.New(postgres.Config{
		Conn:       sqlDB,
		DriverName: "postgres",
	})
	db, err := gorm.Open(dialector, &gorm.Config{})
	require.NoError(t, err)

	mockEvaluationRepo := mocks.NewMockEvaluationRepository(ctrl)
	mockBreedFilterRepo := mocks.NewMockEvaluationPetBreedFilterRepository(ctrl)

	evaluationHandler := &evaluationHandler{
		db:                    db,
		evaluationRepository:  mockEvaluationRepo,
		breedFilterRepository: mockBreedFilterRepo,
	}

	ctx := context.Background()
	companyID := int64(123)

	t.Run("Success - With Breed Filter", func(t *testing.T) {
		// 设置 SQL 事务期望
		sqlMock.ExpectBegin()
		sqlMock.ExpectCommit() // 成功案例
		// Prepare test data
		petBreedFilters := []do.PetBreedFilter{
			{
				PetType:    customerpb.PetType_PET_TYPE_DOG,
				IsAllBreed: false,
				BreedNames: []string{"Golden Retriever", "Labrador"},
			},
			{
				PetType:    customerpb.PetType_PET_TYPE_CAT,
				IsAllBreed: true,
				BreedNames: []string{},
			},
		}

		evaluationDo := &do.EvaluationDO{
			Name:            "Test Evaluation",
			Description:     "Test Description",
			BreedFilter:     true,
			PetBreedFilters: petBreedFilters,
		}
		expectedID := int64(456)

		// Mock the transaction function to execute the callback with the mock TX
		mockEvaluationRepo.EXPECT().
			AddNewEvaluation(gomock.Any(), gomock.Any(), gomock.Eq(companyID), gomock.Eq(evaluationDo)).
			DoAndReturn(func(ctx context.Context, tx *gorm.DB, companyID int64, evalDo *do.EvaluationDO) (int64, error) {
				return expectedID, nil
			})

		mockBreedFilterRepo.EXPECT().
			BatchCreateFilter(gomock.Any(), gomock.Any(), gomock.Eq(expectedID), gomock.Eq(evaluationDo.PetBreedFilters)).
			Return(nil)

		// Execute
		resultID, err := evaluationHandler.CreateNewEvaluation(ctx, companyID, evaluationDo)

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, expectedID, resultID)
	})

	t.Run("Success - Without Breed Filter", func(t *testing.T) {
		// 设置 SQL 事务期望
		sqlMock.ExpectBegin()
		sqlMock.ExpectCommit() // 成功案例
		// Prepare test data
		evaluationDo := &do.EvaluationDO{
			Name:            "Test Evaluation No Filter",
			Description:     "Test Description No Filter",
			BreedFilter:     false,
			PetBreedFilters: []do.PetBreedFilter{}, // Empty slice since no filters
		}
		expectedID := int64(789)

		// Set expectations
		mockEvaluationRepo.EXPECT().
			AddNewEvaluation(gomock.Any(), gomock.Any(), gomock.Eq(companyID), gomock.Eq(evaluationDo)).
			DoAndReturn(func(ctx context.Context, tx *gorm.DB, companyID int64, evalDo *do.EvaluationDO) (int64, error) {
				return expectedID, nil
			})

		// Execute
		resultID, err := evaluationHandler.CreateNewEvaluation(ctx, companyID, evaluationDo)

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, expectedID, resultID)
	})

	t.Run("Error - AddNewEvaluation Fails", func(t *testing.T) {
		// 设置 SQL 事务期望
		sqlMock.ExpectBegin()
		sqlMock.ExpectRollback() // 失败案例
		// Prepare test data
		petBreedFilters := []do.PetBreedFilter{
			{
				PetType:    customerpb.PetType_PET_TYPE_DOG,
				IsAllBreed: false,
				BreedNames: []string{"Poodle", "Beagle"},
			},
		}

		evaluationDo := &do.EvaluationDO{
			Name:            "Test Evaluation Error",
			Description:     "Test Description Error",
			BreedFilter:     true,
			PetBreedFilters: petBreedFilters,
		}
		expectedErr := errors.New("database error")

		// Set expectations
		mockEvaluationRepo.EXPECT().
			AddNewEvaluation(gomock.Any(), gomock.Any(), gomock.Eq(companyID), gomock.Eq(evaluationDo)).
			DoAndReturn(func(ctx context.Context, tx *gorm.DB, companyID int64, evalDo *do.EvaluationDO) (int64, error) {
				return 0, expectedErr
			})

		// Execute
		resultID, err := evaluationHandler.CreateNewEvaluation(ctx, companyID, evaluationDo)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
		assert.Equal(t, int64(0), resultID)
	})

	t.Run("Error - BatchCreateFilter Fails", func(t *testing.T) {
		// 设置 SQL 事务期望
		sqlMock.ExpectBegin()
		sqlMock.ExpectRollback() // 失败案例
		// Prepare test data
		petBreedFilters := []do.PetBreedFilter{
			{
				PetType:    customerpb.PetType_PET_TYPE_BIRD,
				IsAllBreed: false,
				BreedNames: []string{"Canary", "Parakeet"},
			},
			{
				PetType:    customerpb.PetType_PET_TYPE_OTHER,
				IsAllBreed: true,
				BreedNames: []string{},
			},
		}

		evaluationDo := &do.EvaluationDO{
			Name:            "Test Evaluation Filter Error",
			Description:     "Test Description Filter Error",
			BreedFilter:     true,
			PetBreedFilters: petBreedFilters,
		}
		expectedID := int64(101)
		expectedErr := errors.New("filter creation error")

		// Set expectations
		mockEvaluationRepo.EXPECT().
			AddNewEvaluation(gomock.Any(), gomock.Any(), gomock.Eq(companyID), gomock.Eq(evaluationDo)).
			DoAndReturn(func(ctx context.Context, tx *gorm.DB, companyID int64, evalDo *do.EvaluationDO) (int64, error) {
				return expectedID, nil
			})

		mockBreedFilterRepo.EXPECT().
			BatchCreateFilter(gomock.Any(), gomock.Any(), gomock.Eq(expectedID), gomock.Eq(evaluationDo.PetBreedFilters)).
			Return(expectedErr)

		// Execute
		resultID, err := evaluationHandler.CreateNewEvaluation(ctx, companyID, evaluationDo)

		// Assert
		assert.Error(t, err)
		assert.Equal(t, expectedErr, err)
		assert.Equal(t, int64(0), resultID)
	})
}
