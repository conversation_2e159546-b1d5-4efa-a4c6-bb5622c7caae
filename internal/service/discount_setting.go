package service

import (
	"context"

	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/zlog"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

type DiscountSettingHandler interface {
	GetByCompanyId(ctx context.Context, companyId int64) (*do.DiscountSettingDO, error)
	Upsert(ctx context.Context, discountSettingDO *do.DiscountSettingDO) error
}

type discountSettingHandler struct {
	discountSettingRepo repository.DiscountSettingRepository
}

func (d *discountSettingHandler) GetByCompanyId(ctx context.Context, companyId int64) (*do.DiscountSettingDO, error) {
	id, err := d.discountSettingRepo.GetByCompanyId(ctx, companyId)
	if err != nil {
		zlog.Error(ctx, "failed to get discount setting", zap.Error(err))
		return nil, err
	}
	if id == nil {
		err := d.discountSettingRepo.Upsert(ctx, &do.DiscountSettingDO{
			CompanyID:     companyId,
			ApplyBestOnly: false,
			ApplySequence: []offeringModelsV2.RuleType{offeringModelsV2.RuleType_MULTIPLE_PET, offeringModelsV2.RuleType_MULTIPLE_STAY},
			UpdatedBy:     lo.ToPtr(int64(0)),
		})
		if err != nil {
			return nil, err
		}
	}
	id, err = d.discountSettingRepo.GetByCompanyId(ctx, companyId)
	if err != nil {
		zlog.Error(ctx, "failed to get discount setting", zap.Error(err))
		return nil, err
	}
	return id, nil
}

func (d *discountSettingHandler) Upsert(ctx context.Context, discountSettingDO *do.DiscountSettingDO) error {
	return d.discountSettingRepo.Upsert(ctx, discountSettingDO)
}

func NewDiscountSettingHandler() DiscountSettingHandler {
	return &discountSettingHandler{
		discountSettingRepo: repository.NewDiscountSettingRepository(resource.GetOfferingDB()),
	}
}
