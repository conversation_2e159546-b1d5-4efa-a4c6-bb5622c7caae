package service

import (
	"context"
	"google.golang.org/genproto/googleapis/type/interval"
	"google.golang.org/protobuf/types/known/durationpb"
	"google.golang.org/protobuf/types/known/timestamppb"
	"strconv"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
)

type MessageDeliverHandler interface {
	InsertGroupClassSession(ctx context.Context, tx repository.Transaction,
		existingSession *models.GroupClassSession, session *do.GroupClassSessionDO, location *time.Location) error
	Send(ctx context.Context, messageType eventbuspb.EventType, referenceID string) error
	TaskSendAll(ctx context.Context) error
}

func NewMessageDeliverHandler(
	eventRepository repository.EventRepository,
	producer repository.Producer) MessageDeliverHandler {
	return &messageDeliverHandler{
		eventRepository: eventRepository,
		producer:        producer,
	}
}

type messageDeliverHandler struct {
	eventRepository repository.EventRepository
	producer        repository.Producer
}

func (h *messageDeliverHandler) InsertGroupClassSession(ctx context.Context, tx repository.Transaction,
	existingSession *models.GroupClassSession, session *do.GroupClassSessionDO, location *time.Location) error {
	beforeDurationMin := time.Duration(existingSession.DurationMin) * time.Minute
	event := &models.Event{
		MessageType: eventbuspb.EventType_OFFERING_GROUP_CLASS_SESSION_UPDATED,
		ReferenceID: strconv.FormatInt(session.ID, 10),
		Payload: &eventbuspb.EventData{
			Event: &eventbuspb.EventData_GroupClassSessionEvent{
				GroupClassSessionEvent: &eventbuspb.GroupClassSessionEvent{
					Session: converter.ConvertGroupClassSessionDOToPB(session, location),
					BeforeInterval: &interval.Interval{
						StartTime: timestamppb.New(*existingSession.StartTime),
						EndTime:   timestamppb.New(existingSession.StartTime.Add(beforeDurationMin)),
					},
					BeforeDuration: durationpb.New(beforeDurationMin),
				},
			},
		},
		Status:     models.StatusPending,
		RetryTimes: 0,
	}

	if err := tx.Event().Insert(ctx, event); err != nil {
		zlog.Error(ctx, "failed to InsertEvent", zap.Int64("session_id", session.ID),
			zap.Error(err))
		return err
	}

	return nil
}

func (h *messageDeliverHandler) Send(ctx context.Context, messageType eventbuspb.EventType, referenceID string) error {
	events, err := h.eventRepository.SelectByReference(ctx, messageType, referenceID)
	if err != nil {
		zlog.Error(ctx, "failed to SelectByReference",
			zap.Stringer("message_type", messageType),
			zap.String("reference_id", referenceID),
			zap.Error(err))
		return status.Errorf(codes.Internal, "failed to Select: %v", err)
	}

	for _, event := range events {
		_ = h.send(ctx, event)
	}

	return nil
}

func (h *messageDeliverHandler) send(ctx context.Context, event *models.Event) error {
	if event.Status != models.StatusPending {
		zlog.Warn(ctx, "status is not pending",
			zap.Int64("id", event.ID))
		return nil
	}

	event.RetryTimes++
	if err := h.producer.Publish(ctx, event); err != nil {
		zlog.Error(ctx, "failed to Publish",
			zap.Int64("id", event.ID),
			zap.Error(err))
	} else {
		event.Status = models.StatusDone
	}

	if err := h.eventRepository.Update(ctx, event); err != nil {
		zlog.Error(ctx, "failed to Update",
			zap.Int64("id", event.ID),
			zap.Error(err))
		return status.Errorf(codes.Internal, "failed to UpdateEvent: %v", err)
	}
	return nil
}

func (h *messageDeliverHandler) TaskSendAll(ctx context.Context) error {
	events, err := h.eventRepository.SelectByStatus(ctx, []models.EventStatus{models.StatusPending})
	if err != nil {
		zlog.Error(ctx, "failed to SelectByStatus",
			zap.Error(err))
		return status.Errorf(codes.Internal, "failed to Select: %v", err)
	}

	for _, event := range events {
		_ = h.send(ctx, event)
	}

	return nil
}
