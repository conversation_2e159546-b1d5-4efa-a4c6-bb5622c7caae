package service

import (
	"context"

	"github.com/pkg/errors"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

type LodgingTypeHandler interface {
	CreateLodgingType(ctx context.Context, do *do.LodgingTypeDO) error
	UpdateLodgingType(ctx context.Context, id int64, companyID *int64, updateOpt *do.LodgingTypeUpdateOpt) (*do.LodgingTypeDO, error)
	ListLodgingType(ctx context.Context, companyId int64) ([]*do.LodgingTypeDO, error)
	MGetLodgingType(ctx context.Context, idList []int64) ([]*do.LodgingTypeDO, error)
	DeleteLodgingType(ctx context.Context, id int64, companyId *int64, deletedBy int64) error
	SortLodgingType(ctx context.Context, companyId *int64, updatedBy *int64, ids []int64) error
}

type lodgingTypeHandler struct {
	lodgingTypeRepository repository.LodgingTypeRepository
	lodgingUnitRepository repository.LodgingUnitRepository
}

func (l lodgingTypeHandler) CreateLodgingType(ctx context.Context, do *do.LodgingTypeDO) error {
	sort, err := l.lodgingTypeRepository.GetMaxSort(ctx, do.CompanyID)
	if err != nil {
		return err
	}
	do.Sort = sort + 1
	return l.lodgingTypeRepository.Add(ctx, do)
}

func (l lodgingTypeHandler) UpdateLodgingType(ctx context.Context, id int64, companyID *int64, updateOpt *do.LodgingTypeUpdateOpt) (*do.LodgingTypeDO, error) {
	return l.lodgingTypeRepository.Update(ctx, id, companyID, updateOpt)
}

func (l lodgingTypeHandler) ListLodgingType(ctx context.Context, companyId int64) ([]*do.LodgingTypeDO, error) {
	return l.lodgingTypeRepository.List(ctx, companyId)
}

func (l lodgingTypeHandler) MGetLodgingType(ctx context.Context, idList []int64) ([]*do.LodgingTypeDO, error) {
	return l.lodgingTypeRepository.MGet(ctx, idList)
}

func (l lodgingTypeHandler) DeleteLodgingType(ctx context.Context, id int64, companyId *int64, deletedBy int64) error {
	return l.lodgingTypeRepository.Delete(ctx, id, deletedBy, companyId)
}

func (l lodgingTypeHandler) SortLodgingType(
	ctx context.Context,
	companyId *int64,
	updatedBy *int64,
	ids []int64,
) error {
	if len(ids) == 0 {
		return nil
	}
	var updateOpts = make([]*do.LodgingTypeUpdateByIDOpt, 0, len(ids))
	for i, id := range ids {
		sort := int32(i)
		updateOpts = append(updateOpts, &do.LodgingTypeUpdateByIDOpt{
			ID:        id,
			Sort:      &sort,
			UpdatedBy: updatedBy,
		})
	}

	_, err := l.lodgingTypeRepository.BatchUpdateById(ctx, companyId, updateOpts)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func NewLodgingTypeHandler() LodgingTypeHandler {
	return &lodgingTypeHandler{
		lodgingTypeRepository: repository.NewLodgingTypeRepository(resource.GetOfferingDB()),
		lodgingUnitRepository: repository.NewLodgingUnitRepository(resource.GetOfferingDB()),
	}
}
