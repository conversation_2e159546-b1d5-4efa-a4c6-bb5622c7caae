package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/moego-svc-offering/internal/mocks"
)

func TestLodgingTypeHandler_DeleteLodgingType_Normal(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	cid := int64(1)
	lodgingTypeId := int64(11)
	staffId := int64(101)

	mockLodgingTypeRepository := mocks.NewMockLodgingTypeRepository(ctrl)
	mockLodgingTypeRepository.EXPECT().Delete(context.Background(), lodgingTypeId, staffId, &cid).Return(nil)

	handler := &lodgingTypeHandler{
		lodgingTypeRepository: mockLodgingTypeRepository,
	}

	err := handler.DeleteLodgingType(context.Background(), lodgingTypeId, &cid, staffId)
	assert.NoError(t, err)
}
