package service

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/proto"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/mocks"
)

func mockIsAllStaff(id int64) bool {
	return id%2 == 0
}

func TestServiceHandler_GetAvailAbleStaffList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	serviceIds := []int64{1, 2, 3, 4}
	companyId := int64(101)
	businessId := int64(1001)
	mockServiceBriefList := mockServiceBriefList(serviceIds, func(serviceBrief *do.ServiceBrief) {
		serviceBrief.AvailableForAllStaff = mockIsAllStaff(serviceBrief.ServiceId)
	})
	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetBriefServiceListWithServiceIds(context.TODO(), gomock.InAnyOrder(serviceIds)).Return(mockServiceBriefList, nil)

	mockAllStaffIds := []int64{11, 12, 13, 14, 15, 16}
	mockStaffClient := mocks.NewMockStaffClient(ctrl)
	mockStaffClient.EXPECT().ListAllStaffIds(context.TODO(), companyId, proto.Int64(businessId)).Return(mockAllStaffIds, nil)

	mockStaffAvailabilityDO := []do.ServiceStaffAvailabilityDO{
		{
			StaffId:   11,
			ServiceId: 1,
		},
		{
			StaffId:   12,
			ServiceId: 1,
		},
	}
	mockServiceStaffAvailabilityRepository := mocks.NewMockServiceStaffAvailabilityRepository(ctrl)
	mockServiceStaffAvailabilityRepository.EXPECT().GetAvailableStaffRulesByServiceIDList(context.TODO(), []int64{1, 3}).Return(mockStaffAvailabilityDO, nil)

	handler := &serviceAvailabilityHandler{
		serviceRepository:                  mockServiceRepository,
		serviceStaffAvailabilityRepository: mockServiceStaffAvailabilityRepository,
		staffClient:                        mockStaffClient,
	}

	expect := map[int64]do.ServiceStaffAvailabilityRule{
		1: {
			AvailableForAllStaff: false,
			StaffIds:             []int64{11, 12},
		},
		2: {
			AvailableForAllStaff: true,
			StaffIds:             []int64{11, 12, 13, 14, 15, 16},
		},
		3: {
			AvailableForAllStaff: false,
			StaffIds:             nil,
		},
		4: {
			AvailableForAllStaff: true,
			StaffIds:             []int64{11, 12, 13, 14, 15, 16},
		},
	}

	result, err := handler.BatchGetAvailableStaffIds(context.TODO(), companyId, proto.Int64(businessId), serviceIds)
	assert.NoError(t, err)
	assert.Equal(t, expect, result)
}
