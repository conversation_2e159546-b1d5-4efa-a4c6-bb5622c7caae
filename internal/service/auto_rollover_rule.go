package service

import (
	"context"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

type AutoRolloverRuleHandler interface {
	BatchGetAutoRolloverRules(ctx context.Context, serviceIds []int64) (map[int64]*do.AutoRolloverRule, error)
}

type autoRolloverRuleHandler struct {
	autoRolloverRuleRepository repository.AutoRolloverRuleRepository
}

func (a autoRolloverRuleHandler) BatchGetAutoRolloverRules(ctx context.Context, serviceIds []int64) (map[int64]*do.AutoRolloverRule, error) {
	ruleWithServiceId, err := a.autoRolloverRuleRepository.BatchGetAutoRolloverRules(ctx, serviceIds)
	if err != nil {
		return nil, err
	}

	for _, serviceId := range serviceIds {
		if _, ok := ruleWithServiceId[serviceId]; !ok {
			ruleWithServiceId[serviceId] = do.DefaultAutoRolloverRule
		}
	}

	return ruleWithServiceId, nil
}

func NewAutoRolloverRuleHandler() AutoRolloverRuleHandler {
	return &autoRolloverRuleHandler{autoRolloverRuleRepository: repository.NewAutoRolloverRuleRepository(resource.GetGroomingDB())}
}
