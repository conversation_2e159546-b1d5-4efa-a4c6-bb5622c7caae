package engine

import (
	"sort"

	"github.com/samber/lo"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

type MultiPetRule struct{}

func NewMultiPetRule() *MultiPetRule {
	return &MultiPetRule{}
}

func (r *MultiPetRule) Apply(petDetails []*do.CalculateDO, rule do.PricingRuleRecordDO) {
	if len(petDetails) == 0 || rule.RuleConfiguration == nil {
		return
	}

	if rule.NeedInSameLodging {
		// 按寄养单元分组
		lodgingGroups := make(map[int64][]*do.CalculateDO)
		for _, petDetail := range petDetails {
			if petDetail.LodgingUnitId != nil {
				lodgingGroups[*petDetail.LodgingUnitId] = append(lodgingGroups[*petDetail.LodgingUnitId], petDetail)
			}
		}

		// 对每个寄养单元分别应用规则
		for _, lodgingPets := range lodgingGroups {
			if len(lodgingPets) > 0 {
				r.applyRule(lodgingPets, rule)
			}
		}
	} else {
		// 直接应用规则
		r.applyRule(petDetails, rule)
	}
}

func (r *MultiPetRule) applyRule(petDetails []*do.CalculateDO, rule do.PricingRuleRecordDO) {
	if len(petDetails) == 0 {
		return
	}

	filterPetDetails := lo.Filter(petDetails, func(detail *do.CalculateDO, _ int) bool {
		if (rule.AllBoardingApplicable && detail.ServiceItemType == offeringModelsV1.ServiceItemType_BOARDING) ||
			(rule.AllDaycareApplicable && detail.ServiceItemType == offeringModelsV1.ServiceItemType_DAYCARE) ||
			lo.Contains(rule.SelectedBoardingServices, detail.ServiceId) ||
			lo.Contains(rule.SelectedDaycareServices, detail.ServiceId) {
			return true
		}
		return false
	})
	// Group the details by pet ID
	petToServiceMap := lo.GroupBy(filterPetDetails, func(detail *do.CalculateDO) int64 {
		return detail.PetId
	})
	petIdKey := lo.Keys(petToServiceMap)
	petCount := len(petIdKey)

	// 查找适用的条件组
	applicableGroup := findApplicableConditionGroup(offeringpb.ConditionType_PET_COUNT, rule, float64(petCount))
	if applicableGroup == nil {
		return
	}

	// 应用效果
	if rule.RuleApplyType == offeringpb.RuleApplyType_APPLY_TO_EACH {
		for _, petDetail := range filterPetDetails {
			if lo.Contains(petDetail.UsedPricingRuleIds, *rule.ID) {
				continue
			}
			applyEffect(petDetail, applicableGroup.GetEffect(), *rule.ID)
		}
	} else if rule.RuleApplyType == offeringpb.RuleApplyType_APPLY_TO_ADDITIONAL {
		threshold := 2.0 // 默认为 2
		for _, condition := range applicableGroup.Conditions {
			if condition.Type == offeringpb.ConditionType_PET_COUNT && condition.Value.GetNumberValue() >= 2 {
				threshold = condition.Value.GetNumberValue()
				break
			}
		}

		// 对额外的宠物应用折扣
		if float64(petCount) < threshold {
			return
		}

		// Sort the keys based on the average price of their corresponding values
		sort.Slice(petIdKey, func(i, j int) bool {
			return getAveragePrice(petToServiceMap[petIdKey[i]]).GreaterThan(getAveragePrice(petToServiceMap[petIdKey[j]]))
		})

		for i := int(threshold - 1); i < len(petIdKey); i++ {
			for _, petDetail := range petToServiceMap[petIdKey[i]] {
				if lo.Contains(petDetail.UsedPricingRuleIds, *rule.ID) {
					continue
				}
				applyEffect(petDetail, applicableGroup.GetEffect(), *rule.ID)
			}
		}
	}
}

func (r *MultiPetRule) AppendFormula(petDetails []*do.CalculateDO, rule do.PricingRuleRecordDO, symbol string, formula string) string {
	if len(petDetails) == 0 || rule.RuleConfiguration == nil {
		return formula
	}

	// Group the details by pet ID
	petToServiceMap := lo.GroupBy(petDetails, func(detail *do.CalculateDO) int64 {
		return detail.PetId
	})
	petIdKey := lo.Keys(petToServiceMap)

	// 查找适用的条件组
	petCount := len(petIdKey)
	applicableGroup := findApplicableConditionGroup(offeringpb.ConditionType_PET_COUNT, rule, float64(petCount))
	if applicableGroup == nil {
		return formula
	}

	return assembleFormula(applicableGroup.GetEffect(), symbol, formula)
}
