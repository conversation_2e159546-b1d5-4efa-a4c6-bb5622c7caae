package engine

import (
	"fmt"

	"github.com/shopspring/decimal"

	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

// 应用规则效果
func applyEffect(petDetail *do.CalculateDO, effect *offeringModelsV2.Effect, ruleId int64) {
	originalPrice := petDetail.AdjustedPrice
	adjustedPrice := calculatePrice(originalPrice, effect)
	petDetail.AdjustedPrice = adjustedPrice
	petDetail.IsHitPricingRule = true
	petDetail.UsedPricingRuleIds = append(petDetail.UsedPricingRuleIds, ruleId)
	petDetail.PricingRuleIdPriceMap[ruleId] = decimal.Decimal.Abs(originalPrice.Sub(adjustedPrice))
}

// 查找适用的条件组
func findApplicableConditionGroup(conditionType offeringModelsV2.ConditionType, rule do.PricingRuleRecordDO, value float64) *offeringModelsV2.ConditionGroup {

	var bestGroup *offeringModelsV2.ConditionGroup
	var bestValue float64 = -1

	// 遍历所有条件组
	for i, group := range rule.RuleConfiguration.ConditionGroups {
		// 检查条件组是否适用
		isApplicable := false
		matchedValue := 0.0

		for _, condition := range group.Conditions {
			if condition.Type != conditionType || condition.Value.GetNumberValue() <= 0 {
				continue
			}

			condValue := condition.Value.GetNumberValue()
			isMatch := false

			// 根据操作符评估条件
			switch condition.Operator {
			case utilsV2.Operator_OPERATOR_GE:
				isMatch = value >= condValue
			}

			if isMatch {
				isApplicable = true
				matchedValue = condValue
				break
			}
		}

		// 如果条件适用，且值更高，则更新最佳组
		if isApplicable && matchedValue > bestValue {
			bestGroup = rule.RuleConfiguration.ConditionGroups[i]
			bestValue = matchedValue
		}
	}

	return bestGroup
}

// 计算平均价格
func getAveragePrice(details []*do.CalculateDO) decimal.Decimal {
	if len(details) == 0 {
		return decimal.Zero
	}

	total := decimal.Zero
	for _, detail := range details {
		total = total.Add(detail.AdjustedPrice)
	}

	return total.Div(decimal.NewFromInt(int64(len(details))))
}

// 计算价格
func calculatePrice(originalPrice decimal.Decimal, effect *offeringModelsV2.Effect) decimal.Decimal {
	switch effect.GetType() {
	case offeringModelsV2.EffectType_FIXED_DISCOUNT:
		adjustment := decimal.NewFromFloat(effect.GetValue())
		return decimal.Max(originalPrice.Sub(adjustment), decimal.Zero)
	case offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT:
		percentage := decimal.NewFromFloat(1 - effect.GetValue()/100)
		if effect.RoundingPosition != nil {
			return originalPrice.Mul(percentage).RoundBank(*effect.RoundingPosition)
		}
		return originalPrice.Mul(percentage).RoundBank(2)
	case offeringModelsV2.EffectType_FIXED_INCREASE:
		adjustment := decimal.NewFromFloat(effect.GetValue())
		return originalPrice.Add(adjustment)
	case offeringModelsV2.EffectType_PERCENTAGE_INCREASE:
		percentage := decimal.NewFromFloat(1 + effect.GetValue()/100)
		if effect.RoundingPosition != nil {
			return originalPrice.Mul(percentage).RoundBank(*effect.RoundingPosition)
		}
		return originalPrice.Mul(percentage).RoundBank(2)
	}
	return originalPrice
}

func isDateInRange(date string, startDate string, endDate string) bool {
	return date >= startDate && date <= endDate
}

// 计算规则效果
func assembleFormula(effect *offeringModelsV2.Effect, symbol string, formula string) string {
	switch effect.Type {
	case offeringModelsV2.EffectType_FIXED_DISCOUNT:
		return formula + fmt.Sprintf(" - %s%.2f", symbol, effect.Value)
	case offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT:
		return "(" + formula + ")" + fmt.Sprintf(" * (1 - %.0f%%)", effect.Value)
	}
	return ""
}

func getAdjustedPrice(currentServiceDetails []*do.CalculateDO) decimal.Decimal {
	adjustedPrice := currentServiceDetails[0].AdjustedPrice
	for _, detail := range currentServiceDetails {
		if detail.IsHitPricingRule && detail.AdjustedPrice.LessThan(adjustedPrice) {
			adjustedPrice = detail.AdjustedPrice
		}
	}
	return adjustedPrice
}
