package engine

import (
	"google.golang.org/genproto/googleapis/type/dayofweek"
	"testing"
	"time"

	"github.com/samber/lo"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringModelsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

func TestPeakDateRule_Apply_EmptyPetDetails(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{}
	pricingRule := do.PricingRuleRecordDO{
		ID:       proto.Int64(1),
		RuleType: offeringModelsV2.RuleType_PEAK_DATE,
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - no panic, function should return without changes
	assert.Empty(t, petDetails)
}

func TestPeakDateRule_Apply_NilRuleConfiguration(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:           1,
			ServiceId:       100,
			ServicePrice:    decimal.NewFromFloat(100),
			ServiceDate:     lo.ToPtr("2025-03-01"),
			ServiceItemType: offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:   decimal.NewFromFloat(100),
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:                proto.Int64(1),
		RuleType:          offeringModelsV2.RuleType_PEAK_DATE,
		RuleConfiguration: nil,
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - no changes should be made
	assert.Equal(t, decimal.NewFromFloat(100), petDetails[0].AdjustedPrice)
	assert.False(t, petDetails[0].IsHitPricingRule)
}

func TestPeakDateRule_Apply_NoMatchingDate(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-10"), // Date outside the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:       proto.Int64(1),
		RuleType: offeringModelsV2.RuleType_PEAK_DATE,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - no changes should be made
	assert.Equal(t, decimal.NewFromFloat(100), petDetails[0].AdjustedPrice)
	assert.False(t, petDetails[0].IsHitPricingRule)
}

func TestPeakDateRule_Apply_MatchingDate_ApplyToEach(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	expected1 := decimal.NewFromFloat(110)
	expected2 := decimal.NewFromFloat(160)

	assert.Equal(t, expected1, petDetails[0].AdjustedPrice)
	assert.Equal(t, expected2, petDetails[1].AdjustedPrice)
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.True(t, petDetails[1].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.Equal(t, decimal.NewFromFloat(10), petDetails[0].PricingRuleIdPriceMap[1])
	assert.Equal(t, decimal.NewFromFloat(10), petDetails[1].PricingRuleIdPriceMap[1])
}

func TestPeakDateRule_Apply_MatchingDate_ApplyToAllPets(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:       proto.Int64(1),
		RuleType: offeringModelsV2.RuleType_PEAK_DATE,
		// nolint:staticcheck
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_ALL_PETS,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 20.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - price increase should be distributed evenly
	// Total original price: 100 + 150 = 250
	// Total adjusted price: 250 + 20 = 270
	// Increase per pet: 20 / 2 = 10 each

	// 使用 decimal 直接比较
	expected1 := decimal.NewFromInt(110)
	expected2 := decimal.NewFromInt(160)
	expectedChange := decimal.NewFromInt(10)

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.True(t, petDetails[1].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.True(t, expectedChange.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
	assert.True(t, expectedChange.Equal(petDetails[1].PricingRuleIdPriceMap[1]))
}

func TestPeakDateRule_Apply_EvenDistributionWithRemainder(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 3,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(200),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(200),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:       proto.Int64(1),
		RuleType: offeringModelsV2.RuleType_PEAK_DATE,
		// nolint:staticcheck
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_ALL_PETS,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0, // 增加 10 元，不能被 3 整除
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	// 总价格: 100 + 150 + 200 = 450
	// 总调整后价格: 450 + 10 = 460
	// 每个宠物平均增加: 10 / 3 = 3.33 (保留两位小数)
	// 余数: 10 - 3.33*3 = 0.01，应该加到第一个宠物上
	// 所以第一个宠物增加 3.34，其他宠物增加 3.33

	// 使用 decimal 直接比较
	expected1 := decimal.NewFromFloat(103.34)
	expected2 := decimal.NewFromFloat(153.33)
	expected3 := decimal.NewFromFloat(203.33)
	expectedChange1 := decimal.NewFromFloat(3.34)
	expectedChange2 := decimal.NewFromFloat(3.33)

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, expected3.Equal(petDetails[2].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.True(t, petDetails[1].IsHitPricingRule)
	assert.True(t, petDetails[2].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[2].UsedPricingRuleIds, int64(1))
	assert.True(t, expectedChange1.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
	assert.True(t, expectedChange2.Equal(petDetails[1].PricingRuleIdPriceMap[1]))
	assert.True(t, expectedChange2.Equal(petDetails[2].PricingRuleIdPriceMap[1]))
}

func TestPeakDateRule_Apply_MultipleDays_ApplyToAllPets(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-07"), // First day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-08"), // Second day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-09"), // Third day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-07"), // First day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-08"), // Second day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-09"), // Third day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:       proto.Int64(1),
		RuleType: offeringModelsV2.RuleType_PEAK_DATE,
		// nolint:staticcheck
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_ALL_PETS,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-05-01",
										EndDate:   "2025-05-31",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 100.0, // $100 increase per day
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	// For each day, the $100 increase should be distributed evenly between the two pets
	// So each pet should get a $50 increase per day
	// Pet 1: 100 + 50 = 150 for each day
	// Pet 2: 150 + 50 = 200 for each day

	// Check first pet's prices for all three days
	expectedPet1Price := decimal.NewFromFloat(150) // 100 + 50
	expectedPet2Price := decimal.NewFromFloat(200) // 150 + 50
	expectedChange := decimal.NewFromFloat(50)     // 100 / 2 pets

	// Check all days for pet 1
	for i := 0; i < 3; i++ {
		assert.True(t, expectedPet1Price.Equal(petDetails[i].AdjustedPrice), "Pet 1 day %d price incorrect", i+1)
		assert.True(t, petDetails[i].IsHitPricingRule, "Pet 1 day %d should hit pricing rule", i+1)
		assert.Contains(t, petDetails[i].UsedPricingRuleIds, int64(1), "Pet 1 day %d should use rule ID 1", i+1)
		assert.True(t, expectedChange.Equal(petDetails[i].PricingRuleIdPriceMap[1]), "Pet 1 day %d price change incorrect", i+1)
	}

	// Check all days for pet 2
	for i := 3; i < 6; i++ {
		assert.True(t, expectedPet2Price.Equal(petDetails[i].AdjustedPrice), "Pet 2 day %d price incorrect", i-2)
		assert.True(t, petDetails[i].IsHitPricingRule, "Pet 2 day %d should hit pricing rule", i-2)
		assert.Contains(t, petDetails[i].UsedPricingRuleIds, int64(1), "Pet 2 day %d should use rule ID 1", i-2)
		assert.True(t, expectedChange.Equal(petDetails[i].PricingRuleIdPriceMap[1]), "Pet 2 day %d price change incorrect", i-2)
	}

	// Total price increase should be $100 × 3 days = $300
	// Each pet gets $50 × 3 days = $150 increase in total
}

func TestPeakDateRule_Apply_MatchingDate_ApplyToFirstPet_IsChargePerLodgingFalse(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)),
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(2)),
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:                 proto.Int64(1),
		RuleType:           offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType:      offeringModelsV2.RuleApplyType_APPLY_TO_FIRST_PET,
		IsChargePerLodging: false, // 不按lodging收费，应用到第一只宠物
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 20.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - 新逻辑：IsChargePerLodging=false，全部价格变化应用到第一只宠物
	// Total original price: 100 + 150 = 250
	// Total adjusted price: 250 + 20 = 270
	// 全部20元增加应用到第一只宠物

	// 使用 decimal 直接比较
	expected1 := decimal.NewFromInt(120) // 100 + 20 (全部增加)
	expected2 := decimal.NewFromInt(150) // 150 + 0 (无变化)
	expectedChange1 := decimal.NewFromInt(20)

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.False(t, petDetails[1].IsHitPricingRule) // 第二只宠物不受影响
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.NotContains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.True(t, expectedChange1.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
	assert.Equal(t, 0, len(petDetails[1].PricingRuleIdPriceMap)) // 第二只宠物没有价格变化记录
}

func TestPeakDateRule_Apply_MatchingDate_ApplyToFirstPet_IsChargePerLodgingTrue(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)), // 同一个lodging
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)), // 同一个lodging
		},
		{
			PetId:                 3,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(200),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(200),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(2)), // 不同的lodging
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:                 proto.Int64(1),
		RuleType:           offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType:      offeringModelsV2.RuleApplyType_APPLY_TO_FIRST_PET,
		IsChargePerLodging: true, // 按lodging收费，每个lodging的第一只宠物应用价格变化
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 30.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - 新逻辑：IsChargePerLodging=true，按lodging分组，每个lodging的第一只宠物应用价格变化
	// Lodging 1: Pet1(100) + Pet2(150) = 250, 增加30，应用到Pet1
	// Lodging 2: Pet3(200) = 200, 增加30，应用到Pet3

	// 使用 decimal 直接比较
	expected1 := decimal.NewFromInt(130) // 100 + 30 (lodging1的增加)
	expected2 := decimal.NewFromInt(150) // 150 + 0 (无变化，因为不是lodging1的第一只)
	expected3 := decimal.NewFromInt(230) // 200 + 30 (lodging2的增加)
	expectedChange := decimal.NewFromInt(30)

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, expected3.Equal(petDetails[2].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)  // Pet1受影响
	assert.False(t, petDetails[1].IsHitPricingRule) // Pet2不受影响
	assert.True(t, petDetails[2].IsHitPricingRule)  // Pet3受影响
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.NotContains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[2].UsedPricingRuleIds, int64(1))
	assert.True(t, expectedChange.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
	assert.Equal(t, 0, len(petDetails[1].PricingRuleIdPriceMap)) // Pet2没有价格变化记录
	assert.True(t, expectedChange.Equal(petDetails[2].PricingRuleIdPriceMap[1]))
}

func TestPeakDateRule_Apply_MatchingDate_PercentageDiscount(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_PERCENTAGE_DISCOUNT,
						Value: 20.0, // 20% discount
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	// 100 - 20% = 80

	// 使用 decimal 直接比较
	expectedPrice := decimal.NewFromInt(80)
	expectedDiscount := decimal.NewFromInt(20)

	assert.True(t, expectedPrice.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.True(t, expectedDiscount.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
}

func TestPeakDateRule_Apply_MultipleDates(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-02"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-10"), // Date outside the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	expected1 := decimal.NewFromFloat(110)
	expected2 := decimal.NewFromFloat(110)
	expected3 := decimal.NewFromFloat(100) // No change for date outside range

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, expected3.Equal(petDetails[2].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.True(t, petDetails[1].IsHitPricingRule)
	assert.False(t, petDetails[2].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.NotContains(t, petDetails[2].UsedPricingRuleIds, int64(1))
}

func TestPeakDateRule_Apply_ApplyToFirstPet_IsChargePerLodgingFalse(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)),
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(2)),
		},
		{
			PetId:                 3,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(200),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within the peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(200),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(3)),
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:                 proto.Int64(1),
		RuleType:           offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType:      offeringModelsV2.RuleApplyType_APPLY_TO_FIRST_PET,
		IsChargePerLodging: false, // 不按lodging收费，全部价格变化应用到第一只宠物
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0, // 增加 10 元
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - 新逻辑：IsChargePerLodging=false，全部价格变化应用到第一只宠物
	// 总价格: 100 + 150 + 200 = 450
	// 总调整后价格: 450 + 10 = 460
	// 全部10元增加应用到第一只宠物

	// 使用 decimal 直接比较
	expected1 := decimal.NewFromFloat(110) // 100 + 10 (全部增加)
	expected2 := decimal.NewFromFloat(150) // 150 + 0 (无变化)
	expected3 := decimal.NewFromFloat(200) // 200 + 0 (无变化)
	expectedChange1 := decimal.NewFromFloat(10)

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, expected3.Equal(petDetails[2].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.False(t, petDetails[1].IsHitPricingRule) // 第二只宠物不受影响
	assert.False(t, petDetails[2].IsHitPricingRule) // 第三只宠物不受影响
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.NotContains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	assert.NotContains(t, petDetails[2].UsedPricingRuleIds, int64(1))
	assert.True(t, expectedChange1.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
	assert.Equal(t, 0, len(petDetails[1].PricingRuleIdPriceMap)) // 第二只宠物没有价格变化记录
	assert.Equal(t, 0, len(petDetails[2].PricingRuleIdPriceMap)) // 第三只宠物没有价格变化记录
}

func TestPeakDateRule_Apply_MultipleConditionGroups(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-03-01"), // Date within first peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-04-01"), // Date within second peak date range
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-03-01",
										EndDate:   "2025-03-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 10.0,
					},
				},
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-04-01",
										EndDate:   "2025-04-05",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 20.0,
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	expected1 := decimal.NewFromFloat(110) // First date range: +10
	expected2 := decimal.NewFromFloat(120) // Second date range: +20

	assert.True(t, expected1.Equal(petDetails[0].AdjustedPrice))
	assert.True(t, expected2.Equal(petDetails[1].AdjustedPrice))
	assert.True(t, petDetails[0].IsHitPricingRule)
	assert.True(t, petDetails[1].IsHitPricingRule)
	assert.Contains(t, petDetails[0].UsedPricingRuleIds, int64(1))
	assert.Contains(t, petDetails[1].UsedPricingRuleIds, int64(1))
	expectedChange1 := decimal.NewFromFloat(10)
	expectedChange2 := decimal.NewFromFloat(20)
	assert.True(t, expectedChange1.Equal(petDetails[0].PricingRuleIdPriceMap[1]))
	assert.True(t, expectedChange2.Equal(petDetails[1].PricingRuleIdPriceMap[1]))
}

func TestPeakDateRule_Apply_MultipleDays_ApplyToFirstPet_IsChargePerLodgingTrue(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-07"), // First day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)), // Lodging 1
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-08"), // Second day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)), // Lodging 1
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-09"), // Third day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)), // Lodging 1
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-07"), // First day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)), // Same lodging as pet 1
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-08"), // Second day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)), // Same lodging as pet 1
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-09"), // Third day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
			LodgingUnitId:         lo.ToPtr(int64(1)), // Same lodging as pet 1
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:                 proto.Int64(1),
		RuleType:           offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType:      offeringModelsV2.RuleApplyType_APPLY_TO_FIRST_PET,
		IsChargePerLodging: true, // 按lodging收费，每个lodging的第一只宠物应用价格变化
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-05-01",
										EndDate:   "2025-05-31",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 100.0, // $100 increase per day
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert - 新逻辑：IsChargePerLodging=true，按lodging分组，每个lodging的第一只宠物应用价格变化
	// 由于两只宠物都在同一个lodging(1)，每天的$100增加都应用到第一只宠物(Pet1)
	// Pet 1: 100 + 100 = 200 for each day (gets the increase)
	// Pet 2: 150 + 0 = 150 for each day (no change, not first pet in lodging)

	// Check first pet's prices for all three days (should get the increase)
	expectedPet1Price := decimal.NewFromFloat(200) // 100 + 100
	expectedPet2Price := decimal.NewFromFloat(150) // 150 + 0 (no change)
	expectedChange := decimal.NewFromFloat(100)    // Full $100 increase

	// Check all days for pet 1 (should get the increase)
	for i := 0; i < 3; i++ {
		assert.True(t, expectedPet1Price.Equal(petDetails[i].AdjustedPrice), "Pet 1 day %d price incorrect", i+1)
		assert.True(t, petDetails[i].IsHitPricingRule, "Pet 1 day %d should hit pricing rule", i+1)
		assert.Contains(t, petDetails[i].UsedPricingRuleIds, int64(1), "Pet 1 day %d should use rule ID 1", i+1)
		assert.True(t, expectedChange.Equal(petDetails[i].PricingRuleIdPriceMap[1]), "Pet 1 day %d price change incorrect", i+1)
	}

	// Check all days for pet 2 (should not get the increase)
	for i := 3; i < 6; i++ {
		assert.True(t, expectedPet2Price.Equal(petDetails[i].AdjustedPrice), "Pet 2 day %d price incorrect", i-2)
		assert.False(t, petDetails[i].IsHitPricingRule, "Pet 2 day %d should not hit pricing rule", i-2)
		assert.NotContains(t, petDetails[i].UsedPricingRuleIds, int64(1), "Pet 2 day %d should not use rule ID 1", i-2)
		assert.Equal(t, 0, len(petDetails[i].PricingRuleIdPriceMap), "Pet 2 day %d should have no price change", i-2)
	}

	// Total price increase should be $100 × 3 days = $300, all applied to Pet 1
}

func TestPeakDateRule_Apply_MultipleDays_ApplyToEach(t *testing.T) {
	// Arrange
	rule := PeakDateRule{}
	petDetails := []*do.CalculateDO{
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-07"), // First day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-08"), // Second day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 1,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(100),
			ServiceDate:           lo.ToPtr("2025-05-09"), // Third day of boarding
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(100),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-07"), // First day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-08"), // Second day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
		{
			PetId:                 2,
			ServiceId:             100,
			ServicePrice:          decimal.NewFromFloat(150),
			ServiceDate:           lo.ToPtr("2025-05-09"), // Third day of boarding for second pet
			ServiceItemType:       offeringModelsV1.ServiceItemType_BOARDING,
			AdjustedPrice:         decimal.NewFromFloat(150),
			PricingRuleIdPriceMap: map[int64]decimal.Decimal{},
		},
	}
	pricingRule := do.PricingRuleRecordDO{
		ID:            proto.Int64(1),
		RuleType:      offeringModelsV2.RuleType_PEAK_DATE,
		RuleApplyType: offeringModelsV2.RuleApplyType_APPLY_TO_EACH,
		RuleConfiguration: &offeringModelsV2.PricingRuleConfiguration{
			ConditionGroups: []*offeringModelsV2.ConditionGroup{
				{
					Conditions: []*offeringModelsV2.Condition{
						{
							Type:     offeringModelsV2.ConditionType_DATE_RANGE,
							Operator: utilsV2.Operator_OPERATOR_BETWEEN,
							Value: &offeringModelsV2.GenericValue{
								Value: &offeringModelsV2.GenericValue_DateRange{
									DateRange: &utilsV2.StringDateRange{
										StartDate: "2025-05-01",
										EndDate:   "2025-05-31",
									},
								},
							},
						},
					},
					Effect: &offeringModelsV2.Effect{
						Type:  offeringModelsV2.EffectType_FIXED_INCREASE,
						Value: 100.0, // $100 increase per pet per day
					},
				},
			},
		},
	}

	// Act
	rule.Apply(petDetails, pricingRule)

	// Assert
	// For APPLY_TO_EACH mode, each pet gets the full price increase for each day
	// Pet 1: 100 + 100 = 200 for each day
	// Pet 2: 150 + 100 = 250 for each day

	// Check first pet's prices for all three days
	expectedPet1Price := decimal.NewFromFloat(200) // 100 + 100
	expectedPet2Price := decimal.NewFromFloat(250) // 150 + 100
	expectedChange := decimal.NewFromFloat(100)    // Full $100 increase per pet

	// Check all days for pet 1
	for i := 0; i < 3; i++ {
		assert.True(t, expectedPet1Price.Equal(petDetails[i].AdjustedPrice), "Pet 1 day %d price incorrect", i+1)
		assert.True(t, petDetails[i].IsHitPricingRule, "Pet 1 day %d should hit pricing rule", i+1)
		assert.Contains(t, petDetails[i].UsedPricingRuleIds, int64(1), "Pet 1 day %d should use rule ID 1", i+1)
		assert.True(t, expectedChange.Equal(petDetails[i].PricingRuleIdPriceMap[1]), "Pet 1 day %d price change incorrect", i+1)
	}

	// Check all days for pet 2
	for i := 3; i < 6; i++ {
		assert.True(t, expectedPet2Price.Equal(petDetails[i].AdjustedPrice), "Pet 2 day %d price incorrect", i-2)
		assert.True(t, petDetails[i].IsHitPricingRule, "Pet 2 day %d should hit pricing rule", i-2)
		assert.Contains(t, petDetails[i].UsedPricingRuleIds, int64(1), "Pet 2 day %d should use rule ID 1", i-2)
		assert.True(t, expectedChange.Equal(petDetails[i].PricingRuleIdPriceMap[1]), "Pet 2 day %d price change incorrect", i-2)
	}

	// Total price increase should be $100 × 2 pets × 3 days = $600
	// Each pet gets $100 × 3 days = $300 increase in total
}

func TestConvertGoWeekdayToProto(t *testing.T) {
	tests := []struct {
		name     string
		weekday  time.Weekday
		expected dayofweek.DayOfWeek
	}{
		{"Monday", time.Monday, dayofweek.DayOfWeek_MONDAY},
		{"Tuesday", time.Tuesday, dayofweek.DayOfWeek_TUESDAY},
		{"Wednesday", time.Wednesday, dayofweek.DayOfWeek_WEDNESDAY},
		{"Thursday", time.Thursday, dayofweek.DayOfWeek_THURSDAY},
		{"Friday", time.Friday, dayofweek.DayOfWeek_FRIDAY},
		{"Saturday", time.Saturday, dayofweek.DayOfWeek_SATURDAY},
		{"Sunday", time.Sunday, dayofweek.DayOfWeek_SUNDAY},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertGoWeekdayToProto(tt.weekday)
			if result != tt.expected {
				t.Errorf("convertGoWeekdayToProto(%v) = %v, want %v", tt.weekday, result, tt.expected)
			}
		})
	}
}

func TestMatchDateRange(t *testing.T) {
	dateRangeCondition := &offeringModelsV2.Condition{
		Value: &offeringModelsV2.GenericValue{
			Value: &offeringModelsV2.GenericValue_DateRange{
				DateRange: &utilsV2.StringDateRange{
					StartDate: "2024-01-01",
					EndDate:   "2024-12-31",
				},
			},
		},
	}
	tests := []struct {
		name       string
		condition  *offeringModelsV2.Condition
		targetDate string
		expected   bool
	}{
		{
			name:       "日期在范围内",
			condition:  dateRangeCondition,
			targetDate: "2024-06-15",
			expected:   true,
		},
		{
			name:       "日期在开始边界",
			condition:  dateRangeCondition,
			targetDate: "2024-01-01",
			expected:   true,
		},
		{
			name:       "日期在结束边界",
			condition:  dateRangeCondition,
			targetDate: "2024-12-31",
			expected:   true,
		},
		{
			name:       "日期超出范围-太早",
			condition:  dateRangeCondition,
			targetDate: "2023-12-31",
			expected:   false,
		},
		{
			name:       "日期超出范围-太晚",
			condition:  dateRangeCondition,
			targetDate: "2025-01-01",
			expected:   false,
		},
		{
			name: "DateRange为nil",
			condition: &offeringModelsV2.Condition{
				Value: &offeringModelsV2.GenericValue{
					Value: &offeringModelsV2.GenericValue_RepeatDates{
						RepeatDates: &offeringModelsV2.RepeatDates{},
					},
				},
			},
			targetDate: "2024-06-15",
			expected:   false,
		},
		{
			name:       "无效日期格式",
			condition:  dateRangeCondition,
			targetDate: "invalid-date",
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := matchDateRange(tt.condition, tt.targetDate)
			if result != tt.expected {
				t.Errorf("matchDateRange() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestCheckWeeklyRepeat(t *testing.T) {
	// 2024-01-01是星期一
	startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)

	tests := []struct {
		name       string
		week       *offeringModelsV2.RepeatDates_Week
		targetDate time.Time
		expected   bool
	}{
		{
			name: "每周一重复-匹配",
			week: &offeringModelsV2.RepeatDates_Week{
				IntervalNum: 1,
				DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY},
			},
			targetDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC), // 周一
			expected:   true,
		},
		{
			name: "每周一重复-不匹配星期几",
			week: &offeringModelsV2.RepeatDates_Week{
				IntervalNum: 1,
				DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY},
			},
			targetDate: time.Date(2024, 1, 2, 0, 0, 0, 0, time.UTC), // 周二
			expected:   false,
		},
		{
			name: "每2周一重复-第1周匹配",
			week: &offeringModelsV2.RepeatDates_Week{
				IntervalNum: 2,
				DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY},
			},
			targetDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC), // 第1周周一
			expected:   true,
		},
		{
			name: "每2周一重复-第2周不匹配",
			week: &offeringModelsV2.RepeatDates_Week{
				IntervalNum: 2,
				DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY},
			},
			targetDate: time.Date(2024, 1, 8, 0, 0, 0, 0, time.UTC), // 第2周周一
			expected:   false,
		},
		{
			name: "每2周一重复-第3周匹配",
			week: &offeringModelsV2.RepeatDates_Week{
				IntervalNum: 2,
				DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY},
			},
			targetDate: time.Date(2024, 1, 15, 0, 0, 0, 0, time.UTC), // 第3周周一
			expected:   true,
		},
		{
			name: "多天重复-周三匹配",
			week: &offeringModelsV2.RepeatDates_Week{
				IntervalNum: 1,
				DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY, dayofweek.DayOfWeek_WEDNESDAY, dayofweek.DayOfWeek_FRIDAY},
			},
			targetDate: time.Date(2024, 1, 3, 0, 0, 0, 0, time.UTC), // 周三
			expected:   true,
		},
		{
			name: "无效间隔-0",
			week: &offeringModelsV2.RepeatDates_Week{
				IntervalNum: 0,
				DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY},
			},
			targetDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			expected:   false,
		},
		{
			name: "无效间隔-负数",
			week: &offeringModelsV2.RepeatDates_Week{
				IntervalNum: -1,
				DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY},
			},
			targetDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			expected:   false,
		},
		{
			name: "空的星期几列表",
			week: &offeringModelsV2.RepeatDates_Week{
				IntervalNum: 1,
				DayOfWeeks:  []dayofweek.DayOfWeek{},
			},
			targetDate: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
			expected:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := checkWeeklyRepeat(tt.week, startDate, tt.targetDate)
			if result != tt.expected {
				t.Errorf("checkWeeklyRepeat() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestMatchRepeatDates(t *testing.T) {
	tests := []struct {
		name       string
		condition  *offeringModelsV2.Condition
		targetDate string
		expected   bool
	}{
		{
			name: "RepeatDates为nil",
			condition: &offeringModelsV2.Condition{
				Value: &offeringModelsV2.GenericValue{
					Value: &offeringModelsV2.GenericValue_DateRange{
						DateRange: &utilsV2.StringDateRange{},
					},
				},
			},
			targetDate: "2024-01-01",
			expected:   false,
		},
		{
			name: "无效目标日期格式",
			condition: &offeringModelsV2.Condition{
				Value: &offeringModelsV2.GenericValue{
					Value: &offeringModelsV2.GenericValue_RepeatDates{
						RepeatDates: &offeringModelsV2.RepeatDates{
							DateRange: &utilsV2.StringDateRange{
								StartDate: "2024-01-01",
								EndDate:   "2024-12-31",
							},
						},
					},
				},
			},
			targetDate: "invalid-date",
			expected:   false,
		},
		{
			name: "无效开始日期格式",
			condition: &offeringModelsV2.Condition{
				Value: &offeringModelsV2.GenericValue{
					Value: &offeringModelsV2.GenericValue_RepeatDates{
						RepeatDates: &offeringModelsV2.RepeatDates{
							DateRange: &utilsV2.StringDateRange{
								StartDate: "invalid-date",
								EndDate:   "2024-12-31",
							},
						},
					},
				},
			},
			targetDate: "2024-06-15",
			expected:   false,
		},
		{
			name: "无效结束日期格式",
			condition: &offeringModelsV2.Condition{
				Value: &offeringModelsV2.GenericValue{
					Value: &offeringModelsV2.GenericValue_RepeatDates{
						RepeatDates: &offeringModelsV2.RepeatDates{
							DateRange: &utilsV2.StringDateRange{
								StartDate: "2024-01-01",
								EndDate:   "invalid-date",
							},
						},
					},
				},
			},
			targetDate: "2024-06-15",
			expected:   false,
		},
		{
			name: "日期超出范围",
			condition: &offeringModelsV2.Condition{
				Value: &offeringModelsV2.GenericValue{
					Value: &offeringModelsV2.GenericValue_RepeatDates{
						RepeatDates: &offeringModelsV2.RepeatDates{
							DateRange: &utilsV2.StringDateRange{
								StartDate: "2024-06-01",
								EndDate:   "2024-06-30",
							},
						},
					},
				},
			},
			targetDate: "2024-05-31",
			expected:   false,
		},
		{
			name: "没有周重复规则-日期在范围内",
			condition: &offeringModelsV2.Condition{
				Value: &offeringModelsV2.GenericValue{
					Value: &offeringModelsV2.GenericValue_RepeatDates{
						RepeatDates: &offeringModelsV2.RepeatDates{
							DateRange: &utilsV2.StringDateRange{
								StartDate: "2024-01-01",
								EndDate:   "2024-12-31",
							},
						},
					},
				},
			},
			targetDate: "2024-06-15",
			expected:   true,
		},
		{
			name: "每周一重复-匹配",
			condition: &offeringModelsV2.Condition{
				Value: &offeringModelsV2.GenericValue{
					Value: &offeringModelsV2.GenericValue_RepeatDates{
						RepeatDates: &offeringModelsV2.RepeatDates{
							DateRange: &utilsV2.StringDateRange{
								StartDate: "2024-01-01", // 2024-01-01是周一
								EndDate:   "2024-12-31",
							},
							Interval: &offeringModelsV2.RepeatDates_Week_{
								Week: &offeringModelsV2.RepeatDates_Week{
									IntervalNum: 1,
									DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY},
								},
							},
						},
					},
				},
			},
			targetDate: "2024-01-01", // 周一
			expected:   true,
		},
		{
			name: "每周一重复-不匹配",
			condition: &offeringModelsV2.Condition{
				Value: &offeringModelsV2.GenericValue{
					Value: &offeringModelsV2.GenericValue_RepeatDates{
						RepeatDates: &offeringModelsV2.RepeatDates{
							DateRange: &utilsV2.StringDateRange{
								StartDate: "2024-01-01", // 2024-01-01是周一
								EndDate:   "2024-12-31",
							},
							Interval: &offeringModelsV2.RepeatDates_Week_{
								Week: &offeringModelsV2.RepeatDates_Week{
									IntervalNum: 1,
									DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY},
								},
							},
						},
					},
				},
			},
			targetDate: "2024-01-02", // 周二
			expected:   false,
		},
		{
			name: "复杂重复规则-每2周的周三和周五",
			condition: &offeringModelsV2.Condition{
				Value: &offeringModelsV2.GenericValue{
					Value: &offeringModelsV2.GenericValue_RepeatDates{
						RepeatDates: &offeringModelsV2.RepeatDates{
							DateRange: &utilsV2.StringDateRange{
								StartDate: "2024-01-01", // 2024-01-01是周一
								EndDate:   "2024-12-31",
							},
							Interval: &offeringModelsV2.RepeatDates_Week_{
								Week: &offeringModelsV2.RepeatDates_Week{
									IntervalNum: 2,
									DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_WEDNESDAY, dayofweek.DayOfWeek_FRIDAY},
								},
							},
						},
					},
				},
			},
			targetDate: "2024-01-03", // 第1周周三，应该匹配
			expected:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := matchRepeatDates(tt.condition, tt.targetDate)
			if result != tt.expected {
				t.Errorf("matchRepeatDates() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// Benchmark测试
func BenchmarkMatchRepeatDates(b *testing.B) {
	condition := &offeringModelsV2.Condition{
		Value: &offeringModelsV2.GenericValue{
			Value: &offeringModelsV2.GenericValue_RepeatDates{
				RepeatDates: &offeringModelsV2.RepeatDates{
					DateRange: &utilsV2.StringDateRange{
						StartDate: "2024-01-01",
						EndDate:   "2024-12-31",
					},
					Interval: &offeringModelsV2.RepeatDates_Week_{
						Week: &offeringModelsV2.RepeatDates_Week{
							IntervalNum: 2,
							DayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY, dayofweek.DayOfWeek_WEDNESDAY, dayofweek.DayOfWeek_FRIDAY},
						},
					},
				},
			},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		matchRepeatDates(condition, "2024-06-15")
	}
}

// 表格驱动测试覆盖各种周重复场景
func TestWeeklyRepeatScenarios(t *testing.T) {
	startDate := time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC) // 2024-01-01 周一

	scenarios := []struct {
		name        string
		intervalNum int32
		dayOfWeeks  []dayofweek.DayOfWeek
		testCases   []struct {
			date     string
			expected bool
		}
	}{
		{
			name:        "每周工作日",
			intervalNum: 1,
			dayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_MONDAY, dayofweek.DayOfWeek_TUESDAY, dayofweek.DayOfWeek_WEDNESDAY, dayofweek.DayOfWeek_THURSDAY, dayofweek.DayOfWeek_FRIDAY},
			testCases: []struct {
				date     string
				expected bool
			}{
				{"2024-01-01", true},  // 周一
				{"2024-01-02", true},  // 周二
				{"2024-01-06", false}, // 周六
				{"2024-01-07", false}, // 周日
				{"2024-01-08", true},  // 下周一
			},
		},
		{
			name:        "每3周的周末",
			intervalNum: 3,
			dayOfWeeks:  []dayofweek.DayOfWeek{dayofweek.DayOfWeek_SATURDAY, dayofweek.DayOfWeek_SUNDAY},
			testCases: []struct {
				date     string
				expected bool
			}{
				{"2024-01-06", true},  // 第1周周六
				{"2024-01-07", true},  // 第1周周日
				{"2024-01-13", false}, // 第2周周六
				{"2024-01-20", false}, // 第3周周六
				{"2024-01-27", true},  // 第4周周六 (第2个周期开始)
			},
		},
	}

	for _, scenario := range scenarios {
		t.Run(scenario.name, func(t *testing.T) {
			week := &offeringModelsV2.RepeatDates_Week{
				IntervalNum: scenario.intervalNum,
				DayOfWeeks:  scenario.dayOfWeeks,
			}

			for _, testCase := range scenario.testCases {
				targetDate, _ := time.Parse(time.DateOnly, testCase.date)
				result := checkWeeklyRepeat(week, startDate, targetDate)
				if result != testCase.expected {
					t.Errorf("日期 %s: got %v, want %v", testCase.date, result, testCase.expected)
				}
			}
		})
	}
}
