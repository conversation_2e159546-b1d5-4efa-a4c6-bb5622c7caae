package engine

import (
	"github.com/samber/lo"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
)

type MultiStayRule struct{}

func NewMultiStayRule() *MultiStayRule {
	return &MultiStayRule{}
}

func (r *MultiStayRule) Apply(petDetails []*do.CalculateDO, rule do.PricingRuleRecordDO) {
	if len(petDetails) == 0 || rule.RuleConfiguration == nil {
		return
	}

	// Group the details by pet ID
	petToServiceMap := lo.GroupBy(petDetails, func(detail *do.CalculateDO) int64 {
		return detail.PetId
	})

	for _, petDetailsValue := range petToServiceMap {
		// 检查住宿天数
		stayLength := 0
		for _, petDetail := range petDetailsValue {
			if petDetail.ServiceDate != nil {
				stayLength++
			}
		}

		if stayLength <= 1 {
			continue // 只有一天或没有日期，不应用多住宿规则
		}

		// 查找适用的条件组
		applicableGroup := findApplicableConditionGroup(offeringpb.ConditionType_STAY_LENGTH, rule, float64(stayLength))
		if applicableGroup == nil {
			return
		}

		// 应用效果
		for _, petDetail := range petDetailsValue {
			applyEffect(petDetail, applicableGroup.GetEffect(), *rule.ID)
		}
	}
}

func (r *MultiStayRule) AppendFormula(petDetails []*do.CalculateDO, rule do.PricingRuleRecordDO, symbol string, formula string) string {
	if len(petDetails) == 0 || rule.RuleConfiguration == nil {
		return formula
	}

	// Group the details by pet ID
	petToServiceMap := lo.GroupBy(petDetails, func(detail *do.CalculateDO) int64 {
		return detail.PetId
	})

	for _, petDetailsValue := range petToServiceMap {
		// 检查住宿天数
		stayLength := 0
		for _, petDetail := range petDetailsValue {
			if petDetail.ServiceDate != nil {
				stayLength++
			}
		}

		if stayLength <= 1 {
			continue // 只有一天或没有日期，不应用多住宿规则
		}

		// 查找适用的条件组
		applicableGroup := findApplicableConditionGroup(offeringpb.ConditionType_STAY_LENGTH, rule, float64(stayLength))
		if applicableGroup == nil {
			return formula
		}

		return assembleFormula(applicableGroup.GetEffect(), symbol, formula)
	}
	return formula
}
