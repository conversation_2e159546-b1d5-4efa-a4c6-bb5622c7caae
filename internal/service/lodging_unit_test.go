package service

import (
	"context"
	"fmt"
	"testing"

	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/MoeGolibrary/go-lib/merror"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/mocks"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func TestLodgingUnitHandler_batchGetDedicatedLodgingByEvaluationService(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockEvaluationRepository := mocks.NewMockEvaluationRepository(ctrl)
	mockEvaluationRepository.EXPECT().GetEvaluationListByIds(context.TODO(), []int64{1, 2, 3}).Return([]*do.EvaluationDO{
		{
			Id:                   1,
			LodgingFilter:        false,
			CustomizedLodgingIds: nil,
		},
		{
			Id:                   2,
			LodgingFilter:        true,
			CustomizedLodgingIds: []int64{1, 2},
		},
	}, nil)
	handler := &lodgingUnitHandler{
		evaluationRepository: mockEvaluationRepository,
	}

	expect := map[int64][]int64{
		2: {1, 2},
		3: make([]int64, 0),
	}

	result, err := handler.batchGetDedicatedLodgingByEvaluationService(context.TODO(), []int64{1, 2, 3})
	assert.NoError(t, err)
	assert.Equal(t, expect, result)
}

func Test_lodgingUnitHandler_batchGetDedicatedLodgingByService(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetBriefServiceListWithServiceIds(context.TODO(), []int64{1, 2, 3}).Return([]do.ServiceBrief{
		{
			ServiceId:          1,
			LodgingFilter:      false,
			CustomizedLodgings: nil,
		},
		{
			ServiceId:          2,
			LodgingFilter:      true,
			CustomizedLodgings: []int64{1, 2},
		},
	}, nil)
	handler := &lodgingUnitHandler{
		serviceRepository: mockServiceRepository,
	}

	expect := map[int64][]int64{
		2: {1, 2},
		3: make([]int64, 0),
	}

	result, err := handler.batchGetDedicatedLodgingByService(context.TODO(), []int64{1, 2, 3})
	assert.NoError(t, err)
	assert.Equal(t, expect, result)
}

func Test_lodgingUnitHandler_whereOptDoToModel(t *testing.T) {
	ctrl := gomock.NewController(t)
	mockServiceRepository := mocks.NewMockServiceRepository(ctrl)
	mockServiceRepository.EXPECT().GetBriefServiceListWithServiceIds(context.TODO(), []int64{1}).Return(nil, assert.AnError)
	handler := &lodgingUnitHandler{
		serviceRepository: mockServiceRepository,
	}
	_, err := handler.ListLodgingUnit(context.TODO(), 0, &do.LodgingUnitWhereOpt{
		ServiceID: lo.ToPtr(int64(1)),
	})
	assert.Error(t, err)

	mockEvaluationRepository := mocks.NewMockEvaluationRepository(ctrl)
	mockEvaluationRepository.EXPECT().GetEvaluationListByIds(context.TODO(), []int64{1}).Return(nil, assert.AnError)
	handler = &lodgingUnitHandler{
		evaluationRepository: mockEvaluationRepository,
	}
	_, err = handler.whereOptDoToModel(context.TODO(), 0, &do.LodgingUnitWhereOpt{
		EvaluationServiceID: lo.ToPtr(int64(1)),
	})
	assert.Error(t, err)

	var result *models.LodgingUnitWhereOpt
	mockServiceRepository.EXPECT().GetBriefServiceListWithServiceIds(context.TODO(), []int64{1}).Return([]do.ServiceBrief{
		{
			ServiceId:          1,
			LodgingFilter:      true,
			CustomizedLodgings: []int64{1, 2, 3},
		},
	}, nil)
	mockEvaluationRepository.EXPECT().GetEvaluationListByIds(context.TODO(), []int64{1}).Return([]*do.EvaluationDO{
		{
			Id:                   1,
			LodgingFilter:        true,
			CustomizedLodgingIds: []int64{1, 2, 4},
		},
	}, nil)
	handler = &lodgingUnitHandler{
		serviceRepository:    mockServiceRepository,
		evaluationRepository: mockEvaluationRepository,
	}
	result, err = handler.whereOptDoToModel(context.TODO(), 0, &do.LodgingUnitWhereOpt{
		LodgingTypeIDIn:     []int64{2, 3, 4, 5},
		EvaluationServiceID: lo.ToPtr(int64(1)),
		ServiceID:           lo.ToPtr(int64(1)),
	})
	assert.NoError(t, err)
	assert.Equal(t, []int64{2}, *result.LodgingTypeIDIn)
}

func TestLodgingUnitHandler_BatchCreateLodgingUnit_TypeNotExist(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockLodgingTypeRepository := mocks.NewMockLodgingTypeRepository(ctrl)
	mockLodgingUnitRepository := mocks.NewMockLodgingUnitRepository(ctrl)
	mockLodgingTypeRepository.EXPECT().MGet(context.Background(), []int64{1, 1, 2}).Return([]*do.LodgingTypeDO{
		{
			ID:   1,
			Name: "test1",
		},
	}, nil)
	mockLodgingUnitRepository.EXPECT().GetMaxSort(gomock.Any(), gomock.Any()).
		DoAndReturn(func(_ context.Context, opt *do.LodgingUnitWhereOpt) (int32, error) {
			if *opt.BusinessID == 1000 && *opt.LodgingTypeID == 1 {
				return int32(5), nil
			}
			if *opt.BusinessID == 1000 && *opt.LodgingTypeID == 2 {
				return int32(0), nil
			}
			return 0, fmt.Errorf("unexpected parameters")
		}).AnyTimes()

	handler := &lodgingUnitHandler{
		lodgingTypeRepository: mockLodgingTypeRepository,
		lodgingUnitRepository: mockLodgingUnitRepository,
	}

	_, err := handler.BatchCreateLodgingUnit(context.Background(), []*do.LodgingUnitDO{
		{
			Name:          "test unit 1",
			LodgingTypeID: 1,
			CompanyID:     100,
			BusinessID:    1000,
		},
		{
			Name:          "test unit 2",
			LodgingTypeID: 1,
			CompanyID:     100,
			BusinessID:    1000,
		},
		{
			Name:          "test unit 3",
			LodgingTypeID: 2,
			CompanyID:     100,
			BusinessID:    1000,
		},
	})

	var expectError *merror.BizError
	var expectErrorCode = errorspb.Code_CODE_LODGING_TYPE_NOT_FOUND

	assert.ErrorAs(t, err, &expectError)
	assert.Equal(t, expectErrorCode, expectError.ErrCode())
}

func TestLodgingUnitHandler_DeleteLodgingUnit_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockLodgingUnitRepository := mocks.NewMockLodgingUnitRepository(ctrl)
	mockLodgingUnitRepository.EXPECT().BatchDelete(context.TODO(), []int64{1, 2, 3}, lo.ToPtr(int64(1)), lo.ToPtr(int64(100))).Return(nil)

	handler := &lodgingUnitHandler{
		lodgingUnitRepository: mockLodgingUnitRepository,
	}

	err := handler.BatchDeleteLodgingUnit(context.TODO(), []int64{1, 2, 3}, lo.ToPtr(int64(1)), lo.ToPtr(int64(100)))
	assert.NoError(t, err)
}
