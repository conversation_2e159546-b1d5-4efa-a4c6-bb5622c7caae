package service

import (
	"context"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

type EvaluationHandler interface {
	CreateNewEvaluation(ctx context.Context, companyId int64, evaluationDo *do.EvaluationDO) (int64, error)
	UpdateEvaluation(ctx context.Context, companyId int64, evaluationId int64, evaluationDo *do.EvaluationDO) error
	GetEvaluationList(ctx context.Context, companyId int64) ([]*do.EvaluationDO, error)
	GetEvaluationListByFilter(ctx context.Context, filter *do.EvaluationModelFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []*do.EvaluationDO, error)
	GetEvaluationDetail(ctx context.Context, evaluationId int64, businessId *int64) (*do.EvaluationDO, error)
	GetApplicableEvaluationList(ctx context.Context, companyId int64, filter *do.EvaluationFilter) ([]*do.EvaluationDO, error)
	GetEvaluationListByIds(ctx context.Context, evaluationIds []int64, businessId *int64) ([]*do.EvaluationDO, error)
	DeleteEvaluation(ctx context.Context, companyId int64, evaluationId int64) error
}

type evaluationHandler struct {
	db                         *gorm.DB
	evaluationRepository       repository.EvaluationRepository
	breedFilterRepository      repository.EvaluationPetBreedFilterRepository
	locationOverrideRepository repository.EvaluationLocationOverrideRepository
	serviceRepository          repository.ServiceRepository
}

func (e evaluationHandler) GetEvaluationListByIds(ctx context.Context, evaluationIds []int64, businessId *int64) ([]*do.EvaluationDO, error) {
	evaluations, err := e.evaluationRepository.GetEvaluationListByIds(ctx, evaluationIds)
	if err != nil {
		return nil, err
	}
	if len(evaluations) == 0 {
		return evaluations, nil
	}
	if businessId != nil {
		err = e.applyLocationOverrideIfNeeded(ctx, *businessId, evaluations)
		if err != nil {
			return nil, err
		}
	}
	return evaluations, err
}

func (e evaluationHandler) GetEvaluationListByFilter(ctx context.Context, filter *do.EvaluationModelFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []*do.EvaluationDO, error) {
	total, evaluations, err := e.evaluationRepository.GetEvaluationListByFilter(ctx, filter, paginationRequest)
	if err != nil {
		return 0, nil, err
	}
	if filter.BusinessId != nil {
		err = e.applyLocationOverrideIfNeeded(ctx, *filter.BusinessId, evaluations)
		if err != nil {
			return 0, nil, err
		}
	}
	return total, evaluations, nil
}

func (e evaluationHandler) GetApplicableEvaluationList(ctx context.Context, companyId int64, filter *do.EvaluationFilter) ([]*do.EvaluationDO, error) {
	evaluationList, err := e.GetEvaluationList(ctx, companyId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	if len(evaluationList) == 0 {
		return evaluationList, nil
	}

	if filter == nil || !filter.IncludeInactive {
		evaluationList = lo.Filter(evaluationList, func(e *do.EvaluationDO, _ int) bool {
			return e.IsActive
		})
	}
	if len(evaluationList) == 0 {
		return evaluationList, nil
	}

	if filter != nil {
		if filter.BusinessId != nil {
			evaluationList = lo.Filter(evaluationList, func(e *do.EvaluationDO, _ int) bool {
				if e.AvailableForAllBusiness {
					return true
				}
				if lo.Contains(e.AvailableBusinessIds, *filter.BusinessId) {
					return true
				}
				return false
			})
			if len(evaluationList) == 0 {
				return evaluationList, nil
			}
			err = e.applyLocationOverrideIfNeeded(ctx, *filter.BusinessId, evaluationList)
			if err != nil {
				return nil, err
			}
		}

		if filter.ServiceItemType != nil {
			evaluationList = lo.Filter(evaluationList, func(e *do.EvaluationDO, _ int) bool {
				return lo.Contains(e.ServiceItemTypes, *filter.ServiceItemType)
			})
			if len(evaluationList) == 0 {
				return evaluationList, nil
			}
		}

		if filter.FilterByPet != nil {
			evaluationList, err = e.evaluationFilterByPet(ctx, filter.FilterByPet, evaluationList)
			if err != nil {
				return nil, err
			}
		}
	}

	return evaluationList, nil
}

func (e evaluationHandler) evaluationFilterByPet(
	ctx context.Context, filterByPet *offeringpb.ServiceFilterByPet, evaluationList []*do.EvaluationDO) ([]*do.EvaluationDO, error) {
	if filterByPet == nil {
		return evaluationList, nil
	}
	evaluationIds := lo.Map(evaluationList, func(e *do.EvaluationDO, _ int) int64 {
		return e.Id
	})
	allBreedFilters, err := e.breedFilterRepository.ListByEvaluationIDs(ctx, evaluationIds)
	if err != nil {
		return nil, err
	}
	evaluationIdToBreedFilters := lo.GroupBy(allBreedFilters, func(filter models.EvaluationPetBreedFilter) int64 {
		return filter.EvaluationID
	})
	filteredEvaluationList := lo.Filter(evaluationList, func(e *do.EvaluationDO, _ int) bool {
		enableBreedFilter, breedFilters := e.BreedFilter, evaluationIdToBreedFilters[e.Id]
		if !enableBreedFilter || len(breedFilters) == 0 {
			return true
		}
		for _, breedFilter := range breedFilters {
			if breedFilter.PetType != filterByPet.PetType {
				continue
			}
			if breedFilter.IsAllBreed {
				return true
			}
			for _, breed := range breedFilter.BreedNames {
				if breed == *filterByPet.PetBreed {
					return true
				}
			}
		}
		return false
	})
	return filteredEvaluationList, nil
}

func (e evaluationHandler) applyLocationOverrideIfNeeded(ctx context.Context, businessId int64, evaluationList []*do.EvaluationDO) error {
	evaluationIds := lo.Map(evaluationList, func(e *do.EvaluationDO, _ int) int64 {
		return e.Id
	})
	locationOverrides, err := e.locationOverrideRepository.ListByEvaluationIDs(ctx, evaluationIds)
	locationOverrides = lo.Filter(locationOverrides, func(override *models.EvaluationLocationOverride, _ int) bool {
		return override.BusinessID == businessId
	})
	if err != nil {
		return err
	}
	evaluationIdToLocationOverride := lo.KeyBy(locationOverrides, func(override *models.EvaluationLocationOverride) int64 {
		return override.EvaluationID
	})
	for _, evaluation := range evaluationList {
		override := evaluationIdToLocationOverride[evaluation.Id]
		if override != nil {
			if override.Price != nil {
				evaluation.Price = *override.Price
			}
			if override.Duration != nil {
				evaluation.Duration = *override.Duration
			}
		}
	}
	return nil
}

func (e evaluationHandler) CreateNewEvaluation(ctx context.Context, companyId int64, evaluationDo *do.EvaluationDO) (int64, error) {
	var evaluationID int64
	var err error
	err = e.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		evaluationID, err = e.evaluationRepository.AddNewEvaluation(ctx, tx, companyId, evaluationDo)
		if err != nil {
			return err
		}
		if evaluationDo.BreedFilter {
			err = e.breedFilterRepository.BatchCreateFilter(ctx, tx, evaluationID, evaluationDo.PetBreedFilters)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return 0, err
	}
	return evaluationID, nil
}

func (e evaluationHandler) UpdateEvaluation(ctx context.Context, companyId int64, evaluationId int64, evaluationDo *do.EvaluationDO) error {
	err := e.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err := e.evaluationRepository.UpdateEvaluation(ctx, tx, companyId, evaluationId, evaluationDo)
		if err != nil {
			return err
		}
		if evaluationDo.BreedFilter {
			err = e.breedFilterRepository.BatchUpdateFilter(ctx, tx, evaluationId, evaluationDo.PetBreedFilters)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func (e evaluationHandler) GetEvaluationList(ctx context.Context, companyId int64) ([]*do.EvaluationDO, error) {
	evaluationList, err := e.evaluationRepository.GetEvaluationList(ctx, companyId)
	if err != nil {
		return nil, err
	}
	evaluationIDs := lo.Map(evaluationList, func(e *do.EvaluationDO, _ int) int64 {
		return e.Id
	})
	allBreedFilters, err := e.breedFilterRepository.ListByEvaluationIDs(ctx, evaluationIDs)
	if err != nil {
		return nil, err
	}
	evaluationIdToBreedFilters := lo.GroupBy(allBreedFilters, func(filter models.EvaluationPetBreedFilter) int64 {
		return filter.EvaluationID
	})
	for _, evaluationDO := range evaluationList {
		breedFilters := evaluationIdToBreedFilters[evaluationDO.Id]
		evaluationDO.PetBreedFilters = converter.ConvertPetBreedFilterModelToDO(breedFilters)
	}
	return evaluationList, nil
}

func (e evaluationHandler) GetEvaluationDetail(ctx context.Context, evaluationId int64, businessId *int64) (*do.EvaluationDO, error) {
	evaluationDetail, err := e.evaluationRepository.GetEvaluationDetail(ctx, evaluationId)
	if err != nil {
		return nil, err
	}
	breedFilters, err := e.breedFilterRepository.ListByEvaluationID(ctx, evaluationId)
	if err != nil {
		return nil, err
	}
	evaluationDetail.PetBreedFilters = converter.ConvertPetBreedFilterModelToDO(breedFilters)
	if businessId != nil {
		err = e.applyLocationOverrideIfNeeded(ctx, *businessId, []*do.EvaluationDO{evaluationDetail})
		if err != nil {
			return nil, err
		}
	}
	return evaluationDetail, nil
}

func (e evaluationHandler) DeleteEvaluation(ctx context.Context, companyId int64, evaluationId int64) error {
	return e.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 1. 更新所有关联的 service，将 evaluationId 设为 0, 并将 required evaluation 设为 false
		serviceUpdateOpt := &do.ServiceUpdateOpt{
			IsEvaluationRequired:      proto.Bool(false),
			IsEvaluationRequiredForOb: proto.Bool(false),
			EvaluationID:              proto.Int64(0),
		}
		if err := e.serviceRepository.UpdateAllServiceByCompanyId(ctx, companyId, serviceUpdateOpt); err != nil {
			return errors.WithStack(err)
		}

		// 2. 软删除 evaluation
		if err := e.evaluationRepository.DeleteEvaluation(ctx, tx, companyId, evaluationId); err != nil {
			return errors.WithStack(err)
		}

		return nil
	})
}

func NewEvaluationHandler() EvaluationHandler {
	return &evaluationHandler{
		db:                         resource.GetOfferingDB(),
		evaluationRepository:       repository.NewEvaluationRepository(resource.GetOfferingDB()),
		breedFilterRepository:      repository.NewEvaluationPetBreedFilterRepository(resource.GetOfferingDB()),
		locationOverrideRepository: repository.NewEvaluationLocationOverrideRepository(resource.GetOfferingDB()),
		serviceRepository:          repository.NewServiceRepository(resource.GetGroomingDB()),
	}
}
