package service

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

// MockCustomizeCareTypeRepository is a mock implementation of CustomizeCareTypeRepository
type MockCustomizeCareTypeRepository struct {
	mock.Mock
}

func (m *MockCustomizeCareTypeRepository) List(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, filter *offeringsvcpb.WhiteListFilter) ([]*do.CustomizeCareTypeDO, error) {
	args := m.Called(ctx, whereOpt, filter)
	return args.Get(0).([]*do.CustomizeCareTypeDO), args.Error(1)
}

func (m *MockCustomizeCareTypeRepository) Update(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, updateOpt *models.CustomizeCareTypeUpdateOpt) error {
	args := m.Called(ctx, whereOpt, updateOpt)
	return args.Error(0)
}

func (m *MockCustomizeCareTypeRepository) IsNameDuplicate(ctx context.Context, companyId int64, name string, serviceItemType offeringpb.ServiceItemType, filter *offeringsvcpb.WhiteListFilter) (bool, error) {
	args := m.Called(ctx, companyId, name, serviceItemType, filter)
	return args.Bool(0), args.Error(1)
}

func TestCustomizeCareTypeHandler_Update(t *testing.T) {
	tests := []struct {
		name            string
		serviceItemType offeringpb.ServiceItemType
		companyId       int64
		inputName       string
		staffId         int64
		filter          *offeringsvcpb.WhiteListFilter
		isDuplicate     bool
		duplicateError  error
		updateError     error
		expectedError   string
		expectedCode    codes.Code
	}{
		{
			name:            "successful update",
			serviceItemType: offeringpb.ServiceItemType_BOARDING,
			companyId:       100,
			inputName:       "New Care Type",
			staffId:         1,
			filter:          nil,
			isDuplicate:     false,
			duplicateError:  nil,
			updateError:     nil,
			expectedError:   "",
			expectedCode:    codes.OK,
		},
		{
			name:            "duplicate name error",
			serviceItemType: offeringpb.ServiceItemType_BOARDING,
			companyId:       100,
			inputName:       "Existing Care Type",
			staffId:         1,
			filter:          nil,
			isDuplicate:     true,
			duplicateError:  nil,
			updateError:     nil,
			expectedError:   "care type name 'Existing Care Type' already exists",
			expectedCode:    codes.AlreadyExists,
		},
		{
			name:            "database error during duplicate check",
			serviceItemType: offeringpb.ServiceItemType_BOARDING,
			companyId:       100,
			inputName:       "Some Care Type",
			staffId:         1,
			filter:          nil,
			isDuplicate:     false,
			duplicateError:  assert.AnError,
			updateError:     nil,
			expectedError:   "failed to check name duplicate",
			expectedCode:    codes.Internal,
		},
		{
			name:            "empty name error",
			serviceItemType: offeringpb.ServiceItemType_BOARDING,
			companyId:       100,
			inputName:       "",
			staffId:         1,
			filter:          nil,
			isDuplicate:     false,
			duplicateError:  errors.New("name cannot be empty"),
			updateError:     nil,
			expectedError:   "failed to check name duplicate",
			expectedCode:    codes.Internal,
		},
		{
			name:            "whitespace only name error",
			serviceItemType: offeringpb.ServiceItemType_BOARDING,
			companyId:       100,
			inputName:       "   ",
			staffId:         1,
			filter:          nil,
			isDuplicate:     false,
			duplicateError:  errors.New("name cannot be empty"),
			updateError:     nil,
			expectedError:   "failed to check name duplicate",
			expectedCode:    codes.Internal,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup mock
			mockRepo := new(MockCustomizeCareTypeRepository)
			handler := &customizeCareTypeHandler{
				customizeCareTypeRepository: mockRepo,
			}

			ctx := context.Background()

			// Setup expectations
			mockRepo.On("IsNameDuplicate", ctx, tt.companyId, tt.inputName, tt.serviceItemType, tt.filter).
				Return(tt.isDuplicate, tt.duplicateError)

			if tt.duplicateError == nil && !tt.isDuplicate {
				mockRepo.On("Update", ctx, mock.AnythingOfType("*models.CustomizeCareTypeWhereOpt"), mock.AnythingOfType("*models.CustomizeCareTypeUpdateOpt")).
					Return(tt.updateError)
			}

			// Execute
			err := handler.Update(ctx, &UpdateCustomizeCareTypeRequest{
				ServiceItemType: tt.serviceItemType, CompanyId: tt.companyId, Name: tt.inputName, StaffId: tt.staffId, Filter: tt.filter})

			// Assert
			if tt.expectedError == "" {
				assert.NoError(t, err)
			} else {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)

				// Check gRPC status code
				if st, ok := status.FromError(err); ok {
					assert.Equal(t, tt.expectedCode, st.Code())
				}
			}

			// Verify mock expectations
			mockRepo.AssertExpectations(t)
		})
	}
}
