package service

import (
	"context"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/MoeGolibrary/go-lib/zlog"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
)

type PlaygroupService interface {
	CreatePlaygroup(ctx context.Context, playgroupDO *do.PlaygroupDO) (int64, error)
	GetPlaygroupByID(ctx context.Context, id, companyId int64) (*do.PlaygroupDO, error)
	GetPlaygroupList(ctx context.Context, filter *do.PlaygroupFilterDO, pagination *utilsV2.PaginationRequest) ([]*do.PlaygroupDO, int32, error)
	UpdatePlaygroup(ctx context.Context, playgroupDO *do.PlaygroupDO) error
	DeletePlaygroup(ctx context.Context, companyID int64, id int64, operateId int64) error
	SortPlaygroup(ctx context.Context, playgroupSortDO *do.PlaygroupSortDO) error
}

type playgroupService struct {
	playgroupRepo repository.PlaygroupRepository
}

func NewPlaygroupService(playgroupRepository repository.PlaygroupRepository) PlaygroupService {
	return &playgroupService{playgroupRepo: playgroupRepository}
}

func (s *playgroupService) isNameExist(ctx context.Context, playgroupDO *do.PlaygroupDO) error {
	if playgroupDO == nil || playgroupDO.Name == "" || playgroupDO.CompanyID == 0 {
		return status.Errorf(codes.InvalidArgument, "playgroup name or company id is empty")
	}
	where := &models.PlaygroupWhereOpt{
		Name:      &playgroupDO.Name,
		CompanyID: &playgroupDO.CompanyID,
	}
	// 如果是更新操作，排除当前 ID
	if playgroupDO.ID > 0 {
		where.ExcludedID = &playgroupDO.ID
	}
	playgroup, err := s.playgroupRepo.GetPlaygroupByName(ctx, where)
	if err != nil {
		return err
	}

	if playgroup != nil {
		return status.Errorf(codes.AlreadyExists, "playgroup name already exists")
	}

	return nil
}

func (s *playgroupService) GetPlaygroupByID(ctx context.Context, id, companyId int64) (*do.PlaygroupDO, error) {
	playgroup, err := s.playgroupRepo.GetPlaygroupByID(ctx, &models.PlaygroupWhereOpt{
		ID:        &id,
		CompanyID: &companyId,
	})
	if err != nil {
		return nil, err
	}
	if playgroup == nil {
		return nil, status.Errorf(codes.NotFound, "playgroup not found")
	}
	return converter.ConverterPlaygroupModelToDO(playgroup), nil
}

func (s *playgroupService) CreatePlaygroup(ctx context.Context, playgroupDO *do.PlaygroupDO) (int64, error) {
	// 判断 name 是否重复
	err := s.isNameExist(ctx, playgroupDO)
	if err != nil {
		zlog.Error(ctx, "check playgroup name err:", zap.Error(err))
		return 0, err
	}

	playgroup := converter.ConverterCreatePlaygroupDOToModel(playgroupDO)

	// 获取该 company 下排序最大的 playgroup, 排在最后
	maxSortPlaygroup, err := s.GetMaxSortPlaygroup(ctx, playgroupDO.CompanyID)
	if err != nil {
		return 0, err
	}
	if maxSortPlaygroup == nil {
		playgroup.Sort = 1
	} else {
		playgroup.Sort = maxSortPlaygroup.Sort + 1
	}

	id, err := s.playgroupRepo.CreatePlaygroup(ctx, playgroup)
	if err != nil {
		return 0, err
	}
	return id, nil
}

// GetMaxSortPlaygroup 获取该 company 下排序最大的 playgroup
func (s *playgroupService) GetMaxSortPlaygroup(ctx context.Context, companyID int64) (*models.Playgroup, error) {
	playgroup, err := s.playgroupRepo.GetMaxSortPlaygroupByCompanyID(ctx, companyID)
	if err != nil {
		return nil, err
	}
	return playgroup, nil
}

func (s *playgroupService) GetPlaygroupList(ctx context.Context, filter *do.PlaygroupFilterDO, pagination *utilsV2.PaginationRequest) ([]*do.PlaygroupDO, int32, error) {
	playgroups, total, err := s.playgroupRepo.ListPlaygroup(ctx, &models.PlaygroupWhereOpt{
		CompanyID:      &filter.CompanyID,
		IDs:            filter.PlaygroupIds,
		IncludeDeleted: &filter.IncludeDeleted,
	}, pagination)
	if err != nil {
		return nil, 0, err
	}
	return converter.ConverterPlaygroupModelListToDO(playgroups), total, nil
}

func (s *playgroupService) UpdatePlaygroup(ctx context.Context, playgroupDO *do.PlaygroupDO) error {
	_, err := s.GetPlaygroupByID(ctx, playgroupDO.ID, playgroupDO.CompanyID)
	if err != nil {
		return err
	}
	// 判断 name 是否重复
	err = s.isNameExist(ctx, playgroupDO)
	if err != nil {
		zlog.Error(ctx, "check playgroup name err:", zap.Error(err))
		return err
	}

	playgroupUpdateOpt := converter.ConverterUpdatePlaygroupDOToUpdateOpt(playgroupDO)
	err = s.playgroupRepo.UpdatePlaygroup(ctx, &models.PlaygroupWhereOpt{
		ID: &playgroupDO.ID,
	}, playgroupUpdateOpt)
	if err != nil {
		return err
	}
	return nil
}

func (s *playgroupService) DeletePlaygroup(ctx context.Context, companyID int64, id int64, operateId int64) error {
	err := s.playgroupRepo.DeletePlaygroup(ctx, &models.PlaygroupWhereOpt{
		ID:        &id,
		CompanyID: &companyID,
	}, operateId)
	if err != nil {
		return err
	}
	return nil
}

func (s *playgroupService) SortPlaygroup(ctx context.Context, playgroupSortDO *do.PlaygroupSortDO) error {
	// 获取现有的 playgroup 列表
	playgroups, _, err := s.playgroupRepo.ListPlaygroup(ctx, &models.PlaygroupWhereOpt{
		CompanyID: &playgroupSortDO.CompanyID,
	}, nil)
	if err != nil {
		return err
	}

	validPlaygroupIds := lo.Map(playgroups, func(p *models.Playgroup, _ int) int64 {
		return p.ID
	})

	// 构建 ID 到 Sort 的映射
	updates := make(map[int64]int32)
	for i, id := range playgroupSortDO.PlaygroupIds {
		if lo.Contains(validPlaygroupIds, id) {
			updates[id] = int32(i + 1)
		}
	}

	// 执行批量更新排序
	return s.playgroupRepo.BatchUpdatePlaygroupSort(ctx,
		playgroupSortDO.CompanyID,
		updates,
		playgroupSortDO.OperateId,
	)
}
