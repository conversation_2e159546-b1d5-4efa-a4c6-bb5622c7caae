package service

import (
	"context"
	"sort"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	"github.com/MoeGolibrary/go-lib/merror"
	errorspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

type LodgingUnitHandler interface {
	BatchCreateLodgingUnit(ctx context.Context, dos []*do.LodgingUnitDO) ([]*do.LodgingUnitDO, error)
	UpdateLodgingUnit(ctx context.Context, id int64, companyID *int64, updateOpt *do.LodgingUnitUpdateOpt) (*do.LodgingUnitDO, error)
	ListLodgingUnit(ctx context.Context, companyID int64, whereOpt *do.LodgingUnitWhereOpt) ([]*do.LodgingUnitDO, error)
	MGetLodgingUnit(ctx context.Context, idList []int64) ([]*do.LodgingUnitDO, error)
	BatchDeleteLodgingUnit(ctx context.Context, ids []int64, companyId *int64, deletedBy *int64) error
	SortLodgingUnit(ctx context.Context, companyId *int64, updatedBy *int64, ids []int64) error
}

type lodgingUnitHandler struct {
	lodgingUnitRepository repository.LodgingUnitRepository
	lodgingTypeRepository repository.LodgingTypeRepository
	serviceRepository     repository.ServiceRepository
	evaluationRepository  repository.EvaluationRepository
}

type SortKey struct {
	BusinessID    int64
	LodgingTypeID int64
}

func (l lodgingUnitHandler) getMaxSortByLodgingType(ctx context.Context, dos []*do.LodgingUnitDO) (map[SortKey]int32, error) {
	sortMap := make(map[SortKey]int32)
	for _, unit := range dos {
		key := SortKey{
			BusinessID:    unit.BusinessID,
			LodgingTypeID: unit.LodgingTypeID,
		}
		if _, exists := sortMap[key]; !exists {
			maxSort, err := l.lodgingUnitRepository.GetMaxSort(ctx, &do.LodgingUnitWhereOpt{
				BusinessID:    &unit.BusinessID,
				LodgingTypeID: &unit.LodgingTypeID,
			})
			if err != nil {
				return nil, err
			}
			sortMap[key] = maxSort
		}
	}
	return sortMap, nil
}

func (l lodgingUnitHandler) BatchCreateLodgingUnit(ctx context.Context, dos []*do.LodgingUnitDO) ([]*do.LodgingUnitDO, error) {
	typeIds := make([]int64, len(dos))
	for i, unit := range dos {
		typeIds[i] = unit.LodgingTypeID
	}
	typeMap, err := l.getLodgingTypeMap(ctx, typeIds)
	if err != nil {
		return nil, err
	}

	sortMap, err := l.getMaxSortByLodgingType(ctx, dos)
	if err != nil {
		return nil, err
	}

	for _, unit := range dos {
		lodgingType := typeMap[unit.LodgingTypeID]
		if lodgingType == nil {
			return nil, merror.NewBizErrorWithStack(errorspb.Code_CODE_LODGING_TYPE_NOT_FOUND, "lodging type invalid")
		}
		key := SortKey{
			BusinessID:    unit.BusinessID,
			LodgingTypeID: unit.LodgingTypeID,
		}
		sortMap[key]++
		unit.Sort = sortMap[key]
	}
	return l.lodgingUnitRepository.BatchAdd(ctx, dos)
}

func (l lodgingUnitHandler) UpdateLodgingUnit(ctx context.Context, id int64, companyID *int64, updateOpt *do.LodgingUnitUpdateOpt) (*do.LodgingUnitDO, error) {
	// Step 1: 重置其他单元的 CameraId
	if updateOpt.CameraId != nil && *updateOpt.CameraId > 0 {
		where := &models.LodgingUnitWhereOpt{
			CompanyID: companyID,
			CameraId:  updateOpt.CameraId,
		}
		updateOpt := &do.LodgingUnitUpdateOpt{
			CameraId:  lo.ToPtr(int64(0)),
			UpdatedAt: lo.ToPtr(time.Now()),
		}
		_, err := l.lodgingUnitRepository.Update(ctx, where, updateOpt)
		if err != nil {
			return nil, errors.WithStack(err)
		}
	}

	// Step 2: 更新当前单元的信息
	where := &models.LodgingUnitWhereOpt{
		ID:        &id,
		CompanyID: companyID,
	}
	return l.lodgingUnitRepository.Update(ctx, where, updateOpt)
}

func (l lodgingUnitHandler) ListLodgingUnit(ctx context.Context, companyID int64, whereOpt *do.LodgingUnitWhereOpt) ([]*do.LodgingUnitDO, error) {
	where, err := l.whereOptDoToModel(ctx, companyID, whereOpt)
	if err != nil {
		return nil, err
	}
	units, err := l.lodgingUnitRepository.List(ctx, where)
	if err != nil {
		return nil, err
	}
	typeIds := lo.Map(units, func(unit *models.LodgingUnit, _ int) int64 {
		return unit.LodgingTypeID
	})
	typeMap, err := l.getLodgingTypeMap(ctx, lo.Uniq(typeIds))
	if err != nil {
		return nil, err
	}
	sortedUnits := l.SortByTypeThenByUnits(typeMap, units)

	return converter.ConvertLodgingUnitPOListToDO(sortedUnits), nil
}

func (l lodgingUnitHandler) SortByTypeThenByUnits(
	typeMap map[int64]*do.LodgingTypeDO,
	units []*models.LodgingUnit,
) []*models.LodgingUnit {
	sortedUnits := make([]*models.LodgingUnit, len(units))
	copy(sortedUnits, units)

	sort.SliceStable(sortedUnits, func(i, j int) bool {
		// 获取对应的 lodgingType
		typeI := typeMap[sortedUnits[i].LodgingTypeID]
		typeJ := typeMap[sortedUnits[j].LodgingTypeID]

		// 首先按照 lodgingType 的 sort 排序
		if typeI.Sort != typeJ.Sort {
			return typeI.Sort < typeJ.Sort
		}

		// 如果 lodgingType 的 sort 相同，则按照 lodgingUnit 的 sort 排序
		return sortedUnits[i].Sort < sortedUnits[j].Sort
	})

	return sortedUnits
}

func (l lodgingUnitHandler) MGetLodgingUnit(ctx context.Context, idList []int64) ([]*do.LodgingUnitDO, error) {
	return l.lodgingUnitRepository.MGet(ctx, idList)
}

func (l lodgingUnitHandler) BatchDeleteLodgingUnit(ctx context.Context, ids []int64, companyId *int64, deletedBy *int64) error {
	return l.lodgingUnitRepository.BatchDelete(ctx, ids, companyId, deletedBy)
}

func (l lodgingUnitHandler) getLodgingTypeMap(ctx context.Context, typeIds []int64) (map[int64]*do.LodgingTypeDO, error) {
	typeDos, err := l.lodgingTypeRepository.MGet(ctx, typeIds)
	if err != nil {
		return nil, err
	}
	typeMap := make(map[int64]*do.LodgingTypeDO)
	for _, t := range typeDos {
		typeMap[t.ID] = t
	}
	return typeMap, nil
}

func (l lodgingUnitHandler) whereOptDoToModel(ctx context.Context, companyID int64, whereOpt *do.LodgingUnitWhereOpt) (*models.LodgingUnitWhereOpt, error) {
	where := converter.ConvertLodgingUnitWhereOptDoToModel(whereOpt)
	where.CompanyID = &companyID
	if whereOpt.ServiceID != nil {
		typeIDs, err := l.getDedicatedLodgingByService(ctx, *whereOpt.ServiceID)
		if err != nil {
			return nil, err
		}
		where.AddLodgingTypesFilter(typeIDs)
	}
	if whereOpt.EvaluationServiceID != nil {
		typeIDs, err := l.getDedicatedLodgingByEvaluationService(ctx, *whereOpt.EvaluationServiceID)
		if err != nil {
			return nil, err
		}
		where.AddLodgingTypesFilter(typeIDs)
	}
	return where, nil
}

func (l lodgingUnitHandler) getDedicatedLodgingByService(ctx context.Context, serviceID int64) ([]int64, error) {
	result, err := l.batchGetDedicatedLodgingByService(ctx, []int64{serviceID})
	if err != nil {
		return nil, err
	}
	return result[serviceID], nil
}

func (l lodgingUnitHandler) batchGetDedicatedLodgingByService(ctx context.Context, serviceIDs []int64) (map[int64][]int64, error) {
	serviceBriefList, err := l.serviceRepository.GetBriefServiceListWithServiceIds(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}
	serviceMap := lo.SliceToMap(serviceBriefList, func(service do.ServiceBrief) (int64, do.ServiceBrief) {
		return service.ServiceId, service
	})

	result := make(map[int64][]int64)
	for _, serviceID := range serviceIDs {
		service, ok := serviceMap[serviceID]
		// 如果 service 不存在，没有可选 lodgingType, 返回空列表
		if !ok {
			result[serviceID] = []int64{}
			continue
		}
		// 如果 service 没有开启 lodgingFilter，所有 lodgingType 都可选
		if !service.LodgingFilter {
			continue
		}
		result[serviceID] = service.CustomizedLodgings
	}
	return result, nil
}

func (l lodgingUnitHandler) getDedicatedLodgingByEvaluationService(ctx context.Context, serviceID int64) ([]int64, error) {
	result, err := l.batchGetDedicatedLodgingByEvaluationService(ctx, []int64{serviceID})
	if err != nil {
		return nil, err
	}
	return result[serviceID], nil
}

func (l lodgingUnitHandler) batchGetDedicatedLodgingByEvaluationService(ctx context.Context, serviceIDs []int64) (map[int64][]int64, error) {
	existServices, err := l.evaluationRepository.GetEvaluationListByIds(ctx, serviceIDs)
	if err != nil {
		return nil, err
	}
	serviceMap := lo.SliceToMap(existServices, func(service *do.EvaluationDO) (int64, *do.EvaluationDO) {
		return service.Id, service
	})

	result := make(map[int64][]int64)
	for _, serviceID := range serviceIDs {
		service, ok := serviceMap[serviceID]
		// 如果 service 不存在，没有可选 lodgingType, 返回空列表
		if !ok {
			result[serviceID] = []int64{}
			continue
		}
		// 如果 service 没有开启 lodgingFilter，所有 lodgingType 都可选
		if !service.LodgingFilter {
			continue
		}
		result[serviceID] = service.CustomizedLodgingIds
	}
	return result, nil
}

func (l lodgingUnitHandler) SortLodgingUnit(
	ctx context.Context,
	companyId *int64,
	updatedBy *int64,
	ids []int64,
) error {
	if len(ids) == 0 {
		return nil
	}
	var updateOpts = make([]*do.LodgingUnitUpdateByIDOpt, 0, len(ids))
	for i, id := range ids {
		sort := int32(i)
		updateOpts = append(updateOpts, &do.LodgingUnitUpdateByIDOpt{
			ID:        id,
			Sort:      &sort,
			UpdatedBy: updatedBy,
		})
	}

	_, err := l.lodgingUnitRepository.BatchUpdateById(ctx, companyId, updateOpts)
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func NewLodgingUnitHandler() LodgingUnitHandler {
	return &lodgingUnitHandler{
		lodgingUnitRepository: repository.NewLodgingUnitRepository(resource.GetOfferingDB()),
		lodgingTypeRepository: repository.NewLodgingTypeRepository(resource.GetOfferingDB()),
		serviceRepository:     repository.NewServiceRepository(resource.GetGroomingDB()),
		evaluationRepository:  repository.NewEvaluationRepository(resource.GetOfferingDB()),
	}
}
