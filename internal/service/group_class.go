package service

import (
	"context"
	"errors"
	"fmt"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	"sort"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/genproto/googleapis/type/datetime"
	"google.golang.org/genproto/googleapis/type/dayofweek"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	moneyutils "github.com/MoeGolibrary/go-lib/common/proto/money"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/clients"
	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
	"github.com/MoeGolibrary/moego-svc-offering/internal/utils"
)

type GroupClassHandler interface {
	CreateInstanceAndSessions(ctx context.Context,
		opt *do.GroupClassInstanceCreateOpt) (*do.GroupClassInstanceDO, []*do.GroupClassSessionDO, error)
	CountInstanceGroupByStatus(ctx context.Context,
		filter *do.GroupClassInstanceFilter) ([]*offeringsvcpb.CountInstancesGroupByStatusResponse_Count, error)
	CountInstanceGroupByServiceID(ctx context.Context,
		filter *do.GroupClassInstanceFilter) ([]*offeringsvcpb.CountInstancesGroupByClassResponse_Count, error)
	GetInstance(ctx context.Context, id int64) (*do.GroupClassInstanceDO, error)
	ListInstances(ctx context.Context, filter *do.GroupClassInstanceFilter,
		page *utilsV2.PaginationRequest) ([]*do.GroupClassInstanceDO, *utilsV2.PaginationResponse, error)
	UpdateInstanceAndSessions(ctx context.Context, filter *do.GroupClassInstanceFilter,
		opt *do.GroupClassInstanceUpdateOpt) (*do.GroupClassInstanceDO, []*do.GroupClassSessionDO, error)
	DeleteInstanceAndSessions(ctx context.Context, instanceID int64) error
	TaskRefreshInstanceStatus(ctx context.Context) error

	UpdateSession(ctx context.Context,
		filter *do.GroupClassSessionFilter, opt *do.GroupClassSessionUpdateOpt) (*do.GroupClassSessionDO, error)
	ListSessions(ctx context.Context, filter *do.GroupClassSessionFilter,
		page *utilsV2.PaginationRequest) ([]*do.GroupClassSessionDO, *utilsV2.PaginationResponse, error)
}

func NewGroupClassHandler(
	serviceManageMent ServiceHandler,
	messageDeliver MessageDeliverHandler,
	transactionManger repository.TransactionManager,
	groupClassInstanceRepository repository.GroupClassInstanceRepository,
	groupClassSessionRepository repository.GroupClassSessionRepository,
) GroupClassHandler {
	return &groupClassHandler{
		serviceManagement:            serviceManageMent,
		messageDeliver:               messageDeliver,
		transactionManager:           transactionManger,
		groupClassInstanceRepository: groupClassInstanceRepository,
		groupClassSessionRepository:  groupClassSessionRepository,
		companyClient:                clients.NewCompanyClient(resource.GetCompanyServiceClient()),
	}
}

type groupClassHandler struct {
	serviceManagement ServiceHandler
	messageDeliver    MessageDeliverHandler

	transactionManager           repository.TransactionManager
	groupClassInstanceRepository repository.GroupClassInstanceRepository
	groupClassSessionRepository  repository.GroupClassSessionRepository
	companyClient                clients.CompanyClient
}

func (h *groupClassHandler) CreateInstanceAndSessions(ctx context.Context,
	opt *do.GroupClassInstanceCreateOpt) (*do.GroupClassInstanceDO, []*do.GroupClassSessionDO, error) {
	setting, err := h.serviceManagement.GetServiceDetail(ctx, opt.GroupClassID)
	if err != nil {
		zlog.Error(ctx, "failed to get service detail",
			zap.Int64("company_id", opt.CompanyID),
			zap.Int64("group_class_id", opt.GroupClassID),
			zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to get service detail: %v", err)
	}
	if err := h.fillTimezone(ctx, opt); err != nil {
		zlog.Error(ctx, "failed to fillTimezone", zap.Error(err))
		return nil, nil, err
	}
	if err := h.validatePermission(opt, setting); err != nil {
		zlog.Error(ctx, "failed to validate permission",
			zap.Int64("service_id", opt.GroupClassID),
			zap.Int64("company_id", opt.CompanyID),
			zap.Int64("business_id", opt.BusinessID),
			zap.Error(err))
		return nil, nil, err
	}

	preference, err := h.companyClient.GetCompanyPreferenceSetting(ctx, opt.CompanyID)
	if err != nil {
		zlog.Error(ctx, "failed to GetPreference", zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to get preference: %v", err)
	}
	instance, err := buildGroupClassInstance(opt, setting, preference)
	if err != nil {
		zlog.Error(ctx, "failed to build instance",
			zap.Int64("service_id", opt.GroupClassID),
			zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to build instance: %v", err)
	}

	var sessions []*do.GroupClassSessionDO
	if err := h.transactionManager.Tx(func(tx repository.Transaction) error {
		instance, err = tx.GroupClassInstance().Insert(ctx, instance)
		if err != nil {
			zlog.Error(ctx, "failed to insert instance",
				zap.Int64("service_id", opt.GroupClassID),
				zap.Error(err))
			return fmt.Errorf("failed to insert instance: %v", err)
		}

		sessions = buildGroupClassSessions(instance, setting)
		if len(sessions) == 0 {
			zlog.Error(ctx, "failed to build sessions",
				zap.Any("opt", *opt))
			return status.Error(codes.InvalidArgument, "failed to build sessions")
		}

		if _, err := tx.GroupClassSession().BatchInsert(ctx, sessions); err != nil {
			zlog.Error(ctx, "failed to insert sessions", zap.Error(err))
			return fmt.Errorf("failed to insert sessions: %v", err)
		}

		return nil
	}); err != nil {
		zlog.Error(ctx, "failed to do transaction", zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to do transaction: %v", err)
	}

	return instance, sessions, nil
}

func (h *groupClassHandler) fillTimezone(ctx context.Context, opt *do.GroupClassInstanceCreateOpt) error {
	tz := opt.StartTime.GetTimeZone().GetId()
	if tz != "" {
		_, err := time.LoadLocation(tz)
		if err == nil {
			return nil
		}
		zlog.Error(ctx, "failed to LoadLocation", zap.String("timezone", tz))
	}
	preference, err := h.companyClient.GetCompanyPreferenceSetting(ctx, opt.CompanyID)
	if err != nil {
		zlog.Error(ctx, "failed to GetPreference", zap.Error(err))
		return status.Errorf(codes.Internal, "failed to GetPreference: %v", err)
	}

	tz = preference.GetTimeZone().GetName()
	_, err = time.LoadLocation(tz)
	if err != nil {
		zlog.Error(ctx, "failed to LoadLocationFromPreference", zap.String("timezone", tz))
		return status.Errorf(codes.DataLoss, "failed to LoadLocationFromPreference: %v", err)
	}

	opt.StartTime.TimeOffset = &datetime.DateTime_TimeZone{
		TimeZone: &datetime.TimeZone{
			Id: tz,
		},
	}
	return nil
}

// TODO: is there another validation? or is there a common validation function?
func (h *groupClassHandler) validatePermission(opt *do.GroupClassInstanceCreateOpt, setting *do.ServiceDO) error {
	if setting.CompanyId != opt.CompanyID {
		return status.Error(codes.PermissionDenied, "company id not match")
	}
	if setting.Availability.IsAllLocation != nil && *setting.Availability.IsAllLocation {
		return nil
	}
	for _, location := range setting.Availability.AvailableBusinessIdList {
		if location == opt.BusinessID {
			return nil
		}
	}
	return status.Errorf(codes.PermissionDenied, "location not match")
}

func (h *groupClassHandler) CountInstanceGroupByStatus(ctx context.Context,
	filter *do.GroupClassInstanceFilter) ([]*offeringsvcpb.CountInstancesGroupByStatusResponse_Count, error) {
	counts, err := h.groupClassInstanceRepository.CountGroupByStatus(ctx, filter)
	if err != nil {
		zlog.Error(ctx, "failed to CountGroupByStatus",
			zap.Int64("company_id", filter.CompanyID),
			zap.Int64s("business_id", filter.BusinessIDs),
			zap.Int64s("staff_ids", filter.StaffIDs),
			zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to query db: %v", err)
	}

	results := make([]*offeringsvcpb.CountInstancesGroupByStatusResponse_Count, 0, len(counts))
	for _, c := range counts {
		results = append(results, &offeringsvcpb.CountInstancesGroupByStatusResponse_Count{
			Status: c.Status,
			Count:  c.Count,
		})
	}
	return results, nil
}

func (h *groupClassHandler) CountInstanceGroupByServiceID(ctx context.Context,
	filter *do.GroupClassInstanceFilter) ([]*offeringsvcpb.CountInstancesGroupByClassResponse_Count, error) {
	counts, err := h.groupClassInstanceRepository.CountGroupByServiceID(ctx, filter)
	if err != nil {
		zlog.Error(ctx, "failed to CountGroupByServiceID",
			zap.Int64("company_id", filter.CompanyID),
			zap.Int64s("business_id", filter.BusinessIDs),
			zap.Int64s("staff_ids", filter.StaffIDs),
			zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to query db: %v", err)
	}
	results := make([]*offeringsvcpb.CountInstancesGroupByClassResponse_Count, 0, len(counts))
	for _, c := range counts {
		results = append(results, &offeringsvcpb.CountInstancesGroupByClassResponse_Count{
			GroupClassId: c.ServiceID,
			Count:        c.Count,
		})
	}
	return results, nil
}

func (h *groupClassHandler) GetInstance(ctx context.Context, id int64) (*do.GroupClassInstanceDO, error) {
	instance, err := h.groupClassInstanceRepository.Get(ctx, id)
	if err != nil {
		zlog.Error(ctx, "failed to Get", zap.Int64("id", id), zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to Get: %v", err)
	}
	return instance, nil
}

func (h *groupClassHandler) ListInstances(ctx context.Context,
	filter *do.GroupClassInstanceFilter, page *utilsV2.PaginationRequest) ([]*do.GroupClassInstanceDO, *utilsV2.PaginationResponse, error) {
	dos, pageResponse, err := h.groupClassInstanceRepository.List(ctx, filter, page)
	if err != nil {
		zlog.Error(ctx, "failed to List",
			zap.Any("filter", filter),
			zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to List: %v", err)
	}
	return dos, pageResponse, nil
}

func (h *groupClassHandler) UpdateInstanceAndSessions(ctx context.Context,
	filter *do.GroupClassInstanceFilter,
	opt *do.GroupClassInstanceUpdateOpt) (*do.GroupClassInstanceDO, []*do.GroupClassSessionDO, error) {
	if len(filter.IDs) != 1 {
		zlog.Error(ctx, "only one allowed", zap.Int64s("ids", filter.IDs))
		return nil, nil, status.Error(codes.InvalidArgument, "only one allowed")
	}
	instance, err := h.groupClassInstanceRepository.Get(ctx, filter.IDs[0])
	if err != nil {
		zlog.Error(ctx, "failed to GetInstance",
			zap.Int64("id", filter.IDs[0]), zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to GetInstance: %v", err)
	}
	if instance.Status != offeringpb.GroupClassInstance_UPCOMING {
		zlog.Error(ctx, "only upcoming allowed", zap.Stringer("status", instance.Status))
		return nil, nil,
			status.Errorf(codes.FailedPrecondition, "%s is not allowed to update", instance.Status)
	}
	if instance.StartTime.Before(time.Now()) {
		zlog.Error(ctx, "has started", zap.Time("start_time", *instance.StartTime))
		return nil, nil,
			status.Error(codes.FailedPrecondition, "has started")
	}

	sessions, _, err := h.groupClassSessionRepository.List(ctx, instance.StartTime.Location(),
		&do.GroupClassSessionFilter{
			InstanceIDs: []int64{instance.ID},
		}, nil)
	if err != nil {
		zlog.Error(ctx, "failed to ListSessions",
			zap.Int64("instance_id", instance.ID),
			zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to ListSessions: %v", err)
	}

	setting, err := h.serviceManagement.GetServiceDetail(ctx, instance.ServiceID)
	if err != nil {
		zlog.Error(ctx, "failed to get service detail",
			zap.Int64("service_id", instance.ServiceID),
			zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to get service detail: %v", err)
	}

	createOpt := &do.GroupClassInstanceCreateOpt{
		CompanyID:    instance.CompanyID,
		BusinessID:   instance.BusinessID,
		GroupClassID: instance.ServiceID,
		StaffID:      instance.StaffID,
		StartTime:    utils.GoogleDateTimeFromTime(instance.StartTime),
		Occurrence:   instance.Occurrence,
	}
	if opt.StaffID != nil && *opt.StaffID > 0 {
		createOpt.StaffID = *opt.StaffID
	}
	if opt.StartTime != nil {
		createOpt.StartTime = opt.StartTime
		if err := h.fillTimezone(ctx, createOpt); err != nil {
			return nil, nil, err
		}
	}
	if opt.Occurrence != nil {
		createOpt.Occurrence = opt.Occurrence
	}

	startTime, err := utils.GetTimeFromGoogleDateTime(createOpt.StartTime)
	if err != nil {
		zlog.Error(ctx, "failed to Parse timezone")
		return nil, nil, status.Error(codes.DataLoss, "failed to Parse timezone")
	}

	if startTime.Before(time.Now()) {
		zlog.Error(ctx, "not allowed to create past instance",
			zap.Time("time", startTime))
		return nil, nil, status.Error(codes.InvalidArgument, "not allowed to created past instance")
	}
	preference, err := h.companyClient.GetCompanyPreferenceSetting(ctx, instance.CompanyID)
	if err != nil {
		zlog.Error(ctx, "failed to GetPreference", zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to get preference: %v", err)
	}
	expectedInstance, err := buildGroupClassInstance(createOpt, setting, preference)
	if err != nil {
		zlog.Error(ctx, "failed to build instance",
			zap.Any("createOpt", createOpt),
			zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to build instance: %v", err)
	}
	expectedInstance.ID = instance.ID

	expectedSessions := buildGroupClassSessions(expectedInstance, setting)
	if len(expectedSessions) != len(sessions) {
		zlog.Error(ctx, "unmatched num",
			zap.Int("current", len(sessions)), zap.Int("expected", len(expectedSessions)))
		return nil, nil, status.Error(codes.Internal, "unmatched num")
	}
	sort.Slice(sessions, func(i, j int) bool {
		return sessions[i].StartTime.Before(*sessions[j].StartTime)
	})
	for i, s := range expectedSessions {
		s.ID = sessions[i].ID
	}

	if err := h.transactionManager.Tx(func(tx repository.Transaction) error {
		if err := tx.GroupClassInstance().Update(ctx, expectedInstance); err != nil {
			zlog.Error(ctx, "failed to UpdateInstance",
				zap.Int64("id", expectedInstance.ID),
				zap.Error(err))
			return err
		}
		for _, session := range expectedSessions {
			if err := h.updateSession(ctx, tx, session); err != nil {
				zlog.Error(ctx, "failed to UpdateSession",
					zap.Int64("id", session.ID), zap.Error(err))
				return err
			}
		}
		return nil
	}); err != nil {
		zlog.Error(ctx, "failed to tx", zap.Error(err))
		return nil, nil, err
	}

	go func() {
		for _, session := range expectedSessions {
			_ = h.messageDeliver.Send(ctx,
				eventbuspb.EventType_OFFERING_GROUP_CLASS_SESSION_UPDATED, strconv.FormatInt(session.ID, 10))
		}
	}()

	return expectedInstance, expectedSessions, nil
}

func (h *groupClassHandler) DeleteInstanceAndSessions(ctx context.Context, instanceID int64) error {
	instance, err := h.groupClassInstanceRepository.Get(ctx, instanceID)
	if err != nil {
		zlog.Error(ctx, "failed to Get", zap.Int64("id", instanceID), zap.Error(err))
		return status.Errorf(codes.Internal, "failed to GetInstance: %v", err)
	}
	if instance.StartTime.Before(time.Now()) {
		zlog.Error(ctx, "can not delete past instance",
			zap.Time("start_time", *instance.StartTime),
			zap.Int64("instance_id", instanceID))
		return status.Errorf(codes.FailedPrecondition, "can not delete past or in-progress instance")
	}
	if err := h.transactionManager.Tx(func(tx repository.Transaction) error {
		if err := tx.GroupClassInstance().Delete(ctx, instanceID); err != nil {
			zlog.Error(ctx, "failed to DeleteInstance",
				zap.Int64("id", instanceID),
				zap.Error(err))
			return status.Errorf(codes.Internal, "failed to DeleteInstance: %v", err)
		}
		if err := tx.GroupClassSession().Delete(ctx, &do.GroupClassSessionFilter{
			InstanceIDs: []int64{instanceID},
		}); err != nil {
			zlog.Error(ctx, "failed to DeleteSessions",
				zap.Int64("instance_id", instanceID),
				zap.Error(err))
			return status.Errorf(codes.Internal, "failed to DeleteSessions: %v", err)
		}
		return nil
	}); err != nil {
		zlog.Error(ctx, "failed to Tx", zap.Error(err))
		return err
	}
	return nil
}

func (h *groupClassHandler) TaskRefreshInstanceStatus(ctx context.Context) error {
	const (
		batchSize = 50
	)

	var pageNum int32 = 1
	for {
		instances, _, err := h.groupClassInstanceRepository.List(ctx, &do.GroupClassInstanceFilter{
			Statuses: []offeringpb.GroupClassInstance_Status{
				offeringpb.GroupClassInstance_IN_PROGRESS,
				offeringpb.GroupClassInstance_UPCOMING,
			},
		}, &utilsV2.PaginationRequest{
			PageSize: proto.Int32(batchSize),
			PageNum:  proto.Int32(pageNum),
		})
		if err != nil {
			zlog.Error(ctx, "failed to List", zap.Error(err))
			return status.Errorf(codes.Internal, "failed to List: %v", err)
		}
		if len(instances) == 0 {
			return nil
		}
		for _, instance := range instances {
			if err := h.refreshInstanceStatus(ctx, instance); err != nil {
				return err
			}
		}
		pageNum++
	}
}

func (h *groupClassHandler) refreshInstanceStatus(ctx context.Context, instance *do.GroupClassInstanceDO) error {
	var expect offeringpb.GroupClassInstance_Status

	now := time.Now().In(instance.StartTime.Location())
	startTime := instance.StartTime
	if now.Year() == startTime.Year() &&
		now.Month() == startTime.Month() &&
		now.Day() == startTime.Day() {
		expect = offeringpb.GroupClassInstance_IN_PROGRESS
	} else if startTime.After(now) {
		expect = offeringpb.GroupClassInstance_UPCOMING
	} else {
		po, err := h.groupClassSessionRepository.GetLast(ctx, instance.ID)
		if err != nil {
			zlog.Error(ctx, "failed to GetLast",
				zap.Int64("instance_id", instance.ID), zap.Error(err))
			return status.Errorf(codes.Internal, "failed to GetLast: %v", err)
		}
		session, err := converter.ConvertGroupClassSessionPOToDO(po, instance.StartTime.Location().String())
		if err != nil {
			zlog.Error(ctx, "failed to Convert", zap.Error(err))
			return status.Errorf(codes.Unknown, "failed to Convert: %v", err)
		}
		endTime := session.StartTime.Add(session.Duration)
		if endTime.Year() == now.Year() &&
			endTime.Month() == now.Month() &&
			endTime.Day() == now.Day() {
			expect = offeringpb.GroupClassInstance_IN_PROGRESS
		} else if endTime.Before(now) {
			expect = offeringpb.GroupClassInstance_PAST
		} else {
			expect = offeringpb.GroupClassInstance_IN_PROGRESS
		}
	}

	if instance.Status == expect {
		return nil
	}
	instance.Status = expect
	if err := h.groupClassInstanceRepository.Update(ctx, instance); err != nil {
		zlog.Error(ctx, "failed to UpdateInstance",
			zap.Int64("id", instance.ID), zap.Error(err))
		return status.Errorf(codes.Internal, "failed to UpdateInstance: %v", err)
	}

	return nil
}

func (h *groupClassHandler) UpdateSession(ctx context.Context,
	filter *do.GroupClassSessionFilter, opt *do.GroupClassSessionUpdateOpt) (*do.GroupClassSessionDO, error) {
	if len(filter.IDs) != 1 {
		zlog.Error(ctx, "only one session allowed", zap.Int64s("id", filter.IDs))
		return nil, status.Error(codes.InvalidArgument, "only one session allowed")
	}
	if opt.StartTime != nil && opt.StartTime.Before(time.Now()) {
		zlog.Error(ctx, "can not start in the past", zap.Time("start_time", *opt.StartTime))
		return nil, status.Error(codes.InvalidArgument, "can't start in the past")
	}

	sessionPo, err := h.groupClassSessionRepository.Get(ctx, filter.IDs[0])
	if err != nil {
		zlog.Error(ctx, "failed to GetSession",
			zap.Int64("id", filter.IDs[0]),
			zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to GetSession: %v", err)
	}
	if filter.CompanyID > 0 && filter.CompanyID != sessionPo.CompanyID {
		zlog.Error(ctx, "unmatched company",
			zap.Int64("req_company_id", filter.CompanyID),
			zap.Int64("company_id", sessionPo.CompanyID))
		return nil, status.Error(codes.PermissionDenied, "unmatched company")
	}
	if sessionPo.StartTime.Before(time.Now()) {
		zlog.Error(ctx, "not allowed to edit",
			zap.Time("start_time", *sessionPo.StartTime))
		return nil, status.Error(codes.InvalidArgument, "session is past")
	}

	instance, err := h.groupClassInstanceRepository.Get(ctx, sessionPo.InstanceID)
	if err != nil {
		zlog.Error(ctx, "failed to GetInstance",
			zap.Int64("instance_id", sessionPo.InstanceID),
			zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to GetInstance: %v", err)
	}

	sessions, _, err := h.groupClassSessionRepository.List(ctx, instance.StartTime.Location(),
		&do.GroupClassSessionFilter{
			InstanceIDs: []int64{sessionPo.InstanceID},
		}, nil)
	if err != nil {
		zlog.Error(ctx, "failed to ListSessions", zap.Int64("instance_id", sessionPo.InstanceID),
			zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to ListSessions: %v", err)
	}

	if err := validateUpdateSessionInput(sessionPo.ID, opt, sessions); err != nil {
		zlog.Error(ctx, "failed to validateUpdateInput",
			zap.Error(err))
		return nil, err
	}

	var session *do.GroupClassSessionDO
	for _, s := range sessions {
		if s.ID == sessionPo.ID {
			session = s
			break
		}
	}

	if opt.StartTime != nil {
		session.StartTime = opt.StartTime
	}
	if opt.Duration != nil {
		session.Duration = *opt.Duration
	}
	session.IsModified = true

	instanceUpdateFn := func(tx repository.Transaction) error {
		return nil
	}
	// if this is the first session, need to update instance's start_time
	if sessions[0].ID == sessionPo.ID {
		t := session.StartTime
		instanceStartTime := time.Date(t.Year(), t.Month(), t.Day(),
			instance.StartTime.Hour(), instance.StartTime.Minute(), instance.StartTime.Second(),
			instance.StartTime.Nanosecond(),
			instance.StartTime.Location())
		instance.StartTime = &instanceStartTime

		now := time.Now().In(instance.StartTime.Location())
		if t.Year() == now.Year() && t.Month() == now.Month() && t.Day() == now.Day() {
			instance.Status = offeringpb.GroupClassInstance_IN_PROGRESS
		} else {
			instance.Status = offeringpb.GroupClassInstance_UPCOMING
		}

		instanceUpdateFn = func(tx repository.Transaction) error {
			if err := tx.GroupClassInstance().Update(ctx, instance); err != nil {
				zlog.Error(ctx, "failed to UpdateInstance",
					zap.Int64("id", instance.ID),
					zap.Error(err))
				return err
			}
			return nil
		}
	}

	if err := h.transactionManager.Tx(func(tx repository.Transaction) error {
		if err := h.updateSession(ctx, tx, session); err != nil {
			return err
		}
		if err := instanceUpdateFn(tx); err != nil {
			return err
		}
		return nil
	}); err != nil {
		zlog.Error(ctx, "failed to Tx", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to tx: %v", err)
	}

	go func() {
		_ = h.messageDeliver.Send(context.WithoutCancel(ctx),
			eventbuspb.EventType_OFFERING_GROUP_CLASS_SESSION_UPDATED, strconv.FormatInt(session.ID, 10))
	}()

	return session, nil
}

func (h *groupClassHandler) updateSession(ctx context.Context, tx repository.Transaction, session *do.GroupClassSessionDO) error {
	existingSession, err := h.groupClassSessionRepository.Get(ctx, session.ID)
	if err != nil {
		return err
	}
	if err := tx.GroupClassSession().Update(ctx, session); err != nil {
		zlog.Error(ctx, "failed to UpdateSession",
			zap.Int64("id", session.ID),
			zap.Error(err))
		return err
	}
	location, err := h.companyClient.GetTimeLocation(ctx, existingSession.CompanyID)
	if err != nil {
		return err
	}
	if err := h.messageDeliver.InsertGroupClassSession(ctx, tx, existingSession, session, location); err != nil {
		return err
	}

	return nil
}

func (h *groupClassHandler) ListSessions(ctx context.Context, filter *do.GroupClassSessionFilter,
	page *utilsV2.PaginationRequest) ([]*do.GroupClassSessionDO, *utilsV2.PaginationResponse, error) {
	// get timezone
	instances, _, err := h.groupClassInstanceRepository.List(ctx, &do.GroupClassInstanceFilter{
		CompanyID: filter.CompanyID,
		IDs:       filter.InstanceIDs,
	}, nil)
	if err != nil {
		zlog.Error(ctx, "failed to ListInstances", zap.Int64s("instance_ids", filter.InstanceIDs),
			zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to List instance: %v", err)
	}
	if len(instances) == 0 {
		return nil, &utilsV2.PaginationResponse{
			PageSize: page.GetPageSize(),
			PageNum:  page.GetPageNum(),
			Total:    0,
		}, nil
	}

	sessions, pageRsp, err := h.groupClassSessionRepository.List(ctx, instances[0].StartTime.Location(), filter, page)
	if err != nil {
		zlog.Error(ctx, "failed to ListSessions",
			zap.Any("filter", filter),
			zap.Error(err))
		return nil, nil, status.Errorf(codes.Internal, "failed to List session: %v", err)
	}

	return sessions, pageRsp, nil
}

func buildGroupClassInstance(opt *do.GroupClassInstanceCreateOpt, setting *do.ServiceDO,
	preference *organizationpb.CompanyPreferenceSettingModel) (*do.GroupClassInstanceDO, error) {
	startTime, err := utils.GetTimeFromGoogleDateTime(opt.StartTime)
	if err != nil {
		return nil, errors.New("failed to ParseTime: " + err.Error())
	}
	instance := &do.GroupClassInstanceDO{
		CompanyID:  opt.CompanyID,
		BusinessID: opt.BusinessID,
		ServiceID:  opt.GroupClassID,
		StaffID:    opt.StaffID,
		Name:       generateName(opt.Occurrence, setting.NumSessions, startTime, preference),
		Price:      moneyutils.FromFloat(setting.Price, "USD"), // default usd, now only support usd
		TaxID:      setting.TaxId,
		StartTime:  &startTime,
		Capacity:   int64(setting.Capacity),
		Occurrence: opt.Occurrence,
		Status:     offeringpb.GroupClassInstance_UPCOMING,
	}
	now := time.Now().In(instance.StartTime.Location())
	if instance.StartTime.Year() == now.Year() &&
		instance.StartTime.Month() == now.Month() &&
		instance.StartTime.Day() == now.Day() {
		instance.Status = offeringpb.GroupClassInstance_IN_PROGRESS
	}
	return instance, nil
}

func generateName(occurrence *offeringpb.GroupClassInstance_Occurrence, numSessions int32, startTime time.Time,
	preference *organizationpb.CompanyPreferenceSettingModel) string {
	s := strings.Builder{}
	if numSessions == 1 {
		s.WriteString(startTime.Weekday().String())
	} else {
		switch occurrence.GetType() {
		case offeringpb.GroupClassInstance_Occurrence_DAY:
			s.WriteString(generateNameDay(occurrence))
		case offeringpb.GroupClassInstance_Occurrence_WEEK:
			s.WriteString(generateNameWeek(occurrence))
		case offeringpb.GroupClassInstance_Occurrence_MONTH:
			s.WriteString(generateNameMonth(occurrence))
		}
	}

	s.WriteString(", ")
	switch preference.TimeFormatType {
	case organizationpb.TimeFormat_HOUR_24:
		s.WriteString(startTime.Format("15:04"))
	case organizationpb.TimeFormat_HOUR_12:
		s.WriteString(startTime.Format(time.Kitchen))
	}
	return s.String()
}

func generateNameDay(occurrence *offeringpb.GroupClassInstance_Occurrence) string {
	if occurrence.GetInterval() == 1 {
		return "Daily"
	}
	return fmt.Sprintf("Every %d days", occurrence.GetInterval())
}

var daysOfWeek = map[dayofweek.DayOfWeek]string{
	dayofweek.DayOfWeek_MONDAY:    "Monday",
	dayofweek.DayOfWeek_TUESDAY:   "Tuesday",
	dayofweek.DayOfWeek_WEDNESDAY: "Wednesday",
	dayofweek.DayOfWeek_THURSDAY:  "Thursday",
	dayofweek.DayOfWeek_FRIDAY:    "Friday",
	dayofweek.DayOfWeek_SATURDAY:  "Saturday",
	dayofweek.DayOfWeek_SUNDAY:    "Sunday",
}

func generateNameWeek(occurrence *offeringpb.GroupClassInstance_Occurrence) string {
	if len(occurrence.GetWeek().GetDayOfWeeks()) == 0 {
		return ""
	}
	s := strings.Builder{}
	if occurrence.GetInterval() == 1 {
		s.WriteString("Weekly on ")
	} else {
		s.WriteString(fmt.Sprintf("Every %d weeks on ", occurrence.GetInterval()))
	}

	s.WriteString(daysOfWeek[occurrence.GetWeek().GetDayOfWeeks()[0]])

	if len(occurrence.GetWeek().GetDayOfWeeks()) == 1 {
		return s.String()
	}

	for i := 1; i < len(occurrence.GetWeek().GetDayOfWeeks())-1; i++ {
		s.WriteString(", ")
		s.WriteString(daysOfWeek[occurrence.GetWeek().GetDayOfWeeks()[i]])
	}
	s.WriteString(" and ")
	s.WriteString(daysOfWeek[occurrence.GetWeek().GetDayOfWeeks()[len(occurrence.GetWeek().GetDayOfWeeks())-1]])
	return s.String()
}

func generateNameMonth(occurrence *offeringpb.GroupClassInstance_Occurrence) string {
	s := strings.Builder{}
	if occurrence.GetInterval() == 1 {
		s.WriteString("Monthly on ")
	} else {
		s.WriteString(fmt.Sprintf("Every %d months on ", occurrence.GetInterval()))
	}
	switch occurrence.GetMonth().GetSelectionType() {
	case offeringpb.GroupClassInstance_Occurrence_Month_ON_25TH:
		s.WriteString("day 25")
	case offeringpb.GroupClassInstance_Occurrence_Month_ON_THIRD_WEDNESDAY:
		s.WriteString("the third Wednesday")
	}
	return s.String()
}

func buildGroupClassSessions(instance *do.GroupClassInstanceDO, setting *do.ServiceDO) []*do.GroupClassSessionDO {
	numSessions := setting.NumSessions
	startTimes := generateStartTimes(*instance.StartTime, instance.Occurrence, int(numSessions))
	if len(startTimes) != int(numSessions) {
		return nil
	}

	sessions := make([]*do.GroupClassSessionDO, 0, numSessions)
	for i := 0; i < int(numSessions); i++ {
		sessions = append(sessions, &do.GroupClassSessionDO{
			CompanyID:  instance.CompanyID,
			BusinessID: instance.BusinessID,
			InstanceID: instance.ID,
			StartTime:  startTimes[i],
			Duration:   time.Duration(setting.DurationSessionMin) * time.Minute,
			IsModified: false,
		})
	}

	return sessions
}

func generateStartTimes(starTime time.Time, occurrence *offeringpb.GroupClassInstance_Occurrence, num int) []*time.Time {
	switch occurrence.GetType() {
	case offeringpb.GroupClassInstance_Occurrence_DAY:
		return generateStartTimesDay(starTime, occurrence, num)
	case offeringpb.GroupClassInstance_Occurrence_WEEK:
		return generateStartTimesWeek(starTime, occurrence, num)
	case offeringpb.GroupClassInstance_Occurrence_MONTH:
		return generateStartTimesMonth(starTime, occurrence, num)
	}
	return nil
}

func generateStartTimesDay(startTime time.Time, occurrence *offeringpb.GroupClassInstance_Occurrence, num int) []*time.Time {
	startTimes := make([]*time.Time, 0, num)

	t := startTime
	startTimes = append(startTimes, &t)
	for i := 1; i < num; i++ {
		startTime = startTime.AddDate(0, 0, int(occurrence.GetInterval()))
		t := startTime
		startTimes = append(startTimes, &t)
	}
	return startTimes
}

var weekdays = map[dayofweek.DayOfWeek]time.Weekday{
	dayofweek.DayOfWeek_MONDAY:    time.Monday,
	dayofweek.DayOfWeek_TUESDAY:   time.Tuesday,
	dayofweek.DayOfWeek_WEDNESDAY: time.Wednesday,
	dayofweek.DayOfWeek_THURSDAY:  time.Thursday,
	dayofweek.DayOfWeek_FRIDAY:    time.Friday,
	dayofweek.DayOfWeek_SATURDAY:  time.Saturday,
	dayofweek.DayOfWeek_SUNDAY:    time.Sunday,
}

func generateStartTimesWeek(startTime time.Time, occurrence *offeringpb.GroupClassInstance_Occurrence, num int) []*time.Time {
	if len(occurrence.GetWeek().GetDayOfWeeks()) == 0 {
		return nil
	}

	weekdaysMap := make(map[time.Weekday]bool, len(occurrence.GetWeek().GetDayOfWeeks()))
	for _, weekday := range occurrence.GetWeek().GetDayOfWeeks() {
		weekdaysMap[weekdays[weekday]] = true
	}

	startTimes := make([]*time.Time, 0, num)

	currentWeekStart := startTime
	for len(startTimes) < num {
		if weekdaysMap[startTime.Weekday()] {
			t := startTime
			startTimes = append(startTimes, &t)
		}

		startTime = startTime.AddDate(0, 0, 1)
		if startTime.Sub(currentWeekStart) >= 7*24*time.Hour {
			currentWeekStart = currentWeekStart.AddDate(0, 0, 7*int(occurrence.GetInterval()))
			startTime = currentWeekStart
		}
	}
	return startTimes
}

func generateStartTimesMonth(startTime time.Time, occurrence *offeringpb.GroupClassInstance_Occurrence, num int) []*time.Time {
	switch occurrence.GetMonth().GetSelectionType() {
	case offeringpb.GroupClassInstance_Occurrence_Month_ON_25TH:
		return generateNthDayOfMonth(startTime, 25, int(occurrence.GetInterval()), num)
	case offeringpb.GroupClassInstance_Occurrence_Month_ON_THIRD_WEDNESDAY:
		return generateWeekdayOfMonth(startTime, 3, time.Wednesday, int(occurrence.GetInterval()), num)
	}
	return nil
}

func generateNthDayOfMonth(startTime time.Time, nthDay int, interval int, num int) []*time.Time {
	// Adjust the start time to the next month if the day of the month is greater than nthDay
	if startTime.Day() > nthDay {
		startTime = startTime.AddDate(0, 1, 0)
	}
	startTime = time.Date(startTime.Year(), startTime.Month(), 1,
		startTime.Hour(), startTime.Minute(), startTime.Second(), startTime.Nanosecond(),
		startTime.Location())

	startTimes := make([]*time.Time, 0, num)

	currentTime := startTime
	for len(startTimes) < num {
		year, month := currentTime.Year(), currentTime.Month()
		daysInMonth := time.Date(year, month+1, 0, 0, 0, 0, 0, currentTime.Location()).Day()
		// if nthDay is greater than the number of days in the month, set it to the last day of the month
		if nthDay > daysInMonth {
			currentTime = time.Date(year, month, daysInMonth,
				currentTime.Hour(), currentTime.Minute(), currentTime.Second(), currentTime.Nanosecond(),
				currentTime.Location())
		} else {
			currentTime = time.Date(year, month, nthDay,
				currentTime.Hour(), currentTime.Minute(), currentTime.Second(), currentTime.Nanosecond(),
				currentTime.Location())
		}

		t := currentTime
		startTimes = append(startTimes, &t)

		currentTime = currentTime.AddDate(0, interval, 0)
	}

	return startTimes
}

func generateWeekdayOfMonth(startTime time.Time, nth int, weekday time.Weekday, interval int, num int) []*time.Time {
	// calculate the nth weekday of the month
	t := time.Date(startTime.Year(), startTime.Month(), 1,
		startTime.Hour(), startTime.Minute(), startTime.Second(), startTime.Nanosecond(),
		startTime.Location())
	for t.Weekday() != weekday {
		t = t.AddDate(0, 0, 1)
	}
	for i := 0; i < nth-1; i++ {
		t = t.AddDate(0, 0, 7)
	}
	// adjust the start time to the next month if the weekday is greater than the nth weekday
	if startTime.Compare(t) > 0 {
		startTime = time.Date(startTime.Year(), startTime.Month()+1, 1,
			startTime.Hour(), startTime.Minute(), startTime.Second(), startTime.Nanosecond(),
			startTime.Location())
	}

	startTimes := make([]*time.Time, 0, num)
	for len(startTimes) < num {
		t := getNthWeekdayOfMonth(startTime, nth, weekday)
		startTimes = append(startTimes, &t)
		startTime = startTime.AddDate(0, interval, 0)
	}
	return startTimes
}

func getNthWeekdayOfMonth(t time.Time, nth int, weekday time.Weekday) time.Time {
	firstOccurrence := time.Date(t.Year(), t.Month(), 1,
		t.Hour(), t.Minute(), t.Second(), t.Nanosecond(),
		t.Location())
	for firstOccurrence.Weekday() != weekday {
		firstOccurrence = firstOccurrence.AddDate(0, 0, 1)
	}
	for i := 0; i < nth-1; i++ {
		firstOccurrence = firstOccurrence.AddDate(0, 0, 7)
	}
	// if the first occurrence is in a different month than the current time,
	// return the last day of the month
	if firstOccurrence.Month() != t.Month() {
		return time.Date(t.Year(), t.Month()+1, 0,
			t.Hour(), t.Minute(), t.Second(), t.Nanosecond(),
			t.Location())
	}
	return firstOccurrence
}

func validateUpdateSessionInput(id int64, opt *do.GroupClassSessionUpdateOpt,
	sessions []*do.GroupClassSessionDO) error {
	idx := -1
	for i, session := range sessions {
		if session.ID == id {
			idx = i
			break
		}
	}
	if idx == -1 {
		return status.Error(codes.Unknown, "not found in the same instance'sessions")
	}

	session := sessions[idx]
	startTime := session.StartTime
	duration := session.Duration
	if opt.StartTime != nil {
		startTime = opt.StartTime
	}
	if opt.Duration != nil {
		duration = *opt.Duration
	}

	if idx > 0 {
		prev := sessions[idx-1]
		if prev.StartTime.Add(prev.Duration).After(*startTime) {
			return status.Error(codes.InvalidArgument, "start before prev session")
		}
	}
	if idx+1 < len(sessions) {
		next := sessions[idx+1]
		endTime := startTime.Add(duration)
		if endTime.After(*next.StartTime) {
			return status.Error(codes.InvalidArgument, "end after next session")
		}
	}

	return nil
}
