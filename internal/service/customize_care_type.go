package service

import (
	"context"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/clients"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"time"
)

type CustomizeCareTypeHandler interface {
	List(ctx context.Context, companyId int64, filter *offeringsvcpb.WhiteListFilter) ([]*do.CustomizeCareTypeDO, error)
	Update(ctx context.Context, req *UpdateCustomizeCareTypeRequest) error
}

type customizeCareTypeHandler struct {
	companyClient               clients.CompanyClient
	customizeCareTypeRepository repository.CustomizeCareTypeRepository
}

type UpdateCustomizeCareTypeRequest struct {
	ServiceItemType offeringpb.ServiceItemType
	CompanyId       int64
	Name            string
	StaffId         int64
	Filter          *offeringsvcpb.WhiteListFilter
}

func NewCustomizeCareTypeHandler(customizeCareTypeRepository repository.CustomizeCareTypeRepository) CustomizeCareTypeHandler {
	return &customizeCareTypeHandler{
		companyClient:               clients.NewCompanyClient(resource.GetCompanyServiceClient()),
		customizeCareTypeRepository: customizeCareTypeRepository,
	}
}

func (c customizeCareTypeHandler) List(
	ctx context.Context, companyId int64, filter *offeringsvcpb.WhiteListFilter) ([]*do.CustomizeCareTypeDO, error) {
	opt := &models.CustomizeCareTypeWhereOpt{
		CompanyID: &companyId,
	}

	return c.customizeCareTypeRepository.List(ctx, opt, filter)
}

func (c customizeCareTypeHandler) Update(
	ctx context.Context, req *UpdateCustomizeCareTypeRequest) error {
	// 检查名称是否重复
	isDuplicate, err := c.customizeCareTypeRepository.IsNameDuplicate(ctx, req.CompanyId, req.Name, req.ServiceItemType, req.Filter)
	if err != nil {
		return status.Errorf(codes.Internal, "failed to check name duplicate: %v", err)
	}
	if isDuplicate {
		return status.Errorf(codes.AlreadyExists, "care type name '%s' already exists", req.Name)
	}

	whereOpt := &models.CustomizeCareTypeWhereOpt{
		ServiceItemType: req.ServiceItemType,
		CompanyID:       &req.CompanyId,
	}
	updateOpt := &models.CustomizeCareTypeUpdateOpt{
		Name:      &req.Name,
		UpdatedBy: &req.StaffId,
		UpdatedAt: time.Now(),
	}
	return c.customizeCareTypeRepository.Update(ctx, whereOpt, updateOpt)
}
