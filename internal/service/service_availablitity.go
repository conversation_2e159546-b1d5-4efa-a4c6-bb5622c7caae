package service

import (
	"context"

	customerModelV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/clients"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

type ServiceAvailabilityHandler interface {
	RemovePetTypeFilter(ctx context.Context, companyId int64, petType customerModelV1.PetType) error
	RemovePetBreedFilter(ctx context.Context, companyId int64, petBreed string) error
	RemovePetSizeFilter(ctx context.Context, companyId int64, petSizeId int64) error
	RemovePetCoatTypeFilter(ctx context.Context, companyId int64, petCoatTypeId int64) error
	RemoveServiceFilter(ctx context.Context, companyId int64, serviceId int64) error
	RemoveLodgingFilter(ctx context.Context, companyId int64, lodgingTypeId int64) error
	RemoveStaffFilter(ctx context.Context, companyId int64, staffId int64) error

	BatchGetAvailableStaffIds(ctx context.Context, companyId int64, businessId *int64, serviceIds []int64) (map[int64]do.ServiceStaffAvailabilityRule, error)
}

func (s serviceAvailabilityHandler) RemovePetTypeFilter(ctx context.Context, companyId int64, petType customerModelV1.PetType) error {
	return s.serviceAvailabilityRepository.RemovePetTypeFilter(ctx, companyId, petType)
}

func (s serviceAvailabilityHandler) RemovePetBreedFilter(ctx context.Context, companyId int64, petBreed string) error {
	return s.serviceAvailabilityRepository.RemovePetBreedFilter(ctx, companyId, petBreed)
}

func (s serviceAvailabilityHandler) RemovePetSizeFilter(ctx context.Context, companyId int64, petSizeId int64) error {
	return s.serviceAvailabilityRepository.RemovePetSizeFilter(ctx, companyId, petSizeId)
}

func (s serviceAvailabilityHandler) RemovePetCoatTypeFilter(ctx context.Context, companyId int64, petCoatTypeId int64) error {
	return s.serviceAvailabilityRepository.RemovePetCoatTypeFilter(ctx, companyId, petCoatTypeId)
}

func (s serviceAvailabilityHandler) RemoveServiceFilter(ctx context.Context, companyId int64, serviceId int64) error {
	if err := s.serviceAvailabilityRepository.RemoveServiceFilter(ctx, companyId, serviceId); err != nil {
		return err
	}

	// delete service override
	if err := s.serviceStaffOverrideRuleRepository.RemoveServiceStaffOverrideRules(ctx, companyId, serviceId); err != nil {
		return err
	}

	// delete service staff availability
	if err := s.serviceStaffAvailabilityRepository.RemoveServiceStaffAvailability(ctx, serviceId); err != nil {
		return err
	}

	return nil
}

func (s serviceAvailabilityHandler) RemoveStaffFilter(ctx context.Context, companyId int64, staffId int64) error {
	if err := s.serviceStaffAvailabilityRepository.RemoveServiceStaffAvailabilityByStaffIds(ctx, 0, []int64{staffId}); err != nil {
		return err
	}
	if err := s.serviceStaffOverrideRuleRepository.RemoveServiceStaffOverrideRulesByStaffIds(ctx, companyId, []int64{staffId}); err != nil {
		return err
	}

	return nil
}

func (s serviceAvailabilityHandler) RemoveLodgingFilter(ctx context.Context, companyId int64, lodgingTypeId int64) error {
	return s.serviceAvailabilityRepository.RemoveLodgingFilter(ctx, companyId, lodgingTypeId)
}

func NewServiceAvailabilityHandler() ServiceAvailabilityHandler {
	return &serviceAvailabilityHandler{
		serviceAvailabilityRepository:      repository.NewServiceAvailabilityRepository(resource.GetGroomingDB()),
		serviceRepository:                  repository.NewServiceRepository(resource.GetGroomingDB()),
		serviceStaffAvailabilityRepository: repository.NewServiceStaffAvailabilityRepository(resource.GetOfferingDB()),
		serviceStaffOverrideRuleRepository: repository.NewServiceStaffOverrideRuleRepository(resource.GetOfferingDB()),
		staffClient:                        clients.NewStaffClient(resource.GetStaffServiceClient()),
		serviceBundleSaleRepository:        repository.NewServiceBundleSaleRepository(resource.GetOfferingDB()),
	}
}

type serviceAvailabilityHandler struct {
	serviceAvailabilityRepository      repository.ServiceAvailabilityRepository
	serviceRepository                  repository.ServiceRepository
	serviceStaffAvailabilityRepository repository.ServiceStaffAvailabilityRepository
	serviceStaffOverrideRuleRepository repository.ServiceStaffOverrideRuleRepository
	serviceBundleSaleRepository        repository.ServiceBundleSaleMappingRepository
	staffClient                        clients.StaffClient
}

func (s serviceAvailabilityHandler) BatchGetAvailableStaffIds(ctx context.Context, companyId int64, businessId *int64, serviceIds []int64) (map[int64]do.ServiceStaffAvailabilityRule, error) {
	// get service brief
	serviceBriefList, err := s.serviceRepository.GetBriefServiceListWithServiceIds(ctx, serviceIds)
	if err != nil {
		return nil, err
	}

	if len(serviceBriefList) == 0 {
		return make(map[int64]do.ServiceStaffAvailabilityRule, 0), nil
	}

	serviceIdsAvailableForAllStaffs := make([]int64, 0)
	serviceIdsNotAvailableForAllStaffs := make([]int64, 0)
	result := make(map[int64]do.ServiceStaffAvailabilityRule, 0)

	for _, serviceBrief := range serviceBriefList {
		if serviceBrief.AvailableForAllStaff {
			serviceIdsAvailableForAllStaffs = append(serviceIdsAvailableForAllStaffs, serviceBrief.ServiceId)
		} else {
			serviceIdsNotAvailableForAllStaffs = append(serviceIdsNotAvailableForAllStaffs, serviceBrief.ServiceId)
		}
	}

	if len(serviceIdsAvailableForAllStaffs) > 0 {
		allStaffIds, err := s.staffClient.ListAllStaffIds(ctx, companyId, businessId)
		if err != nil {
			return nil, err
		}

		for _, serviceId := range serviceIdsAvailableForAllStaffs {
			result[serviceId] = do.ServiceStaffAvailabilityRule{
				AvailableForAllStaff: true,
				StaffIds:             allStaffIds,
			}
		}
	}

	if len(serviceIdsNotAvailableForAllStaffs) > 0 {
		staffAvailableRules, err := s.serviceStaffAvailabilityRepository.GetAvailableStaffRulesByServiceIDList(ctx, serviceIdsNotAvailableForAllStaffs)
		if err != nil {
			return nil, err
		}

		availableStaffIdsByServiceId := make(map[int64][]int64)

		for _, rule := range staffAvailableRules {
			availableStaffIdsByServiceId[rule.ServiceId] = append(availableStaffIdsByServiceId[rule.ServiceId], rule.StaffId)
		}

		for _, serviceId := range serviceIdsNotAvailableForAllStaffs {
			result[serviceId] = do.ServiceStaffAvailabilityRule{
				AvailableForAllStaff: false,
				StaffIds:             availableStaffIdsByServiceId[serviceId],
			}
		}
	}

	return result, nil
}
