package controller

import (
	"context"
	"errors"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/MoeGolibrary/go-lib/merror"
	"github.com/MoeGolibrary/go-lib/zlog"
	errorsModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/errors/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type OfferingService struct {
	offeringsvcpb.UnimplementedServiceManagementServiceServer
}

func (o OfferingService) CreateService(ctx context.Context, request *offeringsvcpb.CreateServiceRequest) (*offeringsvcpb.CreateServiceResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetTokenCompanyId()))

	handler := service.NewServiceHandler()
	createServiceDefToDO := converter.ConvertCreateServiceDefToDO(request.GetCreateServiceDef())
	serviceId, err := handler.CreateNewService(ctx, request.GetTokenCompanyId(), createServiceDefToDO)
	if err != nil {
		zlog.Error(ctx, "failed to create service", zap.Error(err))
		return nil, err
	}

	_, serviceList, err := handler.GetServiceWithFilterPaginated(ctx, request.GetTokenCompanyId(), do.ServiceQueryFilter{
		ServiceIdList: []int64{serviceId},
	}, &utilsV2.PaginationRequest{
		PageSize: proto.Int32(1),
		PageNum:  proto.Int32(1),
	}, offeringpb.ServiceOrderByType_DEFAULT)
	if err != nil {
		zlog.Error(ctx, "failed to get service", zap.Error(err))
		return nil, err
	}
	if len(serviceList) == 0 {
		zlog.Error(ctx, "failed to get service", zap.Error(err))
		return nil, errors.New("empty service")
	}

	return &offeringsvcpb.CreateServiceResponse{
		Service: converter.ConvertServiceDOToPBModel(serviceList[0]),
	}, nil
}

func (o OfferingService) UpdateService(ctx context.Context, request *offeringsvcpb.UpdateServiceRequest) (*offeringsvcpb.UpdateServiceResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetTokenCompanyId()))

	handler := service.NewServiceHandler()
	// protocbuf 生成代码的 json 都有 omitempty 标签。零值会被忽略，在目前场景下 LocationOverrideList 都会传值，所以值为 nil 时，需要手动初始化
	// nolint:staticcheck
	if request.UpdateServiceDef != nil && request.UpdateServiceDef.LocationOverrideList == nil {
		request.UpdateServiceDef.LocationOverrideList = []*offeringpb.LocationOverrideRule{}
	}
	if err := handler.UpdateService(ctx, request.GetTokenCompanyId(), converter.ConvertUpdateServiceDefToDO(request.GetUpdateServiceDef())); err != nil {
		zlog.Error(ctx, "failed to update service", zap.Error(err))
		return nil, err
	}

	_, serviceList, err := handler.GetServiceWithFilterPaginated(ctx, request.GetTokenCompanyId(), do.ServiceQueryFilter{
		ServiceIdList: []int64{request.GetUpdateServiceDef().ServiceId},
	}, &utilsV2.PaginationRequest{
		PageSize: proto.Int32(1),
		PageNum:  proto.Int32(1),
	}, offeringpb.ServiceOrderByType_DEFAULT)
	if err != nil {
		zlog.Error(ctx, "failed to get service", zap.Error(err))
		return nil, err
	}
	if len(serviceList) == 0 {
		zlog.Error(ctx, "failed to get service", zap.Error(err))
		return nil, errors.New("empty service")
	}

	return &offeringsvcpb.UpdateServiceResponse{
		Service: converter.ConvertServiceDOToPBModel(serviceList[0]),
	}, nil
}

func (o OfferingService) GetServiceList(ctx context.Context, request *offeringsvcpb.GetServiceListRequest) (*offeringsvcpb.GetServiceListResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetTokenCompanyId()))

	handler := service.NewServiceHandler()

	serviceQueryFilter := do.ServiceQueryFilter{
		Inactive:       request.Inactive,
		BusinessIDList: request.BusinessIds,
		ServiceIdList:  request.ServiceIds,
	}
	if request.ServiceItemType != nil {
		serviceQueryFilter.ServiceItemTypes = []offeringpb.ServiceItemType{*request.ServiceItemType}
	}
	if request.ServiceType != nil {
		serviceQueryFilter.ServiceTypes = []offeringpb.ServiceType{*request.ServiceType}
	}
	if request.Keyword != nil {
		serviceQueryFilter.Keyword = proto.String(fmt.Sprintf("%%%s%%", *request.Keyword))
	}
	total, serviceList, err := handler.GetServiceWithFilterPaginated(ctx, request.GetTokenCompanyId(), serviceQueryFilter, request.GetPagination(), offeringpb.ServiceOrderByType_DEFAULT)
	if err != nil {
		zlog.Error(ctx, "failed to get service", zap.Error(err))
		return nil, err
	}
	filter := do.CategoryQueryFilter{}
	if request.GetServiceType() != offeringpb.ServiceType_SERVICE_TYPE_UNSPECIFIED {
		filter.ServiceTypes = []offeringpb.ServiceType{request.GetServiceType()}
	}
	if request.GetServiceItemType() != offeringpb.ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED {
		filter.ServiceItemTypes = []offeringpb.ServiceItemType{request.GetServiceItemType()}
	}
	categoryList, err := handler.GetCategoryWithFilter(ctx, request.GetTokenCompanyId(), filter)
	if err != nil {
		zlog.Error(ctx, "failed to get category list", zap.Error(err))
		return nil, err
	}
	// 在 categoryList 中添加一个默认的 category，用于存放没有分组的 Service
	categoryList = append([]*do.Category{{
		ID: 0,
	}}, categoryList...)

	pbCategoryList := make([]*offeringpb.ServiceCategoryModel, 0, len(categoryList))
	for _, category := range categoryList {
		services := make([]*offeringpb.ServiceModel, 0)
		for _, service := range serviceList {
			if service.CategoryId == category.ID {
				services = append(services, converter.ConvertServiceDOToPBModel(service))
			}
		}
		pbCategoryList = append(pbCategoryList, &offeringpb.ServiceCategoryModel{
			CategoryId: category.ID,
			Name:       category.Name,
			Services:   services,
		})
	}
	return &offeringsvcpb.GetServiceListResponse{
		CategoryList: pbCategoryList,
		Pagination: &utilsV2.PaginationResponse{
			Total:    int32(total),
			PageSize: request.GetPagination().GetPageSize(),
			PageNum:  request.GetPagination().GetPageNum(),
		},
	}, nil
}

func (o OfferingService) GetServiceByPetAndServiceId(ctx context.Context, request *offeringsvcpb.GetServiceByPetAndServiceIdRequest) (*offeringsvcpb.GetServiceByPetAndServiceIdResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetCompanyId()))

	handler := service.NewServiceHandler()
	petIdWithServiceList, err := handler.GetServiceListWithOverrideRules(ctx, request.GetCompanyId(), request.BusinessId, converter.ConvertInt64ListMapToSlice(request.GetPetIdWithServiceIdList()))
	if err != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return nil, err
	}

	petIdWithPBServiceList := make(map[int64]*offeringpb.CustomizedServiceViewList)
	for petId, serviceList := range petIdWithServiceList {
		petIdWithPBServiceList[petId] = &offeringpb.CustomizedServiceViewList{
			Services: lo.Map(serviceList, func(service do.ServiceWithOverrideRules, _ int) *offeringpb.CustomizedServiceView {
				return converter.ConvertOverriddenServiceToPBWithCondition(
					&offeringsvcpb.CustomizedServiceQueryCondition{
						ServiceId:  service.ServiceID,
						BusinessId: request.BusinessId,
						PetId:      proto.Int64(petId),
						StaffId:    nil,
					}, service)
			}),
		}
	}

	return &offeringsvcpb.GetServiceByPetAndServiceIdResponse{
		PetIdWithAvailableServiceList: petIdWithPBServiceList,
	}, nil
}

func (o OfferingService) OverrideService(ctx context.Context, request *offeringsvcpb.OverrideServiceRequest) (*offeringsvcpb.OverrideServiceResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetCompanyId()))

	handler := service.NewServiceHandler()
	if err := handler.OverrideServiceByPet(ctx, request.GetCompanyId(), converter.ConvertPBPetOverrideToDO(request)); err != nil {
		zlog.Error(ctx, "failed to override service", zap.Error(err))
		return nil, err
	}
	return &offeringsvcpb.OverrideServiceResponse{}, nil
}

func (o OfferingService) GetApplicableServiceList(ctx context.Context, request *offeringsvcpb.GetApplicableServiceListRequest) (*offeringsvcpb.GetApplicableServiceListResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetCompanyId()))

	handler := service.NewServiceHandler()
	total, serviceList, err := handler.GetApplicableServices(ctx, request.GetCompanyId(), request.GetPetId(), request.GetServiceType(), converter.ConvertApplicableQueryToDO(request), request.GetPagination())
	if err != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return nil, err
	}
	serviceListWithCategory := lo.GroupBy(serviceList, func(service do.ServiceWithOverrideRules) int64 {
		return service.CategoryID
	})

	categoryList, err := handler.GetCategoryWithFilter(ctx, request.GetCompanyId(), do.CategoryQueryFilter{
		CategoryIdList: lo.Keys(serviceListWithCategory),
	})
	if err != nil {
		zlog.Error(ctx, "failed to get category list", zap.Error(err))
		return nil, err
	}

	pbCategoryList := make([]*offeringpb.CustomizedServiceCategoryView, 0)
	pbCategoryList = append(pbCategoryList, &offeringpb.CustomizedServiceCategoryView{
		CategoryId: 0,
		Services: lo.Map(serviceListWithCategory[0], func(service do.ServiceWithOverrideRules, _ int) *offeringpb.CustomizedServiceView {
			return converter.ConvertOverriddenServiceToPBWithCondition(
				&offeringsvcpb.CustomizedServiceQueryCondition{
					ServiceId:  service.ServiceID,
					BusinessId: request.BusinessId,
					PetId:      request.PetId,
					StaffId:    nil,
					Zipcode:    request.Zipcode,
				}, service)
		}),
	})

	for _, category := range categoryList {
		pbCategoryList = append(pbCategoryList, &offeringpb.CustomizedServiceCategoryView{
			CategoryId: category.ID,
			Name:       category.Name,
			Services: lo.Map(serviceListWithCategory[category.ID], func(service do.ServiceWithOverrideRules, _ int) *offeringpb.CustomizedServiceView {
				return converter.ConvertOverriddenServiceToPBWithCondition(
					&offeringsvcpb.CustomizedServiceQueryCondition{
						ServiceId:  service.ServiceID,
						BusinessId: request.BusinessId,
						PetId:      request.PetId,
						StaffId:    nil,
					}, service)
			}),
		})
	}

	return &offeringsvcpb.GetApplicableServiceListResponse{CategoryList: pbCategoryList, Pagination: &utilsV2.PaginationResponse{
		Total:    int32(total),
		PageSize: request.GetPagination().GetPageSize(),
		PageNum:  request.GetPagination().GetPageNum(),
	}}, nil
}

func (o OfferingService) CustomizedServiceByPet(ctx context.Context, request *offeringsvcpb.CustomizedServiceByPetRequest) (*offeringsvcpb.CustomizedServiceByPetResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetCompanyId()))

	handler := service.NewServiceHandler()
	serviceList, err := handler.GetPetCustomizedServiceList(ctx, request.GetCompanyId(), request.GetPetId())
	if err != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return nil, err
	}

	return &offeringsvcpb.CustomizedServiceByPetResponse{
		ServiceList: lo.Map(serviceList, func(service do.ServiceWithPetCustomized, _ int) *offeringpb.ServiceWithPetCustomizedInfo {
			brief := service.ServiceBrief
			serviceWithCustomizedInfo := &offeringpb.ServiceWithPetCustomizedInfo{
				ServiceId:      brief.ServiceId,
				Name:           service.Name,
				Price:          brief.Price,
				PriceUnit:      service.PriceUnit,
				Duration:       brief.Duration,
				IsSavePrice:    false,
				IsSaveDuration: false,
				CreateTime:     service.CreateTime,
				UpdateTime:     service.CreateTime,
			}

			if service.CustomizedPrice != nil {
				serviceWithCustomizedInfo.IsSavePrice = true
				serviceWithCustomizedInfo.Price = *service.CustomizedPrice
			}

			if service.CustomizedDuration != nil {
				serviceWithCustomizedInfo.IsSaveDuration = true
				serviceWithCustomizedInfo.Duration = *service.CustomizedDuration
			}

			return serviceWithCustomizedInfo
		}),
	}, nil
}

func (o OfferingService) GetServiceDetail(ctx context.Context, request *offeringsvcpb.GetServiceDetailRequest) (*offeringsvcpb.GetServiceDetailResponse, error) {
	handler := service.NewServiceHandler()

	serviceDetail, err := handler.GetServiceDetail(ctx, request.GetServiceId())
	if err != nil {
		zlog.Error(ctx, "failed to get service", zap.Error(err))
		return nil, err
	}

	if request.CompanyId != nil && serviceDetail.CompanyId != request.GetCompanyId() {
		return nil, merror.NewBizError(errorsModelsV1.Code_CODE_SERVICE_NOT_FOUND, fmt.Sprintf("service[%d] not found for company[%d]", request.GetServiceId(), request.GetCompanyId()))
	}

	return &offeringsvcpb.GetServiceDetailResponse{
		Service: converter.ConvertServiceDOToPBModel(serviceDetail),
	}, nil
}

func (o OfferingService) GetServiceListByIds(ctx context.Context, request *offeringsvcpb.GetServiceListByIdsRequest) (*offeringsvcpb.GetServiceListByIdsResponse, error) {
	if len(request.GetServiceIds()) == 0 {
		return &offeringsvcpb.GetServiceListByIdsResponse{
			Services: make([]*offeringpb.ServiceBriefView, 0),
		}, nil
	}

	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetCompanyId()))

	handler := service.NewServiceHandler()
	serviceBriefList, err := handler.GetServiceBriefListByIds(ctx, request.GetServiceIds())
	if err != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return nil, err
	}

	return &offeringsvcpb.GetServiceListByIdsResponse{
		Services: lo.Map(serviceBriefList, func(serviceBrief do.ServiceBrief, _ int) *offeringpb.ServiceBriefView {
			return &offeringpb.ServiceBriefView{
				Id:                         serviceBrief.ServiceId,
				CategoryId:                 serviceBrief.CategoryId,
				Type:                       serviceBrief.ServiceType,
				ServiceItemType:            serviceBrief.ServiceItemType,
				Name:                       serviceBrief.Name,
				Description:                serviceBrief.Description,
				Price:                      serviceBrief.Price,
				PriceUnit:                  serviceBrief.PriceUnit,
				Duration:                   serviceBrief.Duration,
				CreateTime:                 &timestamppb.Timestamp{Seconds: serviceBrief.CreateTime},
				UpdateTime:                 &timestamppb.Timestamp{Seconds: serviceBrief.UpdateTime},
				Inactive:                   serviceBrief.Inactive,
				IsDeleted:                  serviceBrief.IsDeleted,
				ColorCode:                  serviceBrief.ColorCode,
				MaxDuration:                serviceBrief.MaxDuration,
				RequireDedicatedStaff:      serviceBrief.RequireDedicatedStaff,
				LodgingFilter:              serviceBrief.LodgingFilter,
				CustomizedLodgings:         serviceBrief.CustomizedLodgings,
				NumSessions:                serviceBrief.NumSessions,
				DurationSessionMin:         serviceBrief.DurationSessionMin,
				Capacity:                   serviceBrief.Capacity,
				IsRequirePrerequisiteClass: serviceBrief.IsRequirePrerequisiteClass,
				PrerequisiteClassIds:       serviceBrief.PrerequisiteClassIds,
				IsEvaluationRequired:       serviceBrief.IsEvaluationRequired,
				IsEvaluationRequiredForOb:  serviceBrief.IsEvaluationRequiredForOb,
				EvaluationId:               serviceBrief.EvaluationId,
				TaxId:                      serviceBrief.TaxId,
				CompanyId:                  serviceBrief.CompanyId,
			}
		}),
	}, nil
}

func (o OfferingService) RemoveServiceFilter(ctx context.Context, request *offeringsvcpb.RemoveServiceFilterRequest) (*offeringsvcpb.RemoveServiceFilterResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetCompanyId()))

	handler := service.NewServiceAvailabilityHandler()

	var err error
	switch filter := request.GetFilter().(type) {
	case *offeringsvcpb.RemoveServiceFilterRequest_PetType:
		err = handler.RemovePetTypeFilter(ctx, request.GetCompanyId(), filter.PetType)
	case *offeringsvcpb.RemoveServiceFilterRequest_PetBreed:
		err = handler.RemovePetBreedFilter(ctx, request.GetCompanyId(), filter.PetBreed)
	case *offeringsvcpb.RemoveServiceFilterRequest_PetSizeId:
		err = handler.RemovePetSizeFilter(ctx, request.GetCompanyId(), filter.PetSizeId)
	case *offeringsvcpb.RemoveServiceFilterRequest_PetCoatTypeId:
		err = handler.RemovePetCoatTypeFilter(ctx, request.GetCompanyId(), filter.PetCoatTypeId)
	case *offeringsvcpb.RemoveServiceFilterRequest_LodgingTypeId:
		err = handler.RemoveLodgingFilter(ctx, request.GetCompanyId(), filter.LodgingTypeId)
	case *offeringsvcpb.RemoveServiceFilterRequest_ServiceId:
		err = handler.RemoveServiceFilter(ctx, request.GetCompanyId(), filter.ServiceId)
	case *offeringsvcpb.RemoveServiceFilterRequest_StaffId:
		err = handler.RemoveStaffFilter(ctx, request.GetCompanyId(), filter.StaffId)
	default:
		zlog.Error(ctx, "unknown filter type", zap.Any("filter", filter))
	}

	if err != nil {
		zlog.Error(ctx, "failed to remove service filter", zap.Error(err))
		return nil, err
	}

	return &offeringsvcpb.RemoveServiceFilterResponse{}, nil
}

func (o OfferingService) GetServiceItemTypes(ctx context.Context, request *offeringsvcpb.GetServiceItemTypesRequest) (*offeringsvcpb.GetServiceItemTypesResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetCompanyId()))

	handler := service.NewServiceHandler()
	serviceItemTypes, err := handler.GetSupportedServiceItemTypes(ctx, request.GetCompanyId())
	if err != nil {
		zlog.Error(ctx, "failed to get normal service item types", zap.Error(err))
		return nil, err
	}

	return &offeringsvcpb.GetServiceItemTypesResponse{
		ServiceItemTypes: serviceItemTypes,
	}, nil
}

func (o OfferingService) ListService(ctx context.Context, request *offeringsvcpb.ListServiceRequest) (*offeringsvcpb.ListServiceResponse, error) {
	handler := service.NewServiceHandler()

	serviceQueryFilter := do.ServiceQueryFilter{
		BusinessIDList:            request.BusinessIds,
		Inactive:                  request.Inactive,
		PrerequisiteClassIds:      request.PrerequisiteClassIds,
		FilterPrerequisiteClasses: request.FilterPrerequisiteClasses,
	}
	// nolint:staticcheck
	if request.ServiceItemType != nil {
		serviceQueryFilter.ServiceItemTypes = []offeringpb.ServiceItemType{*request.ServiceItemType}
	}
	if len(request.ServiceItemTypes) > 0 {
		serviceQueryFilter.ServiceItemTypes = request.ServiceItemTypes
	}
	if request.ServiceType != nil {
		serviceQueryFilter.ServiceTypes = []offeringpb.ServiceType{*request.ServiceType}
	}

	total, services, err := handler.GetServiceWithFilterPaginated(ctx, request.GetTokenCompanyId(), serviceQueryFilter, request.GetPagination(), request.GetOrderBy())
	if err != nil {
		return nil, err
	}

	return &offeringsvcpb.ListServiceResponse{
		Services: lo.Map(services, func(service *do.ServiceDO, _ int) *offeringpb.ServiceModel {
			return converter.ConvertServiceDOToPBModel(service)
		}),
		Pagination: &utilsV2.PaginationResponse{
			Total:    int32(total),
			PageSize: request.GetPagination().GetPageSize(),
			PageNum:  request.GetPagination().GetPageNum(),
		},
	}, nil
}

func (o OfferingService) BatchGetCustomizedService(ctx context.Context, request *offeringsvcpb.BatchGetCustomizedServiceRequest) (*offeringsvcpb.BatchGetCustomizedServiceResponse, error) {
	handler := service.NewServiceHandler()

	servicesWithOverrideRules, err := handler.BatchGetOverriddenServices(ctx, request.GetCompanyId(), converter.ConvertServiceOverrideConditionToDO(request.GetQueryConditionList()))
	if err != nil {
		return nil, err
	}

	result := converter.ConvertServiceWithOverrideInfoToPB(request.GetQueryConditionList(), servicesWithOverrideRules)
	return &offeringsvcpb.BatchGetCustomizedServiceResponse{
		CustomizedServiceList: result,
	}, nil
}

func (o OfferingService) ListAvailableStaffId(ctx context.Context, request *offeringsvcpb.ListAvailableStaffIdRequest) (*offeringsvcpb.ListAvailableStaffIdResponse, error) {
	handler := service.NewServiceAvailabilityHandler()
	availableStaffRuleByServiceId, err := handler.BatchGetAvailableStaffIds(ctx, request.GetCompanyId(), proto.Int64(request.GetBusinessId()), request.GetServiceIds())
	if err != nil {
		return nil, err
	}

	serviceIdToStaffIdList := make(map[int64]*offeringsvcpb.ListAvailableStaffIdResponse_StaffIds)
	for serviceId, availableStaffRule := range availableStaffRuleByServiceId {
		serviceIdToStaffIdList[serviceId] = converter.ConvertAvailableStaffRuleDOToPB(availableStaffRule)
	}

	return &offeringsvcpb.ListAvailableStaffIdResponse{
		ServiceIdToStaffIds: serviceIdToStaffIdList,
	}, nil
}

func (o OfferingService) ListBundleServices(ctx context.Context, request *offeringsvcpb.ListBundleServicesRequest) (*offeringsvcpb.ListBundleServicesResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyID", request.GetCompanyId()))

	handler := service.NewServiceHandler()
	bundleSales, err := handler.GetServiceBundleSaleList(ctx, request.GetCompanyId(), request.GetServiceIds())
	if err != nil {
		zlog.Error(ctx, "failed to get bundle sale list", zap.Error(err))
		return nil, err
	}

	return &offeringsvcpb.ListBundleServicesResponse{
		BundleServices: lo.Map(bundleSales, func(bundleSale *models.ServiceBundleSaleMapping, _ int) *offeringpb.ServiceBundleSaleMappingModel {
			return &offeringpb.ServiceBundleSaleMappingModel{
				ServiceId:       bundleSale.ServiceID,
				BundleServiceId: bundleSale.BundleServiceID,
			}
		}),
	}, nil
}

func (o OfferingService) ListCategories(ctx context.Context, req *offeringsvcpb.ListCategoriesRequest) (
	*offeringsvcpb.ListCategoriesResponse, error) {
	handler := service.NewServiceHandler()
	categories, err := handler.GetCategoryWithFilter(ctx, req.GetCompanyId(), do.CategoryQueryFilter{
		CategoryIdList:   req.GetFilter().GetIds(),
		ServiceTypes:     req.GetFilter().GetServiceTypes(),
		ServiceItemTypes: req.GetFilter().GetServiceItemTypes(),
	})
	if err != nil {
		zlog.Error(ctx, "failed to get category list", zap.Error(err))
		return nil, err
	}
	rsp := &offeringsvcpb.ListCategoriesResponse{
		Categories: make([]*offeringpb.CategoryModel, 0, len(categories)),
	}
	for _, c := range categories {
		rsp.Categories = append(rsp.Categories, &offeringpb.CategoryModel{
			Id:              c.ID,
			Name:            c.Name,
			ServiceItemType: c.ItemType,
			Sort:            c.Sort,
			ServiceType:     c.Type,
		})
	}
	return rsp, nil
}

func (o OfferingService) CreateCategories(ctx context.Context, req *offeringsvcpb.CreateCategoriesRequest) (
	*offeringsvcpb.CreateCategoriesResponse, error) {
	handler := service.NewServiceHandler()
	dos := make([]*do.Category, 0, len(req.GetCategories()))
	for _, c := range req.GetCategories() {
		dos = append(dos, &do.Category{
			Name:      c.GetName(),
			Type:      c.GetServiceType(),
			Sort:      int32(c.GetSort()),
			CompanyID: req.GetCompanyId(),
			ItemType:  c.GetServiceItemType(),
		})
	}
	created, err := handler.CreateCategories(ctx, dos)
	if err != nil {
		zlog.Error(ctx, "failed to get category list", zap.Error(err))
		return nil, err
	}
	rsp := &offeringsvcpb.CreateCategoriesResponse{
		Categories: make([]*offeringpb.CategoryModel, 0, len(created)),
	}
	for _, c := range created {
		rsp.Categories = append(rsp.Categories, &offeringpb.CategoryModel{
			Id:              c.ID,
			Name:            c.Name,
			ServiceItemType: c.ItemType,
			Sort:            c.Sort,
			ServiceType:     c.Type,
		})
	}
	return rsp, nil
}

func (o OfferingService) GetMaxServicePriceByLodgingType(ctx context.Context, request *offeringsvcpb.GetMaxServicePriceByLodgingTypeRequest) (*offeringsvcpb.GetMaxServicePriceByLodgingTypeResponse, error) {
	handler := service.NewServiceHandler()
	price, err := handler.GetMaxServicePriceByLodgingType(ctx, request.GetCompanyId(), request.GetBusinessId(), request.GetLodgingTypeId())
	if err != nil {
		return nil, err
	}

	return &offeringsvcpb.GetMaxServicePriceByLodgingTypeResponse{
		MaxPrice: price,
	}, nil
}

func NewOfferingService() offeringsvcpb.ServiceManagementServiceServer {
	return &OfferingService{}
}
