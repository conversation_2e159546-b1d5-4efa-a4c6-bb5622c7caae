package controller

import (
	"context"

	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

type ServiceStaffOverrideRuleService struct {
	offeringsvcpb.UnimplementedServiceStaffOverrideRuleServiceServer

	serviceStaffOverrideRuleRepository repository.ServiceStaffOverrideRuleRepository
}

func NewServiceStaffOverrideRuleService() *ServiceStaffOverrideRuleService {
	return &ServiceStaffOverrideRuleService{serviceStaffOverrideRuleRepository: repository.NewServiceStaffOverrideRuleRepository(resource.GetOfferingDB())}
}

func (s ServiceStaffOverrideRuleService) ListServiceStaffOverrideRule(ctx context.Context, in *offeringsvcpb.ListServiceStaffOverrideRuleRequest) (*offeringsvcpb.ListServiceStaffOverrideRuleResponse, error) {

	if len(in.GetServiceIds()) == 0 {
		return &offeringsvcpb.ListServiceStaffOverrideRuleResponse{}, nil
	}

	serviceIdToRuleList, err := s.serviceStaffOverrideRuleRepository.GetStaffOverrideRules(ctx, in.CompanyId, &in.BusinessId, in.GetServiceIds())
	if err != nil {
		return nil, err
	}

	rules := make([]*offeringsvcpb.ListServiceStaffOverrideRuleResponse_ServiceStaffOverrideRule, 0)
	for serviceId, ruleList := range serviceIdToRuleList {
		for _, rule := range ruleList {
			rules = append(rules, &offeringsvcpb.ListServiceStaffOverrideRuleResponse_ServiceStaffOverrideRule{
				ServiceId: serviceId,
				StaffId:   rule.StaffID,
				Price:     rule.Price,
				Duration:  rule.Duration,
			})
		}
	}

	return &offeringsvcpb.ListServiceStaffOverrideRuleResponse{Rules: rules}, nil
}
