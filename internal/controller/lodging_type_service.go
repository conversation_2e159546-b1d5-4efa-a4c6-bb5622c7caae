package controller

import (
	"context"

	"github.com/pkg/errors"

	offeringServiceV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type LodgingTypeService struct {
	handler service.LodgingTypeHandler
	offeringServiceV1.UnimplementedLodgingTypeServiceServer
}

func (s LodgingTypeService) CreateLodgingType(ctx context.Context, request *offeringServiceV1.CreateLodgingTypeRequest) (*offeringServiceV1.CreateLodgingTypeResponse, error) {
	lodgingTypeDO := converter.ConvertCreateLodgingTypeRequestToDO(request)
	err := s.handler.CreateLodgingType(ctx, lodgingTypeDO)
	return &offeringServiceV1.CreateLodgingTypeResponse{LodgingType: converter.ConvertLodgingTypeDOToPB(lodgingTypeDO)}, err
}

func (s LodgingTypeService) UpdateLodgingType(ctx context.Context, request *offeringServiceV1.UpdateLodgingTypeRequest) (*offeringServiceV1.UpdateLodgingTypeResponse, error) {
	updateOpt := converter.ConvertUpdateLodgingTypeRequestToUpdateOpt(request)
	lodgingTypeDO, err := s.handler.UpdateLodgingType(ctx, request.GetId(), request.CompanyId, updateOpt)
	return &offeringServiceV1.UpdateLodgingTypeResponse{LodgingType: converter.ConvertLodgingTypeDOToPB(lodgingTypeDO)}, err
}

func (s LodgingTypeService) DeleteLodgingType(ctx context.Context, request *offeringServiceV1.DeleteLodgingTypeRequest) (*offeringServiceV1.DeleteLodgingTypeResponse, error) {
	err := s.handler.DeleteLodgingType(ctx, request.GetId(), request.CompanyId, request.GetTokenStaffId())
	return &offeringServiceV1.DeleteLodgingTypeResponse{}, err
}

func (s LodgingTypeService) GetLodgingTypeList(ctx context.Context, request *offeringServiceV1.GetLodgingTypeListRequest) (*offeringServiceV1.GetLodgingTypeListResponse, error) {
	response := &offeringServiceV1.GetLodgingTypeListResponse{}
	dos, err := s.handler.ListLodgingType(ctx, request.GetCompanyId())
	if err != nil {
		return response, err
	}
	response.LodgingTypeList = converter.ConvertLodgingTypeDOListToPB(dos)
	return response, nil
}

func (s LodgingTypeService) MGetLodgingType(ctx context.Context, request *offeringServiceV1.MGetLodgingTypeRequest) (*offeringServiceV1.MGetLodgingTypeResponse, error) {
	response := &offeringServiceV1.MGetLodgingTypeResponse{}
	dos, err := s.handler.MGetLodgingType(ctx, request.GetIdList())
	if err != nil {
		return response, err
	}
	response.LodgingTypeList = converter.ConvertLodgingTypeDOListToPB(dos)
	return response, nil
}

func (s LodgingTypeService) SortLodgingTypeByIds(ctx context.Context, request *offeringServiceV1.SortLodgingTypeByIdsRequest) (*offeringServiceV1.SortLodgingTypeByIdsResponse, error) {
	err := s.handler.SortLodgingType(ctx, request.CompanyId, request.StaffId, request.GetIds())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &offeringServiceV1.SortLodgingTypeByIdsResponse{}, errors.WithStack(err)
}

func NewLodgingTypeService() offeringServiceV1.LodgingTypeServiceServer {
	return &LodgingTypeService{
		handler: service.NewLodgingTypeHandler(),
	}
}
