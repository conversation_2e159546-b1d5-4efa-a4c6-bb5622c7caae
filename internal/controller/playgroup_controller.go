package controller

import (
	"context"

	"github.com/samber/lo"

	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type PlaygroupController struct {
	offeringsvcpb.UnimplementedPlaygroupServiceServer
	playgroupService service.PlaygroupService
}

func (s *PlaygroupController) ListPlaygroup(ctx context.Context, request *offeringsvcpb.ListPlaygroupRequest) (*offeringsvcpb.ListPlaygroupResponse, error) {
	if request.GetPagination() == nil {
		request.Pagination = &utilsV2.PaginationRequest{
			PageNum:  lo.ToPtr(int32(1)),
			PageSize: lo.ToPtr(int32(1000)),
		}
	}

	filter := &do.PlaygroupFilterDO{
		CompanyID:      request.GetCompanyId(),
		PlaygroupIds:   request.GetIds(),
		IncludeDeleted: request.GetIncludeDeleted(),
	}
	playgroups, total, err := s.playgroupService.GetPlaygroupList(ctx, filter, request.Pagination)
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.ListPlaygroupResponse{
		Playgroups: converter.ConverterPlaygroupDOListToPB(playgroups),
		Pagination: &utilsV2.PaginationResponse{
			Total:    total,
			PageSize: *request.Pagination.PageSize,
			PageNum:  *request.Pagination.PageNum,
		},
	}, nil
}

func (s *PlaygroupController) CreatePlaygroup(ctx context.Context, request *offeringsvcpb.CreatePlaygroupRequest) (*offeringsvcpb.CreatePlaygroupResponse, error) {
	playgroupDO := converter.ConverterCreatePlaygroupRequestToDO(request)
	id, err := s.playgroupService.CreatePlaygroup(ctx, playgroupDO)
	if err != nil {
		return nil, err
	}
	playgroup, err := s.playgroupService.GetPlaygroupByID(ctx, id, request.GetCompanyId())
	if err != nil {
		return nil, err
	}
	playgroupPBModel := converter.ConverterPlaygroupDOToPB(playgroup)

	return &offeringsvcpb.CreatePlaygroupResponse{
		Playgroup: playgroupPBModel,
	}, nil
}

func (s *PlaygroupController) UpdatePlaygroup(ctx context.Context, request *offeringsvcpb.UpdatePlaygroupRequest) (*offeringsvcpb.UpdatePlaygroupResponse, error) {
	playgroupDO := converter.ConverterUpdatePlaygroupRequestToDO(request)
	err := s.playgroupService.UpdatePlaygroup(ctx, playgroupDO)
	if err != nil {
		return nil, err
	}
	playgroup, err := s.playgroupService.GetPlaygroupByID(ctx, playgroupDO.ID, playgroupDO.CompanyID)
	if err != nil {
		return nil, err
	}
	playgroupPBModel := converter.ConverterPlaygroupDOToPB(playgroup)
	return &offeringsvcpb.UpdatePlaygroupResponse{
		Playgroup: playgroupPBModel,
	}, nil
}

func (s *PlaygroupController) DeletePlaygroup(ctx context.Context, request *offeringsvcpb.DeletePlaygroupRequest) (*offeringsvcpb.DeletePlaygroupResponse, error) {
	err := s.playgroupService.DeletePlaygroup(ctx, request.CompanyId, request.Id, request.StaffId)
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.DeletePlaygroupResponse{}, nil
}

func (s *PlaygroupController) SortPlaygroup(ctx context.Context, request *offeringsvcpb.SortPlaygroupRequest) (*offeringsvcpb.SortPlaygroupResponse, error) {
	err := s.playgroupService.SortPlaygroup(ctx, &do.PlaygroupSortDO{
		CompanyID:    request.CompanyId,
		PlaygroupIds: request.Ids,
		OperateId:    request.StaffId,
	})
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.SortPlaygroupResponse{}, nil
}

func NewPlaygroupController(handler service.PlaygroupService) offeringsvcpb.PlaygroupServiceServer {
	return &PlaygroupController{
		playgroupService: handler,
	}
}
