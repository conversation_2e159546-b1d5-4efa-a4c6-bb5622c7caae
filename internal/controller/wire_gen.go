// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package controller

import (
	"github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

// Injectors from wire.go:

func InitControllers() *Controllers {
	vaccineRequirementRepository := repository.NewVaccineRequirement()
	vaccineServiceRequirementService := service.NewVaccineServiceRequirementService(vaccineRequirementRepository)
	serviceVaccineRequirementServiceServer := NewVaccineServiceRequirementController(vaccineServiceRequirementService)
	playgroupRepository := repository.NewPlaygroupRepository()
	playgroupService := service.NewPlaygroupService(playgroupRepository)
	playgroupServiceServer := NewPlaygroupController(playgroupService)
	serviceHandler := service.NewServiceHandler()
	db := resource.GetOfferingDB()
	eventRepository := repository.NewEventRepository(db)
	producer := repository.NewProducer()
	messageDeliverHandler := service.NewMessageDeliverHandler(eventRepository, producer)
	transactionManager := repository.NewTransactionManager(db)
	groupClassInstanceRepository := repository.NewGroupClassInstanceRepository(db)
	groupClassSessionRepository := repository.NewGroupClassSessionRepository(db)
	groupClassHandler := service.NewGroupClassHandler(serviceHandler, messageDeliverHandler, transactionManager, groupClassInstanceRepository, groupClassSessionRepository)
	groupClassServiceServer := NewGroupClassService(groupClassHandler)
	messageDeliverServiceServer := NewMessageDeliverService(messageDeliverHandler)
	customizeCareTypeRepository := repository.NewCustomizeCareTypeRepository(db)
	customizeCareTypeHandler := service.NewCustomizeCareTypeHandler(customizeCareTypeRepository)
	customizeCareTypeServiceServer := NewCustomizeCareTypeService(customizeCareTypeHandler)
	controllers := &Controllers{
		VaccineRequirementController: serviceVaccineRequirementServiceServer,
		PlaygroupController:          playgroupServiceServer,
		GroupClassController:         groupClassServiceServer,
		MessageDeliverController:     messageDeliverServiceServer,
		CustomizeCareTypeController:  customizeCareTypeServiceServer,
	}
	return controllers
}

// wire.go:

type Controllers struct {
	VaccineRequirementController offeringsvcpb.ServiceVaccineRequirementServiceServer
	PlaygroupController          offeringsvcpb.PlaygroupServiceServer
	GroupClassController         offeringsvcpb.GroupClassServiceServer
	MessageDeliverController     offeringsvcpb.MessageDeliverServiceServer
	CustomizeCareTypeController  offeringsvcpb.CustomizeCareTypeServiceServer
}
