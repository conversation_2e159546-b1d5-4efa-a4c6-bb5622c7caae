package controller

import (
	"context"

	offeringmodel "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringServiceV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type AutoRolloverRuleService struct {
	offeringServiceV1.UnimplementedAutoRolloverRuleServiceServer
}

func (a *AutoRolloverRuleService) BatchGetAutoRolloverRule(ctx context.Context, request *offeringServiceV1.BatchGetAutoRolloverRuleRequest) (*offeringServiceV1.BatchGetAutoRolloverRuleResponse, error) {
	handler := service.NewAutoRolloverRuleHandler()
	ruleWithServiceId, err := handler.BatchGetAutoRolloverRules(ctx, request.ServiceIds)
	if err != nil {
		return nil, err
	}

	result := make(map[int64]*offeringmodel.AutoRolloverRuleModel)
	for serviceId, rule := range ruleWithServiceId {
		result[serviceId] = converter.ConvertAutoRolloverRuleDOToPB(rule)
	}

	return &offeringServiceV1.BatchGetAutoRolloverRuleResponse{
		ServiceIdToAutoRolloverRule: result,
	}, nil
}

func NewAutoRolloverRuleService() *AutoRolloverRuleService {
	return &AutoRolloverRuleService{}
}
