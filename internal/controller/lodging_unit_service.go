package controller

import (
	"context"

	"github.com/samber/lo"

	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type LodgingUnitService struct {
	handler     service.LodgingUnitHandler
	typeHandler service.LodgingTypeHandler
	offeringsvcpb.UnimplementedLodgingUnitServiceServer
}

func (s LodgingUnitService) BatchCreateLodgingUnit(ctx context.Context, request *offeringsvcpb.BatchCreateLodgingUnitRequest) (*offeringsvcpb.BatchCreateLodgingUnitResponse, error) {
	result, err := s.handler.BatchCreateLodgingUnit(ctx, converter.ConvertBatchCreateLodgingUnitRequestToDO(request))
	return &offeringsvcpb.BatchCreateLodgingUnitResponse{LodgingUnitList: converter.ConvertLodgingUnitDoListToPB(result)}, err
}

func (s LodgingUnitService) UpdateLodgingUnit(ctx context.Context, request *offeringsvcpb.UpdateLodgingUnitRequest) (*offeringsvcpb.UpdateLodgingUnitResponse, error) {
	updateOpt := converter.ConvertUpdateLodgingUnitRequestToUpdateOpt(request)
	lodgingTypeDO, err := s.handler.UpdateLodgingUnit(ctx, request.Id, request.CompanyId, updateOpt)
	return &offeringsvcpb.UpdateLodgingUnitResponse{LodgingUnit: converter.ConvertLodgingUnitDOToPB(lodgingTypeDO)}, err
}

func (s LodgingUnitService) DeleteLodgingUnit(ctx context.Context, request *offeringsvcpb.DeleteLodgingUnitRequest) (*offeringsvcpb.DeleteLodgingUnitResponse, error) {
	err := s.handler.BatchDeleteLodgingUnit(ctx, []int64{request.GetId()}, request.CompanyId, &request.TokenStaffId)
	return &offeringsvcpb.DeleteLodgingUnitResponse{}, err
}

func (s LodgingUnitService) BatchDeleteLodgingUnit(ctx context.Context, request *offeringsvcpb.BatchDeleteLodgingUnitRequest) (*offeringsvcpb.BatchDeleteLodgingUnitResponse, error) {
	err := s.handler.BatchDeleteLodgingUnit(ctx, request.GetIds(), request.CompanyId, request.TokenStaffId)
	return &offeringsvcpb.BatchDeleteLodgingUnitResponse{}, err
}

func (s LodgingUnitService) GetLodgingUnitList(ctx context.Context, request *offeringsvcpb.GetLodgingUnitListRequest) (*offeringsvcpb.GetLodgingUnitListResponse, error) {
	response := &offeringsvcpb.GetLodgingUnitListResponse{}
	whereOpt := do.LodgingUnitWhereOpt{
		BusinessID:          request.BusinessId,
		ServiceID:           request.ServiceId,
		EvaluationServiceID: request.EvaluationServiceId,
		IDIn:                request.UnitIds,
		CameraIDIn:          request.CameraIds,
	}
	if request.TypeIdList != nil {
		whereOpt.LodgingTypeIDIn = request.TypeIdList.IdList
	}
	dos, err := s.handler.ListLodgingUnit(ctx, request.CompanyId, &whereOpt)
	if err != nil {
		return response, err
	}
	response.LodgingUnitList = converter.ConvertLodgingUnitDoListToPB(dos)
	return response, nil
}

func (s LodgingUnitService) MGetLodgingUnit(ctx context.Context, request *offeringsvcpb.MGetLodgingUnitRequest) (*offeringsvcpb.MGetLodgingUnitResponse, error) {
	response := &offeringsvcpb.MGetLodgingUnitResponse{}
	dos, err := s.handler.MGetLodgingUnit(ctx, request.GetIdList())
	if err != nil {
		return response, err
	}
	lodgingTypeIds := lo.Map(dos, func(do *do.LodgingUnitDO, _ int) int64 {
		return do.LodgingTypeID
	})
	typeDos, err2 := s.typeHandler.MGetLodgingType(ctx, lodgingTypeIds)
	if err2 != nil {
		return response, err2
	}
	response.LodgingUnitList = converter.ConvertLodgingUnitDoListToPB(dos)
	response.LodgingTypeList = converter.ConvertLodgingTypeDOListToPB(typeDos)
	return response, nil
}

func (s LodgingUnitService) SortLodgingUnitByIds(ctx context.Context, request *offeringsvcpb.SortLodgingUnitByIdsRequest) (*offeringsvcpb.SortLodgingUnitByIdsResponse, error) {
	err := s.handler.SortLodgingUnit(ctx, request.CompanyId, request.StaffId, request.GetIds())
	if err != nil {
		return nil, err
	}

	return &offeringsvcpb.SortLodgingUnitByIdsResponse{}, err
}

func NewLodgingUnitService() offeringsvcpb.LodgingUnitServiceServer {
	return &LodgingUnitService{
		handler:     service.NewLodgingUnitHandler(),
		typeHandler: service.NewLodgingTypeHandler(),
	}
}
