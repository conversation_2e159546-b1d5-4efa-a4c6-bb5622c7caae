//go:build wireinject
// +build wireinject

package v2

import (
	"github.com/google/wire"

	offeringServiceV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v2"
)

type ControllersV2 struct {
	PricingRuleController offeringServiceV2.PricingRuleServiceServer
}

func InitControllersV2() *ControllersV2 {
	wire.Build(
		wire.Struct(new(ControllersV2), "*"),
		NewPricingRuleController,
	)
	return &ControllersV2{}
}
