// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package v2

import (
	offeringServiceV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v2"
)

// Injectors from wire.go:

func InitControllersV2() *ControllersV2 {
	pricingRuleController := NewPricingRuleController()
	controllersV2 := &ControllersV2{
		PricingRuleController: pricingRuleController,
	}
	return controllersV2
}

// wire.go:

type ControllersV2 struct {
	PricingRuleController offeringServiceV2.PricingRuleServiceServer
}
