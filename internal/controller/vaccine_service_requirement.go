package controller

import (
	"context"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type vaccineServiceRequirementController struct {
	offeringsvcpb.UnimplementedServiceVaccineRequirementServiceServer
	vaccineRequirementService service.VaccineServiceRequirementService
}

func (v vaccineServiceRequirementController) UpdateVaccineRequirementForService(ctx context.Context, request *offeringsvcpb.UpdateVaccineRequirementForServiceRequest) (*offeringsvcpb.UpdateVaccineRequirementForServiceResponse, error) {
	if err := v.vaccineRequirementService.UpdateVaccineRequirementsForServiceItem(ctx, request.GetTenant().GetCompanyId(), request.GetServiceItemType(), request.GetVaccineIds()); err != nil {
		return nil, err
	}

	return &offeringsvcpb.UpdateVaccineRequirementForServiceResponse{}, nil
}

func (v vaccineServiceRequirementController) UpdateServiceRequirementForVaccine(ctx context.Context, request *offeringsvcpb.UpdateServiceRequirementForVaccineRequest) (*offeringsvcpb.UpdateServiceRequirementForVaccineResponse, error) {
	if err := v.vaccineRequirementService.UpdateServiceRequirementsForVaccine(ctx, request.GetTenant().GetCompanyId(), request.GetVaccineId(), request.GetServiceItemTypes()); err != nil {
		return nil, err
	}
	return &offeringsvcpb.UpdateServiceRequirementForVaccineResponse{}, nil
}

func (v vaccineServiceRequirementController) ListServiceVaccineRequirements(ctx context.Context, request *offeringsvcpb.ListServiceVaccineRequirementsRequest) (*offeringsvcpb.ListServiceVaccineRequirementsResponse, error) {
	total, vaccineRequirements, err := v.vaccineRequirementService.ListVaccineRequirements(ctx, request.GetTenant().GetCompanyId(), request.GetFilter(), request.GetPagination())
	if err != nil {
		return nil, err
	}

	results := make([]*offeringpb.ServiceVaccineRequirementModel, 0, len(vaccineRequirements))
	for _, vaccineRequirement := range vaccineRequirements {
		results = append(results, &offeringpb.ServiceVaccineRequirementModel{
			Id:              vaccineRequirement.Id,
			ServiceItemType: vaccineRequirement.ServiceItemType,
			VaccineId:       vaccineRequirement.VaccineID,
		})
	}

	return &offeringsvcpb.ListServiceVaccineRequirementsResponse{
		ServiceVaccineRequirements: results,
		Pagination: &utilsV2.PaginationResponse{
			Total:    int32(total),
			PageSize: request.GetPagination().GetPageSize(),
			PageNum:  request.GetPagination().GetPageNum(),
		},
	}, nil
}

func NewVaccineServiceRequirementController(vaccineRequirementService service.VaccineServiceRequirementService) offeringsvcpb.ServiceVaccineRequirementServiceServer {
	return &vaccineServiceRequirementController{
		vaccineRequirementService: vaccineRequirementService,
	}
}
