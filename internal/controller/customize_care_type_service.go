package controller

import (
	"context"
	"github.com/MoeGolibrary/go-lib/zlog"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type CustomizeCareTypeService struct {
	offeringsvcpb.UnimplementedCustomizeCareTypeServiceServer
	customizeCareTypeHandler service.CustomizeCareTypeHandler
}

func NewCustomizeCareTypeService(customizeCareTypeHandler service.CustomizeCareTypeHandler,
) offeringsvcpb.CustomizeCareTypeServiceServer {
	return &CustomizeCareTypeService{
		customizeCareTypeHandler: customizeCareTypeHandler,
	}
}

func (c *CustomizeCareTypeService) ListCareTypes(ctx context.Context, request *offeringsvcpb.ListCareTypesRequest) (*offeringsvcpb.ListCareTypesResponse, error) {
	careTypes, err := c.customizeCareTypeHandler.List(ctx, request.GetCompanyId(), request.GetWhiteListFilter())
	if err != nil {
		return nil, err
	}

	careTypeViews, err := converter.ConvertCareTypeDOsToViews(careTypes)
	if err != nil {
		zlog.Error(ctx, "failed to convert care type list", zap.Error(err))
		return nil, status.Errorf(codes.Internal, "failed to convert care type list: %v", err)
	}

	return &offeringsvcpb.ListCareTypesResponse{CareTypes: careTypeViews}, nil
}

func (c *CustomizeCareTypeService) UpdateCareTypeName(ctx context.Context, request *offeringsvcpb.UpdateCareTypeNameRequest) (*offeringsvcpb.UpdateCareTypeNameResponse, error) {
	err := c.customizeCareTypeHandler.Update(ctx, &service.UpdateCustomizeCareTypeRequest{
		ServiceItemType: request.GetServiceItemType(),
		CompanyId:       request.GetCompanyId(),
		Name:            request.GetName(),
		StaffId:         request.GetStaffId(),
		Filter:          request.GetWhiteListFilter(),
	})
	if err != nil {
		zlog.Error(ctx, "failed to update care type", zap.Error(err))
		return nil, err
	}
	return &offeringsvcpb.UpdateCareTypeNameResponse{}, nil
}
