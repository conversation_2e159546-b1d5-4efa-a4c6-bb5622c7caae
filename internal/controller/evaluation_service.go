package controller

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/zlog"
	offeringmodel "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringServiceV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type EvaluationService struct {
	offeringServiceV1.UnimplementedEvaluationServiceServer
}

func (e EvaluationService) CreateEvaluation(ctx context.Context, request *offeringServiceV1.CreateEvaluationRequest) (*offeringServiceV1.CreateEvaluationResponse, error) {
	ctx = zlog.NewContext(ctx, zap.Int64("companyId", request.CompanyId))

	handler := service.NewEvaluationHandler()
	evaluationId, err := handler.CreateNewEvaluation(ctx, request.CompanyId, converter.ConvertEvaluationDefToDO(request.GetEvaluationDef()))
	if err != nil {
		return nil, errors.WithStack(err)
	}

	evaluationDO, err := handler.GetEvaluationDetail(ctx, evaluationId, nil)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &offeringServiceV1.CreateEvaluationResponse{
		EvaluationModel: converter.ConvertEvaluationDOToPBModel(evaluationDO),
	}, nil
}

func (e EvaluationService) UpdateEvaluation(ctx context.Context, request *offeringServiceV1.UpdateEvaluationRequest) (*offeringServiceV1.UpdateEvaluationResponse, error) {
	handler := service.NewEvaluationHandler()
	err := handler.UpdateEvaluation(ctx, request.GetTenant().GetCompanyId(), request.Id, converter.ConvertEvaluationDefToDO(request.GetEvaluationDef()))
	if err != nil {
		return nil, errors.WithStack(err)
	}

	evaluationDO, err := handler.GetEvaluationDetail(ctx, request.Id, nil)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &offeringServiceV1.UpdateEvaluationResponse{
		EvaluationModel: converter.ConvertEvaluationDOToPBModel(evaluationDO),
	}, nil

}

func (e EvaluationService) DeleteEvaluation(ctx context.Context, request *offeringServiceV1.DeleteEvaluationRequest) (*offeringServiceV1.DeleteEvaluationResponse, error) {
	handler := service.NewEvaluationHandler()
	err := handler.DeleteEvaluation(ctx, request.CompanyId, request.Id)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &offeringServiceV1.DeleteEvaluationResponse{}, nil
}

func (e EvaluationService) GetEvaluation(ctx context.Context, request *offeringServiceV1.GetEvaluationRequest) (*offeringServiceV1.GetEvaluationResponse, error) {
	handler := service.NewEvaluationHandler()
	evaluationDO, err := handler.GetEvaluationDetail(ctx, request.Id, request.BusinessId)
	if err != nil {
		return nil, err
	}

	return &offeringServiceV1.GetEvaluationResponse{
		EvaluationModel: converter.ConvertEvaluationDOToPBModel(evaluationDO),
	}, nil
}

func (e EvaluationService) GetEvaluationList(ctx context.Context, request *offeringServiceV1.GetEvaluationListRequest) (*offeringServiceV1.GetEvaluationListResponse, error) {
	handler := service.NewEvaluationHandler()
	evaluationDOs, err := handler.GetEvaluationList(ctx, request.CompanyId)
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return &offeringServiceV1.GetEvaluationListResponse{
		Evaluations: lo.Map(evaluationDOs, func(e *do.EvaluationDO, _ int) *offeringmodel.EvaluationModel {
			return converter.ConvertEvaluationDOToPBModel(e)
		}),
	}, nil
}

func (e EvaluationService) GetApplicableEvaluationList(ctx context.Context, request *offeringServiceV1.GetApplicableEvaluationListRequest) (*offeringServiceV1.GetApplicableEvaluationListResponse, error) {
	handler := service.NewEvaluationHandler()
	evaluationDOs, err := handler.GetApplicableEvaluationList(ctx, request.CompanyId, &do.EvaluationFilter{
		BusinessId:      request.BusinessId,
		ServiceItemType: request.ServiceItemType,
		FilterByPet:     request.FilterByPet,
		IncludeInactive: request.GetIncludeInactive(),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &offeringServiceV1.GetApplicableEvaluationListResponse{
		Evaluations: lo.Map(evaluationDOs, func(e *do.EvaluationDO, _ int) *offeringmodel.EvaluationBriefView {
			return converter.ConvertEvaluationDOToBriefView(e)
		}),
	}, nil
}

func (e EvaluationService) GetBusinessListWithApplicableEvaluation(ctx context.Context, request *offeringServiceV1.GetBusinessListWithApplicableEvaluationRequest) (*offeringServiceV1.GetBusinessListWithApplicableEvaluationResponse, error) {
	handler := service.NewEvaluationHandler()
	evaluationDos, err := handler.GetApplicableEvaluationList(ctx, request.CompanyId, nil)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	businessIds := make([]int64, 0)
	for _, evaluationDo := range evaluationDos {
		if evaluationDo.AvailableForAllBusiness {
			return &offeringServiceV1.GetBusinessListWithApplicableEvaluationResponse{
				IsAllLocation: true,
			}, nil
		}
		businessIds = append(businessIds, evaluationDo.AvailableBusinessIds...)
	}

	return &offeringServiceV1.GetBusinessListWithApplicableEvaluationResponse{
		IsAllLocation: false,
		BusinessIds:   lo.Uniq(businessIds),
	}, nil
}

func (e EvaluationService) GetEvaluationListWithEvaluationIds(ctx context.Context, request *offeringServiceV1.GetEvaluationListWithEvaluationIdsRequest) (*offeringServiceV1.GetEvaluationListWithEvaluationIdsResponse, error) {
	handler := service.NewEvaluationHandler()
	evaluationDOs, err := handler.GetEvaluationListByIds(ctx, request.EvaluationIds, request.BusinessId)
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &offeringServiceV1.GetEvaluationListWithEvaluationIdsResponse{
		Evaluations: lo.Map(evaluationDOs, func(e *do.EvaluationDO, _ int) *offeringmodel.EvaluationBriefView {
			return converter.ConvertEvaluationDOToBriefView(e)
		}),
	}, nil
}

func (e EvaluationService) ListEvaluation(ctx context.Context, request *offeringServiceV1.ListEvaluationRequest) (*offeringServiceV1.ListEvaluationResponse, error) {
	handler := service.NewEvaluationHandler()

	var isResettable *bool
	var companyIds []int64
	var ids []int64
	if request.Filter != nil {
		isResettable = request.Filter.IsResettable
		if len(request.Filter.CompanyIds) > 0 {
			companyIds = request.Filter.CompanyIds
		}
		if len(request.Filter.Ids) > 0 {
			ids = request.Filter.Ids
		}
	}

	filter := &do.EvaluationModelFilter{
		IsResettable: isResettable,
		CompanyIds:   companyIds,
		Ids:          ids,
		BusinessId:   request.Filter.BusinessId,
	}

	total, evaluationDOs, err := handler.GetEvaluationListByFilter(ctx, filter, request.GetPagination())
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return &offeringServiceV1.ListEvaluationResponse{
		Evaluations: lo.Map(evaluationDOs, func(e *do.EvaluationDO, _ int) *offeringmodel.EvaluationModel {
			return converter.ConvertEvaluationDOToPBModel(e)
		}),
		Pagination: &utilsV2.PaginationResponse{
			Total:    int32(total),
			PageSize: request.GetPagination().GetPageSize(),
			PageNum:  request.GetPagination().GetPageNum(),
		},
	}, nil

}

func NewEvaluationService() offeringServiceV1.EvaluationServiceServer {
	return &EvaluationService{}
}
