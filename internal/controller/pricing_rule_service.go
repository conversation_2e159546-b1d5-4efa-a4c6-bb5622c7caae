package controller

import (
	"context"

	offeringServiceV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
)

type PricingRuleService struct {
	offeringServiceV1.UnimplementedPricingRuleServiceServer
}

func (p PricingRuleService) UpsertPricingRule(ctx context.Context, request *offeringServiceV1.UpsertPricingRuleRequest) (*offeringServiceV1.UpsertPricingRuleResponse, error) {
	return nil, nil
}

func (p PricingRuleService) GetPricingRule(ctx context.Context, request *offeringServiceV1.GetPricingRuleRequest) (*offeringServiceV1.GetPricingRuleResponse, error) {
	return nil, nil
}

func (p PricingRuleService) ListPricingRules(ctx context.Context, request *offeringServiceV1.ListPricingRulesRequest) (*offeringServiceV1.ListPricingRulesResponse, error) {
	return nil, nil
}

func (p PricingRuleService) CheckRuleName(ctx context.Context, request *offeringServiceV1.CheckRuleNameRequest) (*offeringServiceV1.CheckRuleNameResponse, error) {
	return nil, nil
}

func (p PricingRuleService) CalculatePricingRule(ctx context.Context, request *offeringServiceV1.CalculatePricingRuleRequest) (*offeringServiceV1.CalculatePricingRuleResponse, error) {
	return nil, nil
}

func (p PricingRuleService) DeletePricingRule(ctx context.Context, request *offeringServiceV1.DeletePricingRuleRequest) (*offeringServiceV1.DeletePricingRuleResponse, error) {
	return nil, nil
}

func (p PricingRuleService) ListAssociatedServices(ctx context.Context, request *offeringServiceV1.ListAssociatedServicesRequest) (*offeringServiceV1.ListAssociatedServicesResponse, error) {
	return nil, nil
}

func NewPricingRuleService() *PricingRuleService {
	return &PricingRuleService{}
}
