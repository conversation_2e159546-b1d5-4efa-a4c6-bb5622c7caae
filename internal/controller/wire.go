//go:build wireinject
// +build wireinject

package controller

import (
	"github.com/google/wire"

	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/repository"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type Controllers struct {
	VaccineRequirementController offeringsvcpb.ServiceVaccineRequirementServiceServer
	PlaygroupController          offeringsvcpb.PlaygroupServiceServer
	GroupClassController         offeringsvcpb.GroupClassServiceServer
	MessageDeliverController     offeringsvcpb.MessageDeliverServiceServer
	CustomizeCareTypeController  offeringsvcpb.CustomizeCareTypeServiceServer
}

func InitControllers() *Controllers {
	wire.Build(
		wire.Struct(new(Controllers), "*"),
		NewVaccineServiceRequirementController,
		service.NewVaccineServiceRequirementService,
		repository.NewVaccineRequirement,

		NewPlaygroupController,
		service.NewPlaygroupService,
		repository.NewPlaygroupRepository,

		NewGroupClassService,
		service.NewServiceHandler,
		service.NewGroupClassHandler,
		repository.NewTransactionManager,
		repository.NewGroupClassInstanceRepository,
		repository.NewGroupClassSessionRepository,

		NewMessageDeliverService,
		service.NewMessageDeliverHandler,
		repository.NewEventRepository,
		repository.NewProducer,

		resource.GetOfferingDB,

		NewCustomizeCareTypeService,
		service.NewCustomizeCareTypeHandler,
		repository.NewCustomizeCareTypeRepository,
	)
	return &Controllers{}
}
