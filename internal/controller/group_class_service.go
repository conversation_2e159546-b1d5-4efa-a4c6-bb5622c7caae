package controller

import (
	"context"
	"github.com/MoeGolibrary/moego-svc-offering/internal/clients"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"

	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/emptypb"

	"github.com/MoeGolibrary/go-lib/zlog"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/samber/lo"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
)

type GroupClassService struct {
	groupClassService service.GroupClassHandler
	companyClient     clients.CompanyClient

	offeringsvcpb.UnimplementedGroupClassServiceServer
}

func NewGroupClassService(
	groupClassService service.GroupClassHandler,
) offeringsvcpb.GroupClassServiceServer {
	return &GroupClassService{
		groupClassService: groupClassService,
		companyClient:     clients.NewCompanyClient(resource.GetCompanyServiceClient()),
	}
}

func (s *GroupClassService) CreateInstanceAndSessions(ctx context.Context,
	req *offeringsvcpb.CreateInstanceAndSessionsRequest) (*offeringsvcpb.CreateInstanceAndSessionsResponse, error) {
	opt, err := converter.ConvertCreateGroupClassInstanceRequestToDO(req)
	if err != nil {
		zlog.Error(ctx, "failed to convert request to DO", zap.Any("req", req), zap.Error(err))
		return nil, status.Error(codes.InvalidArgument, "failed to convert request to DO")
	}

	instanceDO, sessionDos, err := s.groupClassService.CreateInstanceAndSessions(ctx, opt)
	if err != nil {
		return nil, err
	}
	location, err := s.companyClient.GetTimeLocation(ctx, req.CompanyId)
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.CreateInstanceAndSessionsResponse{
		Instance: converter.ConvertGroupClassInstanceDOToPB(instanceDO),
		Sessions: converter.ConvertGroupClassSessionDOListToPB(sessionDos, location),
	}, nil
}

func (s *GroupClassService) CountInstancesGroupByStatus(ctx context.Context,
	req *offeringsvcpb.CountInstancesGroupByStatusRequest) (*offeringsvcpb.CountInstancesGroupByStatusResponse, error) {
	counts, err := s.groupClassService.CountInstanceGroupByStatus(ctx, &do.GroupClassInstanceFilter{
		CompanyID:   req.GetCompanyId(),
		BusinessIDs: []int64{req.GetBusinessId()},
		StaffIDs:    req.GetStaffIds(),
	})
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.CountInstancesGroupByStatusResponse{
		Counts: counts,
	}, nil
}

func (s *GroupClassService) CountInstancesGroupByClass(ctx context.Context,
	req *offeringsvcpb.CountInstancesGroupByClassRequest) (*offeringsvcpb.CountInstancesGroupByClassResponse, error) {
	counts, err := s.groupClassService.CountInstanceGroupByServiceID(ctx, &do.GroupClassInstanceFilter{
		CompanyID:   req.GetCompanyId(),
		BusinessIDs: []int64{req.GetBusinessId()},
		StaffIDs:    req.GetStaffIds(),
		Statuses:    []offeringpb.GroupClassInstance_Status{req.GetStatus()},
	})
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.CountInstancesGroupByClassResponse{
		Counts: counts,
	}, nil
}

func (s *GroupClassService) GetInstance(ctx context.Context,
	req *offeringsvcpb.GetInstanceRequest) (*offeringsvcpb.GetInstanceResponse, error) {
	instance, err := s.groupClassService.GetInstance(ctx, req.GetId())
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.GetInstanceResponse{
		GroupClassInstance: converter.ConvertGroupClassInstanceDOToPB(instance),
	}, nil
}

func (s *GroupClassService) ListInstances(ctx context.Context,
	req *offeringsvcpb.ListInstancesRequest) (*offeringsvcpb.ListInstancesResponse, error) {
	dos, pageResponse, err := s.groupClassService.ListInstances(ctx, &do.GroupClassInstanceFilter{
		IDs:         req.GetIds(),
		CompanyID:   req.GetCompanyId(),
		BusinessIDs: req.GetBusinessIds(),
		StaffIDs:    req.GetStaffIds(),
		ServiceIDs:  req.GroupClassIds,
		Statuses:    []offeringpb.GroupClassInstance_Status{req.GetStatus()},
	}, req.GetPagination())
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.ListInstancesResponse{
		GroupClassInstances: converter.ConvertGroupClassInstanceDOListToPB(dos),
		Pagination:          pageResponse,
	}, nil
}

func (s *GroupClassService) UpdateInstanceAndSessions(ctx context.Context,
	req *offeringsvcpb.UpdateInstanceAndSessionsRequest) (*offeringsvcpb.UpdateInstanceAndSessionsResponse, error) {
	instance, sessions, err := s.groupClassService.UpdateInstanceAndSessions(ctx, &do.GroupClassInstanceFilter{
		IDs: []int64{req.GetId()},
	}, converter.ConvertGroupClassInstanceUpdateReqToOpt(req))
	if err != nil {
		return nil, err
	}
	location, err := s.companyClient.GetTimeLocation(ctx, instance.CompanyID)
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.UpdateInstanceAndSessionsResponse{
		Instance: converter.ConvertGroupClassInstanceDOToPB(instance),
		Sessions: converter.ConvertGroupClassSessionDOListToPB(sessions, location),
	}, nil
}

func (s *GroupClassService) DeleteInstanceAndSessions(ctx context.Context,
	req *offeringsvcpb.DeleteInstanceAndSessionsRequest) (*offeringsvcpb.DeleteInstanceAndSessionsResponse, error) {
	if err := s.groupClassService.DeleteInstanceAndSessions(ctx, req.GetId()); err != nil {
		return nil, err
	}
	return &offeringsvcpb.DeleteInstanceAndSessionsResponse{}, nil
}

func (s *GroupClassService) UpdateSession(ctx context.Context,
	req *offeringsvcpb.UpdateSessionRequest) (*offeringsvcpb.UpdateSessionResponse, error) {
	session, err := s.groupClassService.UpdateSession(ctx, &do.GroupClassSessionFilter{
		IDs: []int64{req.GetId()},
	}, converter.ConvertGroupClassSessionUpdateReqToOpt(req))
	if err != nil {
		return nil, err
	}
	location, err := s.companyClient.GetTimeLocation(ctx, session.CompanyID)
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.UpdateSessionResponse{
		Session: converter.ConvertGroupClassSessionDOToPB(session, location),
	}, nil
}

func (s *GroupClassService) ListSessions(ctx context.Context,
	req *offeringsvcpb.ListSessionsRequest) (*offeringsvcpb.ListSessionsResponse, error) {
	filter := &do.GroupClassSessionFilter{
		CompanyID:   req.GetCompanyId(),
		InstanceIDs: req.GetGroupClassInstanceId(),
		IDs:         req.GetSessionIds(),
	}
	if req.GetStartTimeMin() != nil {
		filter.StartTimeMin = lo.ToPtr(req.GetStartTimeMin().AsTime())
	}
	if req.GetStartTimeMax() != nil {
		filter.StartTimeMax = lo.ToPtr(req.GetStartTimeMax().AsTime())
	}
	dos, _, err := s.groupClassService.ListSessions(ctx, filter, nil)
	if err != nil {
		return nil, err
	}
	if len(dos) == 0 {
		return &offeringsvcpb.ListSessionsResponse{}, nil
	}
	location, err := s.companyClient.GetTimeLocation(ctx, dos[0].CompanyID)
	if err != nil {
		return nil, err
	}
	return &offeringsvcpb.ListSessionsResponse{
		Sessions: converter.ConvertGroupClassSessionDOListToPB(dos, location),
	}, nil
}

func (s *GroupClassService) TaskRefreshInstanceStatus(ctx context.Context, _ *emptypb.Empty) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, s.groupClassService.TaskRefreshInstanceStatus(ctx)
}
