package controller

import (
	"context"

	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/service"
	"google.golang.org/protobuf/types/known/emptypb"
)

type MessageDeliverService struct {
	handler service.MessageDeliverHandler

	offeringsvcpb.UnimplementedMessageDeliverServiceServer
}

func NewMessageDeliverService(handler service.MessageDeliverHandler) offeringsvcpb.MessageDeliverServiceServer {
	return &MessageDeliverService{
		handler: handler,
	}
}

func (s *MessageDeliverService) TaskSendAllEvent(ctx context.Context, _ *emptypb.Empty) (*emptypb.Empty, error) {
	return &emptypb.Empty{}, s.handler.TaskSendAll(ctx)
}
