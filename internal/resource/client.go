package resource

import (
	"github.com/MoeGolibrary/go-lib/grpc"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
)

var _staffServiceClient organizationsvcpb.StaffServiceClient

var _businessServiceClient organizationsvcpb.BusinessServiceClient

var _companyServiceClient organizationsvcpb.CompanyServiceClient

var _serviceAreaServiceClient organizationsvcpb.ServiceAreaServiceClient

func GetStaffServiceClient() organizationsvcpb.StaffServiceClient {
	return _staffServiceClient
}

func GetBusinessServiceClient() organizationsvcpb.BusinessServiceClient {
	return _businessServiceClient
}

func GetCompanyServiceClient() organizationsvcpb.CompanyServiceClient {
	return _companyServiceClient
}

func GetServiceAreaServiceClient() organizationsvcpb.ServiceAreaServiceClient {
	return _serviceAreaServiceClient
}

func InitClient() {
	_staffServiceClient = grpc.NewClient("moego-svc-organization:9090", organizationsvcpb.NewStaffServiceClient)
	_businessServiceClient = grpc.NewClient("moego-svc-organization:9090", organizationsvcpb.NewBusinessServiceClient)
	_companyServiceClient = grpc.NewClient("moego-svc-organization:9090", organizationsvcpb.NewCompanyServiceClient)
	_serviceAreaServiceClient = grpc.NewClient("moego-svc-organization:9090", organizationsvcpb.NewServiceAreaServiceClient)
}
