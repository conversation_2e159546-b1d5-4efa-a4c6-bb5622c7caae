package resource

import (
	"reflect"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/plugin/opentelemetry/tracing"

	"github.com/MoeGolibrary/go-lib/gorm"

	"github.com/MoeGolibrary/moego-svc-offering/config"
)

var _offeringDB *gorm.DB

var _groomingDB *gorm.DB

func GetOfferingDB() *gorm.DB {
	return _offeringDB
}

func GetGroomingDB() *gorm.DB {
	return _groomingDB
}

var mysqlDSNSuffix = "?charset=utf8mb4&parseTime=True"

func InitDB(config *config.Config) {
	offeringDBConfig := config.DB.OfferingDB
	offeringDB, err := gorm.Open(postgres.Open(offeringDBConfig.DSN), &gorm.Config{
		SkipDefaultTransaction: true,
		TranslateError:         true,
		QueryFields:            true,
	})
	if err != nil {
		panic(err)
	}
	// set only tracing
	if err = offeringDB.Use(tracing.NewPlugin(tracing.WithoutMetrics(), tracing.WithoutQueryVariables())); err != nil {
		panic(err)
	}
	offeringSQLDB, err := offeringDB.DB()
	if err != nil {
		panic(err)
	}
	offeringSQLDB.SetMaxIdleConns(offeringDBConfig.MaxIdleConnections)
	offeringSQLDB.SetMaxOpenConns(offeringDBConfig.MaxOpenConnections)
	offeringSQLDB.SetConnMaxLifetime(offeringDBConfig.MaxConnectionLifetime)
	if err = offeringDB.Callback().Update().Before("gorm:update").Register("my_plugin:serialize_before_update", serializeBeforeUpdate); err != nil {
		panic(err)
	}
	if err := offeringSQLDB.Ping(); err != nil {
		panic(err)
	}

	_offeringDB = offeringDB

	groomingDBConfig := config.DB.GroomingDB
	groomingDB, err := gorm.Open(mysql.Open(groomingDBConfig.DSN+mysqlDSNSuffix), &gorm.Config{
		SkipDefaultTransaction: true,
		TranslateError:         true,
	})
	if err != nil {
		panic(err)
	}
	// set only tracing
	if err = groomingDB.Use(tracing.NewPlugin(tracing.WithoutMetrics())); err != nil {
		panic(err)
	}
	groomingSQLDB, err := groomingDB.DB()
	if err != nil {
		panic(err)
	}
	groomingSQLDB.SetMaxIdleConns(groomingDBConfig.MaxIdleConnections)
	groomingSQLDB.SetMaxOpenConns(groomingDBConfig.MaxOpenConnections)
	groomingSQLDB.SetConnMaxLifetime(groomingDBConfig.MaxConnectionLifetime)
	if err := groomingSQLDB.Ping(); err != nil {
		panic(err)
	}
	_groomingDB = groomingDB
}

// 处理 gormx update 不能根据字段类型，自动序列化问题
func serializeBeforeUpdate(db *gorm.DB) {
	destMap, ok := db.Statement.Dest.(map[string]interface{})
	if !ok {
		return
	}
	for _, field := range db.Statement.Schema.Fields {
		if field.Serializer == nil {
			continue
		}
		v, ok1 := destMap[field.DBName]
		if !ok1 || v == nil {
			continue
		}
		sv, err := field.Serializer.Value(db.Statement.Context, field, reflect.ValueOf(v), v)
		if err != nil {
			_ = db.AddError(err)
			return
		}
		destMap[field.DBName] = sv
	}
}
