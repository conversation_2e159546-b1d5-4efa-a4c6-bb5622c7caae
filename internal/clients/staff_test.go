package clients

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/protobuf/proto"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/mocks"
)

func TestStaffClient_ListAllStaffIds(t *testing.T) {
	ctrl := gomock.NewController(t)

	mockStaffServiceClient := mocks.NewMockStaffServiceClient(ctrl)
	mockStaffServiceClient.EXPECT().QueryStaffByCompanyId(gomock.Any(), gomock.Eq(
		&organizationsvcpb.QueryStaffByCompanyIdRequest{
			CompanyId: 1,
			Pagination: &utilsV2.PaginationRequest{
				PageSize: proto.Int32(1000),
				PageNum:  proto.Int32(1),
			},
		})).Return(&organizationsvcpb.QueryStaffByCompanyIdResponse{
		Staffs: []*organizationpb.StaffModel{
			{
				Id:        11,
				FirstName: "John",
			},
			{
				Id:        12,
				FirstName: "Doe",
			},
		},
	}, nil)
	mockStaffServiceClient.EXPECT().GetStaffsByWorkingLocation(gomock.Any(), gomock.Eq(
		&organizationsvcpb.GetStaffsByWorkingLocationRequest{
			CompanyId:         1,
			WorkingLocationId: 2,
		})).Return(&organizationsvcpb.GetStaffsByWorkingLocationResponse{
		Staffs: []*organizationpb.StaffBasicView{
			{
				Id:        21,
				FirstName: "John",
			},
			{
				Id:        22,
				FirstName: "Doe",
			},
		},
	}, nil)

	staffClient := NewStaffClient(mockStaffServiceClient)

	for _, tc := range []struct {
		name       string
		companyId  int64
		businessId *int64
		expected   []int64
	}{
		{
			name:      "list all staff ids",
			companyId: 1,
			expected:  []int64{11, 12},
		},
		{
			name:       "list working staff ids",
			companyId:  1,
			businessId: proto.Int64(2),
			expected:   []int64{21, 22},
		},
	} {
		t.Run(tc.name, func(t *testing.T) {
			ids, err := staffClient.ListAllStaffIds(context.TODO(), tc.companyId, tc.businessId)
			assert.NoError(t, err)
			assert.Equal(t, tc.expected, ids)
		})
	}

}
