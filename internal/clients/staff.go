package clients

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"google.golang.org/protobuf/proto"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
)

type StaffClient interface {
	ListAllStaffIds(ctx context.Context, companyId int64, businessId *int64) ([]int64, error)
}

type staffClient struct {
	client organizationsvcpb.StaffServiceClient
}

func (s *staffClient) ListAllStaffIds(ctx context.Context, companyId int64, businessId *int64) ([]int64, error) {
	if businessId != nil {
		return s.listWorkingStaffIds(ctx, companyId, *businessId)
	}

	resp, err := s.client.QueryStaffByCompanyId(ctx, &organizationsvcpb.QueryStaffByCompanyIdRequest{
		CompanyId: companyId,
		Pagination: &utilsV2.PaginationRequest{
			PageSize: proto.Int32(1000),
			PageNum:  proto.Int32(1),
		},
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return lo.Map(resp.GetStaffs(), func(item *organizationpb.StaffModel, _ int) int64 {
		return item.GetId()
	}), nil
}

func (s *staffClient) listWorkingStaffIds(ctx context.Context, companyId int64, businessId int64) ([]int64, error) {
	resp, err := s.client.GetStaffsByWorkingLocation(ctx, &organizationsvcpb.GetStaffsByWorkingLocationRequest{
		CompanyId:         companyId,
		WorkingLocationId: businessId,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return lo.Map(resp.GetStaffs(), func(item *organizationpb.StaffBasicView, _ int) int64 {
		return item.GetId()
	}), nil
}

func NewStaffClient(client organizationsvcpb.StaffServiceClient) StaffClient {
	return &staffClient{client: client}
}
