package clients

import (
	"context"
	"time"

	"github.com/pkg/errors"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
)

type CompanyClient interface {
	GetCompanyPreferenceSetting(ctx context.Context, companyId int64) (*organizationpb.CompanyPreferenceSettingModel, error)
	GetTimeLocation(ctx context.Context, companyId int64) (*time.Location, error)
}

type companyClient struct {
	companyServiceClient organizationsvcpb.CompanyServiceClient
}

func (i *companyClient) GetTimeLocation(ctx context.Context, companyId int64) (*time.Location, error) {
	setting, err := i.GetCompanyPreferenceSetting(ctx, companyId)
	if err != nil {
		return nil, err
	}
	location, err := time.LoadLocation(setting.GetTimeZone().GetName())
	if err != nil {
		return nil, err
	}
	return location, nil
}

func (i *companyClient) GetCompanyPreferenceSetting(ctx context.Context, companyId int64) (*organizationpb.CompanyPreferenceSettingModel, error) {
	rsp, err := i.companyServiceClient.GetCompanyPreferenceSetting(ctx, &organizationsvcpb.GetCompanyPreferenceSettingRequest{
		CompanyId: companyId,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return rsp.GetPreferenceSetting(), nil
}

func NewCompanyClient(companyServiceClient organizationsvcpb.CompanyServiceClient) CompanyClient {
	return &companyClient{
		companyServiceClient: companyServiceClient,
	}
}
