package clients

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"

	businesspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
)

type BusinessClient interface {
	GetCompanyIDByLocationID(ctx context.Context, businessIDs []int64) (map[int64]int64, error)
	GetBusinessInfo(ctx context.Context, businessId int64) (*businesspb.BusinessModel, error)
}

type businessClient struct {
	businessServiceClient organizationsvcpb.BusinessServiceClient
}

func (i *businessClient) GetCompanyIDByLocationID(ctx context.Context, businessIDs []int64) (map[int64]int64, error) {
	if len(businessIDs) == 0 {
		return map[int64]int64{}, nil
	}
	rsp, err := i.businessServiceClient.BatchGetCompanyId(ctx, &organizationsvcpb.BatchGetCompanyIdRequest{
		BusinessIds: lo.Uniq(businessIDs),
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return rsp.GetBusinessCompanyIdMap(), nil
}

func (i *businessClient) GetBusinessInfo(ctx context.Context, businessId int64) (*businesspb.BusinessModel, error) {
	rsp, err := i.businessServiceClient.GetBusinessInfoWithOwnerEmail(ctx, &organizationsvcpb.GetBusinessInfoWithOwnerEmailRequest{
		Id: businessId,
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return rsp.GetBusiness(), nil
}

func NewBusinessClient(businessServiceClient organizationsvcpb.BusinessServiceClient) BusinessClient {
	return &businessClient{
		businessServiceClient: businessServiceClient,
	}
}
