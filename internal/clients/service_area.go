package clients

import (
	"context"

	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	"google.golang.org/genproto/googleapis/type/latlng"
	"google.golang.org/protobuf/proto"
)

type ServiceAreaClient interface {
	SearchServiceAreas(ctx context.Context, companyId int64, zipcode string, coordinate *latlng.LatLng) (*int64, error)
}

type serviceAreaClient struct {
	client organizationsvcpb.ServiceAreaServiceClient
}

func (s *serviceAreaClient) SearchServiceAreas(ctx context.Context, companyId int64, zipcode string, coordinate *latlng.LatLng) (*int64, error) {
	var lat, lng float64
	if coordinate != nil {
		lat = coordinate.GetLatitude()
		lng = coordinate.GetLongitude()
	}

	resp, err := s.client.SearchServiceAreas(ctx, &organizationsvcpb.SearchServiceAreasRequest{
		CompanyId: companyId,
		Locations: []*organizationsvcpb.SearchServiceAreasRequest_SearchLocation{
			{
				Index:   0,
				Lat:     lat,
				Lng:     lng,
				Zipcode: proto.String(zipcode),
			},
		},
	})
	if err != nil {
		return nil, err
	}

	results := resp.GetResults()
	if len(results) == 0 || results[0].GetIndex() != 0 {
		return nil, nil
	}

	areas := results[0].GetServiceAreas()
	if len(areas) == 0 {
		return nil, nil
	}

	return proto.Int64(areas[0].GetId()), nil
}

func NewServiceAreaClient(client organizationsvcpb.ServiceAreaServiceClient) ServiceAreaClient {
	return &serviceAreaClient{client: client}
}
