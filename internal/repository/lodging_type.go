package repository

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_lodging_type_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository LodgingTypeRepository
type LodgingTypeRepository interface {
	WithTx(tx *gorm.DB) LodgingTypeRepository

	Add(ctx context.Context, do *do.LodgingTypeDO) error
	Update(ctx context.Context, id int64, companyID *int64, updateOpt *do.LodgingTypeUpdateOpt) (*do.LodgingTypeDO, error)
	BatchUpdateById(ctx context.Context, companyID *int64, updateOpts []*do.LodgingTypeUpdateByIDOpt) ([]*do.LodgingTypeDO, error)
	List(ctx context.Context, companyID int64) ([]*do.LodgingTypeDO, error)
	MGet(ctx context.Context, idList []int64) ([]*do.LodgingTypeDO, error)
	Delete(ctx context.Context, id, deletedBy int64, companyID *int64) error
	GetMaxSort(ctx context.Context, companyId int64) (int32, error)
}

type lodgingTypeRepository struct {
	db *gorm.DB
}

func (l lodgingTypeRepository) WithTx(tx *gorm.DB) LodgingTypeRepository {
	return &lodgingTypeRepository{db: tx}
}

func (l lodgingTypeRepository) Add(ctx context.Context, do *do.LodgingTypeDO) error {
	po := converter.ConvertLodgingTypeDOToPO(do)
	err := l.db.WithContext(ctx).Create(po).Error
	if err != nil {
		return errors.WithStack(err)
	}
	do.ID = po.ID
	return nil
}

func (l lodgingTypeRepository) Update(ctx context.Context, id int64, companyID *int64, updateOpt *do.LodgingTypeUpdateOpt) (*do.LodgingTypeDO, error) {
	where := &models.LodgingTypeWhereOpt{
		ID:        &id,
		CompanyID: companyID,
	}
	update := converter.ConvertLodgingTypeUpdateOptDoToModel(updateOpt)
	update.UpdatedAt = lo.ToPtr(time.Now())
	result := models.LodgingType{}
	err := l.db.WithContext(ctx).Model(&result).Clauses(clause.Returning{}).Where(gormx.Query(where)).Updates(gormx.Update(update)).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return converter.ConvertLodgingTypePOToDO(&result), nil
}

func (l lodgingTypeRepository) BatchUpdateById(
	ctx context.Context,
	companyID *int64,
	updateOpts []*do.LodgingTypeUpdateByIDOpt,
) ([]*do.LodgingTypeDO, error) {
	results := make([]*do.LodgingTypeDO, 0)
	err := l.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, updateOpt := range updateOpts {
			result, err := l.WithTx(tx).
				Update(ctx, updateOpt.ID, companyID, converter.ConvertLodgingTypeUpdateByIdToUpdateOpt(updateOpt))
			if err != nil {
				return errors.WithStack(err)
			}
			results = append(results, result)
		}
		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return results, nil
}

func (l lodgingTypeRepository) List(ctx context.Context, companyID int64) ([]*do.LodgingTypeDO, error) {
	where := &models.LodgingTypeWhereOpt{
		CompanyID: &companyID,
	}
	result := make([]*models.LodgingType, 0)
	err := l.db.WithContext(ctx).Where(gormx.Query(where)).Order("sort").Find(&result).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return converter.ConvertLodgingTypePOListToDO(result), nil
}

func (l lodgingTypeRepository) MGet(ctx context.Context, idList []int64) ([]*do.LodgingTypeDO, error) {
	if len(idList) == 0 {
		return make([]*do.LodgingTypeDO, 0), nil
	}
	where := models.LodgingTypeWhereOpt{IDIn: lo.Uniq(idList)}
	result := make([]*models.LodgingType, 0)
	err := l.db.WithContext(ctx).Unscoped().Where(gormx.Query(where)).Find(&result).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return converter.ConvertLodgingTypePOListToDO(result), nil
}

func (l lodgingTypeRepository) Delete(ctx context.Context, id, deletedBy int64, companyID *int64) error {
	where := &models.LodgingTypeWhereOpt{
		CompanyID: companyID,
		ID:        &id,
	}
	update := &models.LodgingTypeUpdateOpt{
		DeletedAt: &gorm.DeletedAt{Valid: true, Time: time.Now()},
		DeletedBy: &deletedBy,
	}
	err := l.db.WithContext(ctx).Model(&models.LodgingType{}).Where(gormx.Query(where)).Updates(gormx.Update(update)).Error
	return errors.WithStack(err)
}

func (l lodgingTypeRepository) GetMaxSort(ctx context.Context, companyId int64) (int32, error) {
	where := &models.LodgingTypeWhereOpt{
		CompanyID: &companyId,
	}
	result := models.LodgingType{}
	tx := l.db.WithContext(ctx).Model(&models.LodgingType{}).Where(gormx.Query(where)).Order("sort desc").Limit(1).Find(&result)
	if tx.Error != nil {
		return 0, errors.WithStack(tx.Error)
	}
	if tx.RowsAffected == 0 {
		return 0, nil
	}
	return result.Sort, nil
}

func NewLodgingTypeRepository(db *gorm.DB) LodgingTypeRepository {
	return &lodgingTypeRepository{db: db}
}
