package repository

import (
	"context"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_service_staff_availability_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceStaffAvailabilityRepository
type ServiceStaffAvailabilityRepository interface {
	BatchCreateStaffAvailability(ctx context.Context, serviceId int64, staffId []int64) (int64, error)
	GetAvailableStaffsByServiceID(ctx context.Context, serviceId int64) ([]int64, error)
	GetAvailableStaffRulesByServiceIDList(ctx context.Context, serviceIds []int64) ([]do.ServiceStaffAvailabilityDO, error)
	UpdateServiceStaffAvailability(ctx context.Context, serviceId int64, staffId []int64) (int64, error)
	RemoveServiceStaffAvailability(ctx context.Context, serviceId int64) error
	RemoveServiceStaffAvailabilityByStaffIds(ctx context.Context, serviceId int64, staffIds []int64) error
}

type serviceStaffAvailabilityRepository struct {
	db *gorm.DB
}

func NewServiceStaffAvailabilityRepository(db *gorm.DB) ServiceStaffAvailabilityRepository {
	return &serviceStaffAvailabilityRepository{db: db}
}

func (s serviceStaffAvailabilityRepository) BatchCreateStaffAvailability(ctx context.Context, serviceId int64, staffIdList []int64) (int64, error) {
	var serviceStaffAvailabilities []*models.ServiceStaffAvailability
	for _, staffId := range staffIdList {
		serviceStaffAvailability := &models.ServiceStaffAvailability{
			StaffID:   staffId,
			ServiceID: serviceId,
		}
		serviceStaffAvailabilities = append(serviceStaffAvailabilities, serviceStaffAvailability)
	}
	result := s.db.WithContext(ctx).Create(&serviceStaffAvailabilities)
	if result.Error != nil {
		return 0, result.Error
	}

	return result.RowsAffected, nil
}

func (s serviceStaffAvailabilityRepository) GetAvailableStaffsByServiceID(ctx context.Context, serviceId int64) ([]int64, error) {
	var staffIds []int64
	result := s.db.WithContext(ctx).Model(&models.ServiceStaffAvailability{}).Where("service_id = ?", serviceId).Pluck("staff_id", &staffIds)
	if result.Error != nil {
		return nil, result.Error
	}

	return staffIds, nil
}

func (s serviceStaffAvailabilityRepository) GetAvailableStaffRulesByServiceIDList(ctx context.Context, serviceIds []int64) ([]do.ServiceStaffAvailabilityDO, error) {
	var staffModels []*models.ServiceStaffAvailability
	result := s.db.WithContext(ctx).Model(&models.ServiceStaffAvailability{}).Where("service_id IN ?", serviceIds).Find(&staffModels)
	if result.Error != nil {
		return nil, result.Error
	}

	var staffIds []do.ServiceStaffAvailabilityDO
	for _, staffModel := range staffModels {
		staffIds = append(staffIds, do.ServiceStaffAvailabilityDO{
			StaffId:   staffModel.StaffID,
			ServiceId: staffModel.ServiceID,
		})
	}

	return staffIds, nil
}

func (s serviceStaffAvailabilityRepository) UpdateServiceStaffAvailability(ctx context.Context, serviceId int64, staffIdList []int64) (int64, error) {
	result := s.db.WithContext(ctx).Model(&models.ServiceStaffAvailability{}).Where("service_id = ?", serviceId).Delete(&models.ServiceStaffAvailability{})
	if result.Error != nil {
		return 0, result.Error
	}

	if len(staffIdList) == 0 {
		return 0, nil
	}

	var serviceStaffAvailabilities []*models.ServiceStaffAvailability
	for _, staffId := range staffIdList {
		serviceStaffAvailability := &models.ServiceStaffAvailability{
			StaffID:   staffId,
			ServiceID: serviceId,
		}
		serviceStaffAvailabilities = append(serviceStaffAvailabilities, serviceStaffAvailability)
	}
	result = s.db.WithContext(ctx).Create(&serviceStaffAvailabilities)
	if result.Error != nil {
		return 0, result.Error
	}

	// delete no available staff override rule
	if err := s.db.WithContext(ctx).Model(&models.ServiceStaffOverrideRule{}).Where("service_id = ?", serviceId).Where("staff_id NOT IN ?", staffIdList).Delete(&models.ServiceStaffOverrideRule{}).Error; err != nil {
		return 0, err
	}

	return result.RowsAffected, nil
}

func (s serviceStaffAvailabilityRepository) RemoveServiceStaffAvailability(ctx context.Context, serviceId int64) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("service_id = ?", serviceId).Delete(&models.ServiceStaffAvailability{}).Error; err != nil {
			return err
		}

		return nil
	})
}

func (s serviceStaffAvailabilityRepository) RemoveServiceStaffAvailabilityByStaffIds(ctx context.Context, serviceId int64, staffIds []int64) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if serviceId != 0 {
			tx = tx.Where("service_id = ?", serviceId)
		}
		if err := tx.Where("staff_id IN ?", staffIds).Delete(&models.ServiceStaffAvailability{}).Error; err != nil {
			return err
		}

		return nil
	})
}
