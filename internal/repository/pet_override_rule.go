package repository

//type PetOverrideRuleRepository interface {
//	ListPetOverrideRules(ctx context.Context, companyId int64, serviceIds []int64) ([]do.PetOverrideRule, error)
//}
//
//type petOverrideRuleRepository struct {
//	db *gorm.DB
//}

//func (p petOverrideRuleRepository) ListPetOverrideRules(ctx context.Context, companyId int64, serviceIds []int64) ([]do.PetOverrideRule, error) {
//	var petOverrideRules []models.MoeGroomingCustomerService
//	db := p.db.WithContext(ctx).Where("company_id = ?", companyId)
//
//	if len(serviceIds) > 0 {
//		db = db.Where("service_id IN ?", serviceIds)
//	}
//
//	if err := db.Find(&petOverrideRules).Error; err != nil {
//		return nil, err
//	}
//
//	return petOverrideRules, nil
//}
