package repository

import (
	"context"

	"github.com/DataDog/appsec-internal-go/log"
	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"

	eventbus "github.com/MoeGolibrary/go-lib/event-bus"

	"github.com/MoeGolibrary/moego-svc-offering/config"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

type Producer interface {
	Publish(ctx context.Context, data *models.Event) error
}

func NewProducer() Producer {
	cfg := config.GetConfig().EventBus

	if cfg.Enable != nil && !*cfg.Enable {
		log.Info("eventbus enable false")
		return &producer{}
	}

	p, err := eventbus.NewProducer(config.GetConfig().EventBus.GroupClass)
	if err != nil {
		panic(err)
	}
	log.Info("eventbus NewProducer")
	return &producer{
		producer: p,
	}
}

type producer struct {
	producer *eventbus.Producer[proto.Message]
}

func (p *producer) Publish(ctx context.Context, data *models.Event) error {
	return p.producer.SendMessage(ctx, &eventbus.Event[proto.Message]{
		ID:        uuid.NewString(),
		Time:      data.CreatedAt,
		Detail:    data.Payload,
		EventType: data.MessageType,
	})
}
