package repository

import (
	"context"
	"encoding/json"
	"sort"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/go-lib/zlog"
	offeringV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/constant"
	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_service_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceRepository
type ServiceRepository interface {
	AddNewService(ctx context.Context, companyId int64, service *do.ServiceDO) (int64, error)
	UpdateService(ctx context.Context, companyId int64, updateOpt *do.ServiceUpdateOpt) error
	IsServiceNameExist(ctx context.Context, companyId int64, service do.UniqueService) (bool, error)
	GetServicesWithFilterPaginated(ctx context.Context, companyId int64, filter do.ServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest, orderBy offeringV1.ServiceOrderByType) (int64, []*do.ServiceDO, error)
	GetServiceWithOverrideRule(ctx context.Context, companyId int64, businessId *int64, serviceIdWithPetIdList map[int64][]int64) (int64, []do.ServiceWithOverrideRules, error)
	GetCustomizedServiceList(ctx context.Context, companyId int64, petId int64) ([]do.PetCustomizedRecord, error)
	GetBriefServiceListWithServiceIds(ctx context.Context, serviceIdList []int64) ([]do.ServiceBrief, error)
	GetApplicableService(ctx context.Context, companyId, petId int64, applicableServiceFilter *do.ApplicableServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []do.ServiceWithOverrideRules, error)
	GetApplicableAddon(ctx context.Context, companyId, petId int64, applicableAddonFilter *do.ApplicableServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []do.ServiceWithOverrideRules, error)
	OverrideServiceByPet(ctx context.Context, companyId int64, serviceWithPetOverrideRules map[int64][]do.PetOverrideRule) error
	GetAllBriefServices(ctx context.Context, companyId int64) ([]do.ServiceBrief, error)
	IsServiceIdInCompany(ctx context.Context, serviceId int64, companyId int64, withDeleted bool) (bool, error)
	GetServiceDetail(ctx context.Context, serviceId int64) (*do.ServiceDO, error)

	// apis after refactoring, we should use db models as the input and output
	GetService(ctx context.Context, serviceId int64) (*models.MoeGroomingService, error)

	UpdateAllServiceByCompanyId(ctx context.Context, companyID int64, updateOpt *do.ServiceUpdateOpt) error
}

type serviceRepository struct {
	db *gorm.DB
}

func (s *serviceRepository) GetService(ctx context.Context, serviceId int64) (*models.MoeGroomingService, error) {
	service := models.MoeGroomingService{}
	if err := s.db.WithContext(ctx).First(&service, serviceId).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}

	return &service, nil
}

func (s *serviceRepository) IsServiceIdInCompany(ctx context.Context, serviceId int64, companyId int64, withDeleted bool) (bool, error) {
	var service models.MoeGroomingService
	whereOpt := &models.ServiceWhereOpt{
		Id:        proto.Int64(serviceId),
		CompanyID: proto.Int64(companyId),
	}
	if !withDeleted {
		whereOpt.Status = proto.Int32(constant.NormalStatus)
	}
	if err := s.db.WithContext(ctx).Model(&models.MoeGroomingService{}).Where(gormx.Query(whereOpt)).First(&service).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil
		}
		return false, errors.WithStack(err)
	}
	return true, nil
}

func (s *serviceRepository) GetApplicableAddon(ctx context.Context, companyId, petId int64, applicableServiceQueryFilter *do.ApplicableServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest) (total int64, addonList []do.ServiceWithOverrideRules, err error) {
	addonModelList := make([]models.MoeGroomingService, 0)
	whereOpt := &models.ServiceWhereOpt{
		ServiceTypesIn: []offeringV1.ServiceType{offeringV1.ServiceType_ADDON},
		Inactive:       applicableServiceQueryFilter.Inactive,
		Status:         proto.Int32(constant.NormalStatus),
		CompanyID:      proto.Int64(companyId),
		NameLike:       applicableServiceQueryFilter.Keyword,
	}
	if err := s.db.WithContext(ctx).Model(&models.MoeGroomingService{}).Where(gormx.Query(whereOpt)).Find(&addonModelList).Error; err != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return 0, nil, err
	}
	if len(addonModelList) == 0 {
		return 0, nil, nil
	}

	// filter by location
	if applicableServiceQueryFilter.BusinessId != nil {
		addonModelList, err = s.filterServiceByBusinessIDs(ctx, companyId, []int64{*applicableServiceQueryFilter.BusinessId}, addonModelList)
		if err != nil {
			return
		}
		if len(addonModelList) == 0 {
			return 0, nil, nil
		}
	}

	// filter by pet
	if applicableServiceQueryFilter.PetFilter != nil {
		addonModelList, err = s.filterServiceByPetInfo(ctx, companyId, *applicableServiceQueryFilter.PetFilter, addonModelList)
		if err != nil {
			return
		}
		if len(addonModelList) == 0 {
			return 0, nil, nil
		}
	}

	// filter by selected service
	if applicableServiceQueryFilter.SelectedServiceFilter != nil {
		addonModelList, err = s.filterAddonBySelectedService(ctx, companyId, *applicableServiceQueryFilter.SelectedServiceFilter, addonModelList)
		if err != nil {
			return
		}
		if len(addonModelList) == 0 {
			return 0, nil, nil
		}
	}

	// get override info
	serviceIdList := lo.Map(addonModelList, func(service models.MoeGroomingService, _ int) int64 {
		return int64(service.ID)
	})
	serviceIdWithPetIdList := make(map[int64][]int64)
	for _, serviceId := range serviceIdList {
		serviceIdWithPetIdList[serviceId] = []int64{petId}
	}
	total = int64(len(addonModelList))

	addonModelList, err = s.sortAndPaginateServiceList(ctx, companyId, addonModelList, paginationRequest, offeringV1.ServiceOrderByType_DEFAULT)
	if err != nil {
		return 0, nil, errors.WithStack(err)
	}

	locationOverrideListByServiceID := make(map[int64][]do.BusinessOverrideRule)
	if applicableServiceQueryFilter.BusinessId != nil {
		locationOverrideListByServiceID, err = s.getLocationOverrideInfo(ctx, companyId, []int64{*applicableServiceQueryFilter.BusinessId}, serviceIdList)
		if err != nil {
			return 0, nil, errors.WithStack(err)
		}
	}

	petOverrideListByServiceID, err := s.getPetOverrideInfo(ctx, companyId, serviceIdWithPetIdList)
	if err != nil {
		return 0, nil, errors.WithStack(err)
	}

	result := make([]do.ServiceWithOverrideRules, 0, len(addonModelList))
	for _, service := range addonModelList {
		result = append(result, do.ServiceWithOverrideRules{
			ServiceID:               int64(service.ID),
			Name:                    service.Name,
			Description:             service.GetDescription(),
			Price:                   service.Price,
			PriceUnit:               offeringV1.ServicePriceUnit(*service.PriceUnit),
			Duration:                proto.Int32(service.Duration),
			TaxID:                   int64(service.TaxID),
			ServiceType:             offeringV1.ServiceType(*service.Type),
			ServiceItemType:         offeringV1.ServiceItemType(*service.ServiceItemType),
			CategoryID:              int64(service.CategoryID),
			RequireDedicatedStaff:   *service.RequireDedicatedStaff,
			RequireDedicatedLodging: *service.RequireDedicatedLodging,
			Inactive:                *service.Inactive,
			PetOverrideRules:        petOverrideListByServiceID[int64(service.ID)],
			BusinessOverrideRules:   locationOverrideListByServiceID[int64(service.ID)],
			MaxDuration:             service.MaxDuration,
			Images:                  service.GetImages(),
			LodgingFilter:           *service.LodgingFilter,
			CustomizedLodgings:      service.GetCustomizedLodgingList(),
			AdditionalServiceRule:   service.AdditionalServiceRule,
		})
	}

	return total, result, nil
}

func (s *serviceRepository) GetAllBriefServices(ctx context.Context, companyId int64) ([]do.ServiceBrief, error) {
	serviceModels := make([]models.MoeGroomingService, 0)
	if err := s.db.WithContext(ctx).Model(&models.MoeGroomingService{}).Where(gormx.Query(models.ServiceWhereOpt{
		CompanyID: proto.Int64(companyId),
		Status:    proto.Int32(constant.NormalStatus),
		Inactive:  proto.Bool(false),
	})).Find(&serviceModels).Error; err != nil {
		zlog.Error(ctx, "failed to get normal services", zap.Error(err))
		return nil, err
	}

	return lo.Map(serviceModels, func(service models.MoeGroomingService, _ int) do.ServiceBrief {
		return converter.ConvertServiceModelToBriefDO(service)
	}), nil
}

func (s *serviceRepository) OverrideServiceByPet(ctx context.Context, companyId int64, serviceWithPetOverrideRules map[int64][]do.PetOverrideRule) error {
	if len(serviceWithPetOverrideRules) == 0 {
		return nil
	}

	serviceBriefList, err := s.GetBriefServiceListWithServiceIds(ctx, lo.Keys(serviceWithPetOverrideRules))
	if err != nil {
		return err
	}
	serviceBriefByID := lo.SliceToMap(serviceBriefList, func(service do.ServiceBrief) (int64, do.ServiceBrief) {
		return service.ServiceId, service
	})

	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for serviceID, petOverrideRuleList := range serviceWithPetOverrideRules {
			serviceBrief := serviceBriefByID[serviceID]
			for _, petOverrideRule := range petOverrideRuleList {
				record := &models.MoeGroomingCustomerService{
					PetID:         int32(petOverrideRule.PetID),
					ServiceID:     int32(serviceID),
					ServiceName:   serviceBrief.Name,
					ServiceDetail: proto.String(serviceBrief.Description),
					ServiceType:   int32(serviceBrief.ServiceType),
					CategoryID:    int32(serviceBrief.CategoryId),
					ServiceTime:   0,
					ServiceFee:    nil,
					SaveType:      nil,
					Status:        proto.Int32(constant.NormalStatus),
					CreateTime:    time.Now().Unix(),
					UpdateTime:    time.Now().Unix(),
					CompanyID:     companyId,
				}
				if petOverrideRule.Price != nil {
					// 先删除旧记录，再添加新记录
					if err := tx.Where(gormx.Query(models.MoeGroomingCustomerServicesWhereOpt{
						CompanyId: proto.Int64(companyId),
						ServiceId: proto.Int64(serviceID),
						PetId:     proto.Int64(petOverrideRule.PetID),
						SaveType:  proto.Int32(constant.SaveTypePrice),
					})).Delete(&models.MoeGroomingCustomerService{}).Error; err != nil {
						zlog.Error(ctx, "failed to delete pet override rule", zap.Error(err))
						return err
					}
					record.ID = 0
					record.ServiceTime = 0
					record.ServiceFee = petOverrideRule.Price
					record.SaveType = proto.Int32(constant.SaveTypePrice)
					if err := tx.Create(record).Error; err != nil {
						zlog.Error(ctx, "failed to override service by pet", zap.Error(err))
						return err
					}
				}
				if petOverrideRule.Duration != nil {
					// 先删除旧记录，再添加新记录
					if err := tx.Where(gormx.Query(models.MoeGroomingCustomerServicesWhereOpt{
						CompanyId: proto.Int64(companyId),
						ServiceId: proto.Int64(serviceID),
						PetId:     proto.Int64(petOverrideRule.PetID),
						SaveType:  proto.Int32(constant.SaveTypeDuration),
					})).Delete(&models.MoeGroomingCustomerService{}).Error; err != nil {
						zlog.Error(ctx, "failed to delete pet override rule", zap.Error(err))
						return err
					}
					record.ID = 0
					record.ServiceTime = *petOverrideRule.Duration
					record.ServiceFee = nil
					record.SaveType = proto.Int32(constant.SaveTypeDuration)
					if err := tx.Create(record).Error; err != nil {
						zlog.Error(ctx, "failed to override service by pet", zap.Error(err))
						return err
					}
				}
			}
		}
		return nil
	}); err != nil {
		zlog.Error(ctx, "failed to override service by pet", zap.Error(err))
		return err
	}

	return nil
}

func (s *serviceRepository) GetApplicableService(ctx context.Context, companyId, petId int64, applicableServiceFilter *do.ApplicableServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest) (total int64, serviceList []do.ServiceWithOverrideRules, err error) {
	serviceModelList := make([]models.MoeGroomingService, 0)
	whereOpt := &models.ServiceWhereOpt{
		ServiceTypesIn: []offeringV1.ServiceType{offeringV1.ServiceType_SERVICE},
		Inactive:       applicableServiceFilter.Inactive,
		Status:         proto.Int32(constant.NormalStatus),
		CompanyID:      proto.Int64(companyId),
		NameLike:       applicableServiceFilter.Keyword,
	}
	if applicableServiceFilter.ServiceItemType != nil {
		whereOpt.ServiceItemTypesIn = []offeringV1.ServiceItemType{*applicableServiceFilter.ServiceItemType}
	}
	if err := s.db.WithContext(ctx).Model(&models.MoeGroomingService{}).Where(gormx.Query(whereOpt)).Find(&serviceModelList).Error; err != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return 0, nil, err
	}
	if len(serviceModelList) == 0 {
		return 0, nil, nil
	}

	// filter by location
	if applicableServiceFilter.BusinessId != nil {
		serviceModelList, err = s.filterServiceByBusinessIDs(ctx, companyId, []int64{*applicableServiceFilter.BusinessId}, serviceModelList)
		if err != nil {
			return
		}
		if len(serviceModelList) == 0 {
			return 0, nil, nil
		}
	}

	// filter by pet
	if applicableServiceFilter.PetFilter != nil {
		serviceModelList, err = s.filterServiceByPetInfo(ctx, companyId, *applicableServiceFilter.PetFilter, serviceModelList)
		if err != nil {
			return
		}
		if len(serviceModelList) == 0 {
			return 0, nil, nil
		}
	}

	// filter by selected lodging
	if applicableServiceFilter.LodgingFilter != nil {
		serviceModelList, err = s.filterServiceByLodging(ctx, companyId, *applicableServiceFilter.LodgingFilter, serviceModelList)
		if err != nil {
			return
		}
		if len(serviceModelList) == 0 {
			return 0, nil, nil
		}
	}

	// get override info
	serviceIdList := lo.Map(serviceModelList, func(service models.MoeGroomingService, _ int) int64 {
		return int64(service.ID)
	})
	serviceIdWithPetIdList := make(map[int64][]int64)
	for _, serviceId := range serviceIdList {
		serviceIdWithPetIdList[serviceId] = []int64{petId}
	}
	total = int64(len(serviceModelList))

	serviceModelList, err = s.sortAndPaginateServiceList(ctx, companyId, serviceModelList, paginationRequest, offeringV1.ServiceOrderByType_DEFAULT)
	if err != nil {
		return 0, nil, errors.WithStack(err)
	}

	locationOverrideListByServiceID := make(map[int64][]do.BusinessOverrideRule)
	if applicableServiceFilter.BusinessId != nil {
		locationOverrideListByServiceID, err = s.getLocationOverrideInfo(ctx, companyId, []int64{*applicableServiceFilter.BusinessId}, serviceIdList)
		if err != nil {
			return 0, nil, errors.WithStack(err)
		}
	}

	petOverrideListByServiceID, err := s.getPetOverrideInfo(ctx, companyId, serviceIdWithPetIdList)
	if err != nil {
		return 0, nil, errors.WithStack(err)
	}

	result := make([]do.ServiceWithOverrideRules, 0, len(serviceModelList))
	for _, service := range serviceModelList {
		result = append(result, do.ServiceWithOverrideRules{
			ServiceID:               int64(service.ID),
			Name:                    service.Name,
			Description:             service.GetDescription(),
			Price:                   service.Price,
			PriceUnit:               offeringV1.ServicePriceUnit(*service.PriceUnit),
			Duration:                proto.Int32(service.Duration),
			TaxID:                   int64(service.TaxID),
			ServiceType:             offeringV1.ServiceType(*service.Type),
			ServiceItemType:         offeringV1.ServiceItemType(*service.ServiceItemType),
			CategoryID:              int64(service.CategoryID),
			RequireDedicatedStaff:   *service.RequireDedicatedStaff,
			RequireDedicatedLodging: *service.RequireDedicatedLodging,
			Inactive:                *service.Inactive,
			PetOverrideRules:        petOverrideListByServiceID[int64(service.ID)],
			BusinessOverrideRules:   locationOverrideListByServiceID[int64(service.ID)],
			MaxDuration:             service.MaxDuration,
			Images:                  service.GetImages(),
			AvailableForAllStaff:    *service.IsAllStaff,
			LodgingFilter:           *service.LodgingFilter,
			CustomizedLodgings:      service.GetCustomizedLodgingList(),
			AdditionalServiceRule:   service.AdditionalServiceRule,
		})
	}

	return total, result, nil
}

func (s *serviceRepository) GetServiceWithOverrideRule(ctx context.Context, companyId int64, businessId *int64, serviceIdWithPetIdList map[int64][]int64) (int64, []do.ServiceWithOverrideRules, error) {
	serviceList := make([]models.MoeGroomingService, 0)
	if err := s.db.WithContext(ctx).Model(&models.MoeGroomingService{}).Where(gormx.Query(models.ServiceWhereOpt{
		ServiceIdListIn: lo.Keys(serviceIdWithPetIdList),
		CompanyID:       proto.Int64(companyId),
	})).Find(&serviceList).Error; err != nil {
		return 0, nil, errors.WithStack(err)
	}
	if len(serviceList) == 0 {
		return 0, nil, nil
	}
	count := int64(len(serviceList))

	locationOverrideListByServiceID := make(map[int64][]do.BusinessOverrideRule)
	if businessId != nil {
		var err error

		locationOverrideListByServiceID, err = s.getLocationOverrideInfo(ctx, companyId, []int64{*businessId}, lo.Keys(serviceIdWithPetIdList))
		if err != nil {
			return 0, nil, errors.WithStack(err)
		}
	}

	petOverrideListByServiceID, err := s.getPetOverrideInfo(ctx, companyId, serviceIdWithPetIdList)
	if err != nil {
		return 0, nil, errors.WithStack(err)
	}

	result := make([]do.ServiceWithOverrideRules, 0, len(serviceList))
	for _, service := range serviceList {
		result = append(result, do.ServiceWithOverrideRules{
			ServiceID:               int64(service.ID),
			Name:                    service.Name,
			Description:             service.GetDescription(),
			Price:                   service.Price,
			PriceUnit:               offeringV1.ServicePriceUnit(*service.PriceUnit),
			Duration:                proto.Int32(service.Duration),
			TaxID:                   int64(service.TaxID),
			ServiceType:             offeringV1.ServiceType(*service.Type),
			ServiceItemType:         offeringV1.ServiceItemType(*service.ServiceItemType),
			CategoryID:              int64(service.CategoryID),
			RequireDedicatedStaff:   *service.RequireDedicatedStaff,
			RequireDedicatedLodging: *service.RequireDedicatedLodging,
			Inactive:                *service.Inactive,
			PetOverrideRules:        petOverrideListByServiceID[int64(service.ID)],
			BusinessOverrideRules:   locationOverrideListByServiceID[int64(service.ID)],
			MaxDuration:             service.MaxDuration,
			Images:                  service.GetImages(),
			LodgingFilter:           *service.LodgingFilter,
			CustomizedLodgings:      service.GetCustomizedLodgingList(),
		})
	}
	return count, result, nil
}

func (s *serviceRepository) AddNewService(ctx context.Context, companyID int64, service *do.ServiceDO) (int64, error) {
	serviceModel := service.ToModel()
	serviceModel.CompanyID = companyID
	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// create service
		err := tx.Create(&serviceModel).Error
		if err != nil {
			return err
		}

		// update sort, 将 sort 更新成主键 ID（迁移前的旧逻辑）
		tx.Model(serviceModel).Update("sort", serviceModel.ID)

		// create breed binding
		if service.NeedBreedFilter() {
			if err := s.createBreedBinding(tx, companyID, int64(serviceModel.ID), service.Availability.CustomizedBreed); err != nil {
				zlog.Error(ctx, "failed to update breed binding", zap.Error(err))
				return err
			}
		}

		// create coat filter
		if service.NeedCoatFilter() {
			if err := s.createCoatFilter(tx, companyID, int64(serviceModel.ID), service.Availability.CustomizedCoat); err != nil {
				zlog.Error(ctx, "failed to update coat filter", zap.Error(err))
				return err
			}
		}

		// create location override rule
		availableBusinessIdList := service.Availability.AvailableBusinessIdList
		if *service.Availability.IsAllLocation {
			availableBusinessIdList = nil
		}
		if err := s.createLocationOverride(tx, companyID, int64(serviceModel.ID), availableBusinessIdList, service.Availability.LocationOverrideList); err != nil {
			zlog.Error(ctx, "failed to update location override", zap.Error(err))
			return err
		}

		// create addon availability
		if service.ServiceType == offeringV1.ServiceType_ADDON && service.AddonAvailability != nil && service.AddonAvailability.GetServiceFilter() {
			if err := s.createAddonAvailability(tx, companyID, int64(serviceModel.ID), service.AddonAvailability.ServiceFilterList); err != nil {
				zlog.Error(ctx, "failed to update addon availability", zap.Error(err))
				return err
			}
		}
		// create auto rollover rule
		if service.AutoRolloverRule != nil {
			if err := s.createAutoRolloverRule(tx, int64(serviceModel.ID), *service.AutoRolloverRule); err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		zlog.Error(ctx, "failed to create service", zap.Error(err))
		return 0, err
	}
	return int64(serviceModel.ID), nil
}

func (s *serviceRepository) UpdateService(ctx context.Context, companyID int64, updateOpt *do.ServiceUpdateOpt) error {
	serviceUpdateOpt := updateOpt.ToModel()
	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// update service
		if err := tx.Model(&models.MoeGroomingService{}).Where(gormx.Query(models.ServiceWhereOpt{
			ServiceIdListIn: []int64{updateOpt.ServiceID},
			CompanyID:       proto.Int64(companyID),
		})).Updates(gormx.Update(&serviceUpdateOpt)).Error; err != nil {
			zlog.Error(ctx, "failed to update service", zap.Error(err))
			return err
		}

		// update breed binding
		if updateOpt.NeedBreedFilter() {
			if err := s.updateBreedBinding(tx, companyID, updateOpt.ServiceID, updateOpt.Availability.CustomizedBreed); err != nil {
				zlog.Error(ctx, "failed to update breed binding", zap.Error(err))
				return err
			}
		}

		// update coat filter
		if updateOpt.NeedCoatFilter() {
			if err := s.updateCoatFilter(tx, companyID, updateOpt.ServiceID, updateOpt.Availability.CustomizedCoat); err != nil {
				zlog.Error(ctx, "failed to update coat filter", zap.Error(err))
				return err
			}
		}

		// update location override rule
		availableBusinessIdList := updateOpt.Availability.AvailableBusinessIdList
		if *updateOpt.Availability.IsAllLocation {
			availableBusinessIdList = nil
		}
		if updateOpt.NeedLocationOverride() {
			if err := s.updateLocationOverride(tx, companyID, updateOpt.ServiceID, availableBusinessIdList, updateOpt.Availability.LocationOverrideList); err != nil {
				zlog.Error(ctx, "failed to update location override", zap.Error(err))
				return err
			}
		}

		// update addon availability
		if updateOpt.AddonAvailability != nil && updateOpt.AddonAvailability.GetServiceFilter() {
			if err := s.updateAddonAvailability(tx, companyID, updateOpt.ServiceID, updateOpt.AddonAvailability.ServiceFilterList); err != nil {
				zlog.Error(ctx, "failed to update addon availability", zap.Error(err))
				return err
			}
		}

		// update auto rollover rule
		if updateOpt.AutoRolloverRule != nil {
			if err := s.updateAutoRolloverRule(tx, updateOpt.ServiceID, *updateOpt.AutoRolloverRule); err != nil {
				return err
			}
		}

		return nil
	}); err != nil {
		zlog.Error(ctx, "failed to update service", zap.Error(err))
		return err
	}
	return nil
}

func (s *serviceRepository) GetServiceDetail(ctx context.Context, serviceId int64) (*do.ServiceDO, error) {
	serviceModel := models.MoeGroomingService{}
	if err := s.db.WithContext(ctx).First(&serviceModel, serviceId).Error; err != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return nil, errors.WithStack(err)
	}

	breedBindingList := make([]models.MoeGroomingServiceBreedBinding, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(
		models.BreedBindingWhereOpt{
			CompanyID:   proto.Int64(serviceModel.CompanyID),
			ServiceIDIn: []int64{serviceId},
			Status:      proto.Int32(constant.BreedBindingStatusNormal),
		},
	)).Find(&breedBindingList).Error; err != nil {
		zlog.Error(ctx, "failed to get breed binding list", zap.Error(err))
		return nil, errors.WithStack(err)
	}
	breedBindingByServiceID := lo.GroupBy(breedBindingList, func(breedBinding models.MoeGroomingServiceBreedBinding) int64 {
		return int64(breedBinding.ServiceID)
	})

	coatBindingList := make([]models.MoeGroomingServiceCoatBinding, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.CoatBindingWhereOpt{
		CompanyID:   proto.Int64(serviceModel.CompanyID),
		ServiceIdIn: []int64{serviceId},
	})).Find(&coatBindingList).Error; err != nil {
		zlog.Error(ctx, "failed to get coat binding list", zap.Error(err))
		return nil, errors.WithStack(err)
	}
	coatBindingByServiceID := lo.SliceToMap(coatBindingList, func(coatBinding models.MoeGroomingServiceCoatBinding) (int64, *models.MoeGroomingServiceCoatBinding) {
		return int64(coatBinding.ServiceID), &coatBinding
	})

	locationOverrideList := make([]models.MoeGroomingServiceLocation, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.ServiceLocationWhereOpt{
		CompanyID:   proto.Int64(serviceModel.CompanyID),
		ServiceIDIn: []int64{serviceId},
		IsDeleted:   proto.Bool(false),
	})).Find(&locationOverrideList).Error; err != nil {
		zlog.Error(ctx, "failed to get location override list", zap.Error(err))
		return nil, errors.WithStack(err)
	}
	locationOverrideByServiceID := lo.GroupBy(locationOverrideList, func(locationOverride models.MoeGroomingServiceLocation) int64 {
		return int64(locationOverride.ServiceID)
	})

	addonAvailabilityRules := make([]models.MoeGroomingAddonApplicableService, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.MoeGroomingAddonApplicableServiceWhereOpt{
		CompanyID: proto.Int64(serviceModel.CompanyID),
		AddonIDIn: []int64{serviceId},
	})).Find(&addonAvailabilityRules).Error; err != nil {
		zlog.Error(ctx, "failed to get addon availability rules", zap.Error(err))
		return nil, errors.WithStack(err)
	}
	addonAvailabilityRuleByServiceID := lo.GroupBy(addonAvailabilityRules, func(addonAvailabilityRule models.MoeGroomingAddonApplicableService) int64 {
		return addonAvailabilityRule.AddonID
	})

	autoRolloverRule := make([]models.MoeAutoRollover, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.AutoRolloverWhereOpt{ServiceIDIn: []int64{serviceId}})).Find(&autoRolloverRule).Error; err != nil {
		return nil, errors.WithStack(err)
	}
	autoRolloverRuleByServiceID := lo.SliceToMap(autoRolloverRule, func(autoRolloverRule models.MoeAutoRollover) (int64, *models.MoeAutoRollover) {
		return autoRolloverRule.ServiceID, &autoRolloverRule
	})

	return converter.ConvertDBModelToServiceDO(serviceModel,
		breedBindingByServiceID[int64(serviceModel.ID)],
		coatBindingByServiceID[int64(serviceModel.ID)],
		locationOverrideByServiceID[int64(serviceModel.ID)],
		addonAvailabilityRuleByServiceID[int64(serviceModel.ID)],
		autoRolloverRuleByServiceID[int64(serviceModel.ID)]), nil
}

func (s *serviceRepository) GetServicesWithFilterPaginated(ctx context.Context, companyID int64, filter do.ServiceQueryFilter, paginationRequest *utilsV2.PaginationRequest, orderBy offeringV1.ServiceOrderByType) (total int64, serviceDO []*do.ServiceDO, err error) {
	serviceModelList := make([]models.MoeGroomingService, 0)
	result := s.db.WithContext(ctx).Model(&models.MoeGroomingService{}).Where(gormx.Query(models.ServiceWhereOpt{
		ServiceIdListIn:    filter.ServiceIdList,
		ServiceItemTypesIn: filter.ServiceItemTypes,
		ServiceTypesIn:     filter.ServiceTypes,
		Inactive:           filter.Inactive,
		NameLike:           filter.Keyword,
		Status:             proto.Int32(constant.NormalStatus),
		CompanyID:          proto.Int64(companyID),
	})).Find(&serviceModelList)
	if result.Error != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(result.Error))
		return 0, nil, result.Error
	}

	serviceModelList, err = s.filterServiceByPrerequisiteClassIds(filter.FilterPrerequisiteClasses, filter.PrerequisiteClassIds, serviceModelList)
	if err != nil {
		return 0, nil, err
	}
	if len(filter.BusinessIDList) > 0 {
		serviceModelList, err = s.filterServiceByBusinessIDs(ctx, companyID, filter.BusinessIDList, serviceModelList)
		if err != nil {
			return 0, nil, err
		}
	}
	count := int64(len(serviceModelList))

	serviceModelList, err = s.sortAndPaginateServiceList(ctx, companyID, serviceModelList, paginationRequest, orderBy)
	if err != nil {
		return 0, nil, errors.WithStack(err)
	}

	if len(serviceModelList) == 0 {
		return count, make([]*do.ServiceDO, 0), nil
	}
	serviceIDList := extractServiceIdList(serviceModelList)

	breedBindingList := make([]models.MoeGroomingServiceBreedBinding, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(
		models.BreedBindingWhereOpt{
			CompanyID:   proto.Int64(companyID),
			ServiceIDIn: serviceIDList,
			Status:      proto.Int32(constant.BreedBindingStatusNormal),
		},
	)).Find(&breedBindingList).Error; err != nil {
		zlog.Error(ctx, "failed to get breed binding list", zap.Error(err))
		return 0, nil, err
	}
	breedBindingByServiceID := lo.GroupBy(breedBindingList, func(breedBinding models.MoeGroomingServiceBreedBinding) int64 {
		return int64(breedBinding.ServiceID)
	})

	coatBindingList := make([]models.MoeGroomingServiceCoatBinding, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.CoatBindingWhereOpt{
		CompanyID:   proto.Int64(companyID),
		ServiceIdIn: serviceIDList,
	})).Find(&coatBindingList).Error; err != nil {
		zlog.Error(ctx, "failed to get coat binding list", zap.Error(err))
		return 0, nil, err
	}
	coatBindingByServiceID := lo.SliceToMap(coatBindingList, func(coatBinding models.MoeGroomingServiceCoatBinding) (int64, *models.MoeGroomingServiceCoatBinding) {
		return int64(coatBinding.ServiceID), &coatBinding
	})

	locationOverrideList := make([]models.MoeGroomingServiceLocation, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.ServiceLocationWhereOpt{
		CompanyID:   proto.Int64(companyID),
		ServiceIDIn: serviceIDList,
		IsDeleted:   proto.Bool(false),
	})).Find(&locationOverrideList).Error; err != nil {
		zlog.Error(ctx, "failed to get location override list", zap.Error(err))
		return 0, nil, err
	}
	locationOverrideByServiceID := lo.GroupBy(locationOverrideList, func(locationOverride models.MoeGroomingServiceLocation) int64 {
		return int64(locationOverride.ServiceID)
	})

	addonAvailabilityRules := make([]models.MoeGroomingAddonApplicableService, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.MoeGroomingAddonApplicableServiceWhereOpt{
		CompanyID: proto.Int64(companyID),
		AddonIDIn: serviceIDList,
	})).Find(&addonAvailabilityRules).Error; err != nil {
		zlog.Error(ctx, "failed to get addon availability rules", zap.Error(err))
		return 0, nil, err
	}
	addonAvailabilityRuleByServiceID := lo.GroupBy(addonAvailabilityRules, func(addonAvailabilityRule models.MoeGroomingAddonApplicableService) int64 {
		return addonAvailabilityRule.AddonID
	})

	autoRolloverRule := make([]models.MoeAutoRollover, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.AutoRolloverWhereOpt{ServiceIDIn: serviceIDList})).Find(&autoRolloverRule).Error; err != nil {
		return 0, nil, err
	}
	autoRolloverRuleByServiceID := lo.SliceToMap(autoRolloverRule, func(autoRolloverRule models.MoeAutoRollover) (int64, *models.MoeAutoRollover) {
		return autoRolloverRule.ServiceID, &autoRolloverRule
	})

	serviceDOList := make([]*do.ServiceDO, 0, len(serviceModelList))
	for _, service := range serviceModelList {
		serviceDOList = append(serviceDOList,
			converter.ConvertDBModelToServiceDO(service,
				breedBindingByServiceID[int64(service.ID)],
				coatBindingByServiceID[int64(service.ID)],
				locationOverrideByServiceID[int64(service.ID)],
				addonAvailabilityRuleByServiceID[int64(service.ID)],
				autoRolloverRuleByServiceID[int64(service.ID)]))
	}
	return count, serviceDOList, nil
}

func (s *serviceRepository) IsServiceNameExist(ctx context.Context, companyId int64, service do.UniqueService) (bool, error) {
	var count int64
	err := s.db.WithContext(ctx).Model(&models.MoeGroomingService{}).Where(gormx.Query(&models.ServiceCategoryWhereOpt{
		CompanyID:        proto.Int64(companyId),
		Name:             proto.String(service.GetName()),
		ServiceTypes:     []offeringV1.ServiceType{service.GetServiceType()},
		ServiceItemTypes: []offeringV1.ServiceItemType{service.GetServiceItemType()},
		Status:           proto.Int32(constant.NormalStatus),
	})).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (s *serviceRepository) GetCustomizedServiceList(ctx context.Context, companyId int64, petId int64) ([]do.PetCustomizedRecord, error) {
	// 获取所有 customized 记录
	customizedServiceList := make([]models.MoeGroomingCustomerService, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.MoeGroomingCustomerServicesWhereOpt{
		CompanyId: proto.Int64(companyId),
		Status:    proto.Int32(constant.NormalStatus),
		PetId:     proto.Int64(petId),
	})).Find(&customizedServiceList).Error; err != nil {
		zlog.Error(ctx, "failed to get customized service list", zap.Error(err))
		return nil, err
	}

	return lo.Map(customizedServiceList, func(customizedService models.MoeGroomingCustomerService, _ int) do.PetCustomizedRecord {
		return do.PetCustomizedRecord{
			ServiceId:  int64(customizedService.ServiceID),
			BusinessId: int64(customizedService.BusinessID),
			Price:      customizedService.ServiceFee,
			Duration:   proto.Int32(customizedService.ServiceTime),
			SaveType:   *customizedService.SaveType,
			CreateTime: customizedService.CreateTime,
			UpdateTime: customizedService.UpdateTime,
		}
	}), nil
}

func (s *serviceRepository) GetBriefServiceListWithServiceIds(ctx context.Context, serviceIdList []int64) ([]do.ServiceBrief, error) {
	if len(serviceIdList) == 0 {
		return make([]do.ServiceBrief, 0), nil
	}

	serviceList := make([]models.MoeGroomingService, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(&models.ServiceWhereOpt{
		ServiceIdListIn: serviceIdList,
	})).Find(&serviceList).Error; err != nil {
		zlog.Error(ctx, "failed to get service list", zap.Error(err))
		return nil, err
	}
	return lo.Map(serviceList, func(service models.MoeGroomingService, _ int) do.ServiceBrief {
		return converter.ConvertServiceModelToBriefDO(service)
	}), nil
}

func (s *serviceRepository) updateBreedBinding(
	tx *gorm.DB,
	companyID int64,
	serviceID int64,
	customizedBreed []do.PetTypeAndBreedAvailabilityRule) error {
	// 将之前的绑定都删除
	if err := s.deleteBreedBindingByServiceId(tx, companyID, []int64{serviceID}); err != nil {
		return err
	}
	// 将新的绑定插入
	return s.createBreedBinding(tx, companyID, serviceID, customizedBreed)
}

func (s *serviceRepository) deleteBreedBindingByServiceId(tx *gorm.DB, companyID int64, serviceIDs []int64) error {
	if len(serviceIDs) == 0 {
		return nil
	}
	return tx.Model(&models.MoeGroomingServiceBreedBinding{}).Where(gormx.Query(models.BreedBindingWhereOpt{
		CompanyID:   proto.Int64(companyID),
		ServiceIDIn: serviceIDs,
	})).Delete(&models.MoeGroomingServiceBreedBinding{}).Error
}

func (s *serviceRepository) createBreedBinding(
	tx *gorm.DB,
	companyID int64,
	serviceID int64,
	customizedBreed []do.PetTypeAndBreedAvailabilityRule) error {
	if len(customizedBreed) == 0 {
		return nil
	}

	recordsToInsert := make([]*models.MoeGroomingServiceBreedBinding, 0, len(customizedBreed))
	for _, rule := range customizedBreed {
		record := &models.MoeGroomingServiceBreedBinding{
			ServiceID:  int32(serviceID),
			Status:     constant.BreedBindingStatusNormal,
			CreateTime: time.Now().Unix(),
			UpdateTime: time.Now().Unix(),
			IsAll:      proto.Bool(*rule.IsAll),
			PetTypeID:  proto.Int32(int32(*rule.PetTypeID)),
			BreedNames: "",
			CompanyID:  companyID,
		}
		if !*rule.IsAll {
			breedNameListJSON, _ := json.Marshal(rule.Breeds)
			record.BreedNameList = proto.String(string(breedNameListJSON))
		}
		recordsToInsert = append(recordsToInsert, record)
	}
	if err := tx.Create(recordsToInsert).Error; err != nil {
		return err
	}

	return nil
}

func (s *serviceRepository) createCoatFilter(tx *gorm.DB, companyID int64, serviceID int64, CustomizedCoat []int64) error {
	record := converter.CovertMoeGroomingServiceCoatBinding(companyID, serviceID, CustomizedCoat)
	return tx.Create(record).Error
}

func (s *serviceRepository) updateCoatFilter(tx *gorm.DB, companyID int64, serviceID int64, CustomizedCoat []int64) error {
	record := converter.CovertMoeGroomingServiceCoatBinding(companyID, serviceID, CustomizedCoat)
	if err := tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "service_id"}, {Name: "company_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"coat_id_list", "update_time"}),
	}).Create(record).Error; err != nil {
		return err
	}
	return nil
}

func (s *serviceRepository) updateLocationOverride(
	tx *gorm.DB,
	companyID int64,
	serviceID int64,
	availableBusinessIdList []int64,
	locationOverrideList []do.BusinessOverrideRule,
) error {
	// 删除之前的 override
	if err := s.deleteLocationOverrideByServiceId(tx, companyID, []int64{serviceID}); err != nil {
		return err
	}

	// 插入新的 override
	return s.createLocationOverride(tx, companyID, serviceID, availableBusinessIdList, locationOverrideList)
}

func (s *serviceRepository) deleteLocationOverrideByServiceId(tx *gorm.DB, companyID int64, serviceIDs []int64) error {
	if len(serviceIDs) == 0 {
		return nil
	}

	return tx.Where(gormx.Query(models.ServiceLocationWhereOpt{
		CompanyID:   proto.Int64(companyID),
		ServiceIDIn: serviceIDs,
	})).Delete(&models.MoeGroomingServiceLocation{}).Error
}

/***
 * @param availableBusinessIdList in case nil, will use locationOverrideList to get businessId
 */
func (s *serviceRepository) createLocationOverride(
	tx *gorm.DB,
	companyID int64,
	serviceID int64,
	availableBusinessIdList []int64,
	locationOverrideList []do.BusinessOverrideRule,
) error {
	if availableBusinessIdList == nil {
		availableBusinessIdList = lo.Map(locationOverrideList, func(rule do.BusinessOverrideRule, _ int) int64 { return rule.BusinessId })
	}
	locationRuleMap := make(map[int64]*do.BusinessOverrideRule)
	for _, rule := range locationOverrideList {
		locationRuleMap[rule.BusinessId] = &rule
	}

	recordsToInsert := make([]*models.MoeGroomingServiceLocation, 0)
	for _, businessID := range availableBusinessIdList {
		rule := locationRuleMap[businessID]
		if rule == nil {
			rule = &do.BusinessOverrideRule{
				BusinessId: businessID,
			}
		}
		recordsToInsert = append(recordsToInsert, converter.ConvertMoeGroomingServiceLocation(companyID, serviceID, rule))
	}

	if len(recordsToInsert) == 0 {
		return nil
	}
	return tx.Create(recordsToInsert).Error
}

func (s *serviceRepository) updateAddonAvailability(tx *gorm.DB, companyID int64, addonID int64, serviceFilterRules []do.AddonAvailabilityRule) error {
	// 删除之前的绑定
	if err := s.deleteAddonAvailabilityByAddOnIn(tx, companyID, []int64{addonID}); err != nil {
		return err
	}

	// 插入新的绑定
	return s.createAddonAvailability(tx, companyID, addonID, serviceFilterRules)
}

func (s *serviceRepository) deleteAddonAvailabilityByAddOnIn(tx *gorm.DB, companyID int64, addonIDs []int64) error {
	if len(addonIDs) == 0 {
		return nil
	}

	return tx.Where(gormx.Query(models.MoeGroomingAddonApplicableServiceWhereOpt{
		CompanyID: proto.Int64(companyID),
		AddonIDIn: addonIDs,
	})).Delete(&models.MoeGroomingAddonApplicableService{}).Error
}

func (s *serviceRepository) createAddonAvailability(tx *gorm.DB, companyID int64, addonID int64, serviceFilterRules []do.AddonAvailabilityRule) error {
	if len(serviceFilterRules) == 0 {
		return nil
	}

	recordsToInsert := make([]*models.MoeGroomingAddonApplicableService, 0, len(serviceFilterRules))
	for _, rule := range serviceFilterRules {
		record := &models.MoeGroomingAddonApplicableService{
			CompanyID:               companyID,
			AddonID:                 addonID,
			ServiceItemType:         int32(*rule.ServiceItemType),
			AvailableForAllServices: proto.Bool(*rule.AvailableForAllServices),
			AvailableServiceIDList:  proto.String("[]"),
		}
		if !*rule.AvailableForAllServices {
			allowedServiceListJSON, _ := json.Marshal(rule.AvailableServiceIdList)
			record.AvailableServiceIDList = proto.String(string(allowedServiceListJSON))
		}
		recordsToInsert = append(recordsToInsert, record)
	}
	if err := tx.Create(recordsToInsert).Error; err != nil {
		return err
	}
	return nil
}

func (s *serviceRepository) createAutoRolloverRule(tx *gorm.DB, serviceId int64, autoRolloverRule do.AutoRolloverRule) error {
	record := converter.ConvertMoeAutoRollover(serviceId, autoRolloverRule)
	return tx.Create(record).Error
}

func (s *serviceRepository) updateAutoRolloverRule(tx *gorm.DB, serviceId int64, autoRolloverRule do.AutoRolloverRule) error {
	record := converter.ConvertMoeAutoRollover(serviceId, autoRolloverRule)
	if err := tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "service_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"enabled", "after_minute", "target_service_id"}),
	}).Create(record).Error; err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (s *serviceRepository) filterServiceByPrerequisiteClassIds(
	filterPrerequisiteClasses *bool, prerequisiteClassIds []int64, serviceList []models.MoeGroomingService) ([]models.MoeGroomingService, error) {
	return lo.Filter(serviceList, func(service models.MoeGroomingService, _ int) bool {
		// service 未开启 prerequisite class 过滤，或者调用方不需要过滤
		if service.IsRequirePrerequisiteClass == nil || !*service.IsRequirePrerequisiteClass ||
			filterPrerequisiteClasses == nil || !*filterPrerequisiteClasses {
			return true
		}
		// 开启了 prerequisite class 过滤
		if len(service.PrerequisiteClassIds) == 0 {
			return true
		}
		if prerequisiteClassIds == nil {
			return false
		}
		for _, prerequisiteClassId := range prerequisiteClassIds {
			if lo.Contains(prerequisiteClassIds, prerequisiteClassId) {
				return true
			}
		}
		return false
	}), nil
}

func (s *serviceRepository) filterServiceByBusinessIDs(ctx context.Context, companyID int64, businessIDs []int64, list []models.MoeGroomingService) ([]models.MoeGroomingService, error) {
	result := make([]models.MoeGroomingService, 0)
	idsNeedToCheck := make([]int64, 0) // 非 available in all locations 的 service，需要校验是否在所选 business 可用
	// available in all locations
	for _, service := range list {
		if service.AvailableInAllLocation() {
			result = append(result, service)
		} else {
			idsNeedToCheck = append(idsNeedToCheck, int64(service.ID))
		}
	}
	if len(idsNeedToCheck) == 0 {
		return result, nil
	}

	locationOverrideModels := make([]models.MoeGroomingServiceLocation, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.ServiceLocationWhereOpt{
		ServiceIDIn:  idsNeedToCheck,
		CompanyID:    proto.Int64(companyID),
		BusinessIDIn: businessIDs,
		IsDeleted:    proto.Bool(false),
	})).Find(&locationOverrideModels).Error; err != nil {
		return nil, err
	}
	serviceIDList := make(map[int64]struct{}) // services those are available in certain locations
	for _, locationOverrideModel := range locationOverrideModels {
		serviceIDList[int64(locationOverrideModel.ServiceID)] = struct{}{}
	}

	for _, service := range list {
		if _, ok := serviceIDList[int64(service.ID)]; ok {
			result = append(result, service)
		}
	}

	return result, nil
}

// TODO: filter logic should be moved to service layer, not repository
func (s *serviceRepository) filterServiceByPetInfo(ctx context.Context, companyId int64, petFilter do.PetFilter, serviceList []models.MoeGroomingService) (result []models.MoeGroomingService, err error) {
	// filter by pet type & breed
	serviceList, err = s.filterServiceByPetBreed(ctx, companyId, petFilter, serviceList)
	if err != nil {
		return nil, err
	}
	if len(serviceList) == 0 {
		return nil, nil
	}

	// filter by pet weight
	serviceList, err = s.filterServiceByPetSize(ctx, companyId, petFilter, serviceList)
	if err != nil {
		return nil, err
	}
	if len(serviceList) == 0 {
		return nil, nil
	}

	// filter by pet coat
	serviceList, err = s.filterServiceByPetCoat(ctx, companyId, petFilter, serviceList)
	if err != nil {
		return nil, err
	}
	if len(serviceList) == 0 {
		return nil, nil
	}

	// filter by pet codes
	// TODO: move all the filter logic to service layer, instead of repository layer
	serviceList, err = s.filterServiceByPetCodes(ctx, petFilter, serviceList)
	if err != nil {
		return nil, err
	}
	if len(serviceList) == 0 {
		return nil, nil
	}

	return serviceList, nil
}

func (s *serviceRepository) filterServiceByPetBreed(ctx context.Context, companyId int64, petFilter do.PetFilter, serviceList []models.MoeGroomingService) ([]models.MoeGroomingService, error) {
	if petFilter.PetBreed == nil {
		// no breed info, no need to filter
		return serviceList, nil
	}
	result := make([]models.MoeGroomingService, 0)
	idsNeedToCheck := make([]int64, 0)
	for _, service := range serviceList {
		if *service.BreedFilter {
			idsNeedToCheck = append(idsNeedToCheck, int64(service.ID))
		} else {
			result = append(result, service)
		}
	}
	if len(idsNeedToCheck) == 0 {
		return result, nil
	}
	serviceById := lo.SliceToMap(serviceList, func(service models.MoeGroomingService) (int64, models.MoeGroomingService) {
		return int64(service.ID), service
	})

	petTypeBinding := make([]models.MoeGroomingServiceBreedBinding, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.BreedBindingWhereOpt{
		CompanyID:   proto.Int64(companyId),
		ServiceIDIn: idsNeedToCheck,
		PetTypeId:   proto.Int32(int32(*petFilter.PetTypeId)),
		Status:      proto.Int32(constant.BreedBindingStatusNormal),
	})).Find(&petTypeBinding).Error; err != nil {
		return nil, err
	}

	for _, petTypeBiding := range petTypeBinding {
		if *petTypeBiding.IsAll {
			result = append(result, serviceById[int64(petTypeBiding.ServiceID)])
		} else {
			if lo.Contains(petTypeBiding.GetBreedingNameList(), *petFilter.PetBreed) {
				result = append(result, serviceById[int64(petTypeBiding.ServiceID)])
			}
		}
	}

	return result, nil
}

func (s *serviceRepository) filterServiceByPetSize(ctx context.Context, companyId int64, petFilter do.PetFilter, serviceList []models.MoeGroomingService) ([]models.MoeGroomingService, error) {
	if petFilter.PetSizeId == nil && petFilter.PetWeight == nil {
		// no size info, no need to filter
		return serviceList, nil
	}
	// 旧版本使用 weight filter，新版本使用 size filter，需要兼容旧版本，当 size filter 为空时，使用 weight filter
	result := make([]models.MoeGroomingService, 0)
	for _, service := range serviceList {
		if *service.PetSizeFilter {
			if petFilter.PetSizeId == nil || lo.Contains(service.GetCustomizedPetSizeList(), *petFilter.PetSizeId) {
				result = append(result, service)
			}
			continue
		}
		if *service.WeightFilter {
			if petFilter.PetWeight == nil || (*service.WeightUpLimit >= *petFilter.PetWeight && *service.WeightDownLimit <= *petFilter.PetWeight) {
				result = append(result, service)
			}
			continue
		}
		result = append(result, service)
	}
	return result, nil
}

func (s *serviceRepository) filterServiceByPetCoat(ctx context.Context, companyId int64, petFilter do.PetFilter, serviceList []models.MoeGroomingService) ([]models.MoeGroomingService, error) {
	if petFilter.PetCoatTypeId == nil {
		//no coat info, no need to filter
		return serviceList, nil
	}

	result := make([]models.MoeGroomingService, 0)
	idsNeedToCheck := make([]int64, 0)

	for _, service := range serviceList {
		if *service.CoatFilter {
			idsNeedToCheck = append(idsNeedToCheck, int64(service.ID))
		} else {
			result = append(result, service)
		}
	}
	if len(idsNeedToCheck) == 0 {
		return result, nil
	}
	serviceById := lo.SliceToMap(serviceList, func(service models.MoeGroomingService) (int64, models.MoeGroomingService) {
		return int64(service.ID), service
	})

	coatBindingList := make([]models.MoeGroomingServiceCoatBinding, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.CoatBindingWhereOpt{
		CompanyID:   proto.Int64(companyId),
		ServiceIdIn: idsNeedToCheck,
	})).Find(&coatBindingList).Error; err != nil {
		return nil, err
	}
	for _, coatBinding := range coatBindingList {
		if lo.Contains(coatBinding.GetCoatIDList(), *petFilter.PetCoatTypeId) {
			result = append(result, serviceById[int64(coatBinding.ServiceID)])
		}
	}
	return result, nil
}

func (s *serviceRepository) filterServiceByPetCodes(ctx context.Context, petFilter do.PetFilter, serviceList []models.MoeGroomingService) ([]models.MoeGroomingService, error) {
	if len(serviceList) == 0 {
		return serviceList, nil
	}

	result := make([]models.MoeGroomingService, 0)
	petCodeFilters := make([]*models.MoeServicePetCodeFilter, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.ServicePetCodeFilterWhereOpt{
		ServiceIdIn: lo.Map(serviceList, func(service models.MoeGroomingService, _ int) int64 {
			return int64(service.ID)
		}),
	})).Find(&petCodeFilters).Error; err != nil {
		return nil, err
	}
	petCodeFilterMap := lo.SliceToMap(petCodeFilters, func(petCodeFilter *models.MoeServicePetCodeFilter) (int64, *models.MoeServicePetCodeFilter) {
		return petCodeFilter.ServiceID, petCodeFilter
	})

	for _, service := range serviceList {
		petCodeFiler, ok := petCodeFilterMap[int64(service.ID)]
		if !ok {
			// if pet code filter not exist, then service is available for any pet code
			result = append(result, service)
			continue
		}

		if s.isPetCodeAvailable(petFilter.PetCodeIds, petCodeFiler) {
			result = append(result, service)
		}
	}
	return result, nil
}

func (s *serviceRepository) isPetCodeAvailable(petCodes []int64, petCodeFilter *models.MoeServicePetCodeFilter) bool {
	if !petCodeFilter.GetIsWhitelist() {
		return len(lo.Intersect(petCodes, petCodeFilter.PetCodeList)) == 0
	}

	if petCodeFilter.GetIsAllPetCode() {
		return true
	}

	for _, petCode := range petCodeFilter.PetCodeList {
		if !lo.Contains(petCodes, petCode) {
			return false
		}
	}
	return true
}

func (s *serviceRepository) filterServiceByLodging(ctx context.Context, companyId int64, selectedLodgingFilter do.LodgingFilter, serviceList []models.MoeGroomingService) ([]models.MoeGroomingService, error) {
	result := make([]models.MoeGroomingService, 0)
	for _, service := range serviceList {
		if service.AvailableForAllLodging() || len(lo.Intersect(service.GetCustomizedLodgingList(), selectedLodgingFilter.SelectedLodgingTypeIdList)) > 0 {
			result = append(result, service)
		}
	}
	return result, nil
}

/**
 * 支持 3 种 Filter 场景：
 * 1. SelectedServiceItemType 为空，SelectedServiceIdList 不为空，表示按照 Service ID 作 Filter，不限制 Service Item Type
 * 2. SelectedServiceIdList 为空，SelectedServiceItemType 不为空，表示按照类型过滤，不限制 Service ID
 * 3. 两者都不为空，此时所有的 SelectedServiceIdList 对应的 ServiceItemType 一定都相同
 */
func (s *serviceRepository) filterAddonBySelectedService(ctx context.Context, companyId int64, selectedServiceFilter do.SelectedServiceFilter, addonList []models.MoeGroomingService) ([]models.MoeGroomingService, error) {
	if selectedServiceFilter.SelectedServiceItemType == nil && len(selectedServiceFilter.SelectedServiceIdList) == 0 {
		return addonList, nil
	}

	result := make([]models.MoeGroomingService, 0)
	idsNeedToCheck := make([]int64, 0)
	for _, service := range addonList {
		if service.AvailableForAllSelectedService() {
			result = append(result, service)
		} else {
			idsNeedToCheck = append(idsNeedToCheck, int64(service.ID))
		}
	}
	if len(idsNeedToCheck) == 0 {
		return result, nil
	}
	addonById := lo.SliceToMap(addonList, func(service models.MoeGroomingService) (int64, models.MoeGroomingService) {
		return int64(service.ID), service
	})

	addonApplicableServiceList := make([]models.MoeGroomingAddonApplicableService, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.MoeGroomingAddonApplicableServiceWhereOpt{
		CompanyID:       proto.Int64(companyId),
		AddonIDIn:       idsNeedToCheck,
		ServiceItemType: selectedServiceFilter.SelectedServiceItemType,
	})).Find(&addonApplicableServiceList).Error; err != nil {
		return nil, err
	}

	selectedServiceItemType := make([]offeringV1.ServiceItemType, 0)
	if selectedServiceFilter.SelectedServiceItemType != nil {
		selectedServiceItemType = append(selectedServiceItemType, *selectedServiceFilter.SelectedServiceItemType)
	}
	if len(selectedServiceFilter.SelectedServiceIdList) != 0 {
		selectServices := make([]*models.MoeGroomingService, 0)
		if err := s.db.WithContext(ctx).Where(gormx.Query(models.ServiceWhereOpt{ServiceIdListIn: selectedServiceFilter.SelectedServiceIdList})).Find(&selectServices).Error; err != nil {
			return nil, errors.WithStack(err)
		}
		selectedServiceItemType = append(selectedServiceItemType,
			lo.Map(selectServices, func(service *models.MoeGroomingService, _ int) offeringV1.ServiceItemType {
				return offeringV1.ServiceItemType(*service.ServiceItemType)
			})...)
	}
	selectedServiceItemType = lo.Uniq(selectedServiceItemType)

	for _, addonApplicableService := range addonApplicableServiceList {
		// available for all services, only need to check if the selected service item types include the addon service item type
		if *addonApplicableService.AvailableForAllServices {
			if lo.Contains(selectedServiceItemType, offeringV1.ServiceItemType(addonApplicableService.ServiceItemType)) {
				result = append(result, addonById[addonApplicableService.AddonID])
			}
			continue
		}

		// no specific selected service ids，only need to check if the selected service item type equals to the addon service item type
		if len(selectedServiceFilter.SelectedServiceIdList) == 0 {
			if *selectedServiceFilter.SelectedServiceItemType == addonApplicableService.GetServiceItemType() {
				result = append(result, addonById[addonApplicableService.AddonID])
			}
			continue
		}

		// specific selected service ids, need to check the id intersection
		if len(lo.Intersect(addonApplicableService.GetAllowedServiceList(), selectedServiceFilter.SelectedServiceIdList)) > 0 {
			result = append(result, addonById[addonApplicableService.AddonID])
			continue
		}
	}

	return result, nil
}

// 排序规则：先按照 category sort 排序，再按照 service sort 排序，最后按照 ID 降序
// TODO: 内存分页，后续重新设计
func (s *serviceRepository) sortAndPaginateServiceList(ctx context.Context, companyId int64, serviceList []models.MoeGroomingService, paginationRequest *utilsV2.PaginationRequest, orderBy offeringV1.ServiceOrderByType) ([]models.MoeGroomingService, error) {
	var err error
	switch orderBy {
	case offeringV1.ServiceOrderByType_MAX_DURATION_ASC:
		serviceList, err = s.sortByMaxDurationAsc(serviceList)
	default:
		serviceList, err = s.sortByDefaultRule(ctx, companyId, serviceList)
	}
	if err != nil {
		return nil, err
	}

	if paginationRequest != nil {
		from := (paginationRequest.GetPageNum() - 1) * paginationRequest.GetPageSize()
		if from > int32(len(serviceList)) {
			return make([]models.MoeGroomingService, 0), nil
		}
		end := from + paginationRequest.GetPageSize()
		if end > int32(len(serviceList)) {
			end = int32(len(serviceList))
		}
		serviceList = serviceList[from:end]
	}

	return serviceList, nil
}

// sortByDefaultRule, sort by category sort desc, service sort desc, service id desc
func (s *serviceRepository) sortByDefaultRule(ctx context.Context, companyId int64, serviceList []models.MoeGroomingService) ([]models.MoeGroomingService, error) {
	categoryIdList := lo.Map(serviceList, func(service models.MoeGroomingService, _ int) int64 {
		return int64(service.CategoryID)
	})
	categoryList := make([]models.MoeGroomingServiceCategory, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.ServiceCategoryWhereOpt{
		CompanyID: proto.Int64(companyId),
		Status:    proto.Int32(constant.NormalStatus),
		IDList:    categoryIdList,
	})).Find(&categoryList).Error; err != nil {
		return nil, errors.WithStack(err)
	}
	categorySortByID := make(map[int64]int32)
	for _, category := range categoryList {
		categorySortByID[int64(category.ID)] = category.Sort
	}

	sort.Slice(serviceList, func(i, j int) bool {
		if serviceList[i].CategoryID != serviceList[j].CategoryID {
			if serviceList[i].CategoryID == 0 {
				return true
			}
			if serviceList[j].CategoryID == 0 {
				return false
			}
			return categorySortByID[int64(serviceList[i].CategoryID)] > categorySortByID[int64(serviceList[j].CategoryID)]
		}

		if serviceList[i].Sort != serviceList[j].Sort {
			return serviceList[i].Sort > serviceList[j].Sort
		}

		return serviceList[i].ID > serviceList[j].ID
	})

	return serviceList, nil
}

// sortByMaxDurationAsc, sort by max duration asc, service id desc
func (s *serviceRepository) sortByMaxDurationAsc(serviceList []models.MoeGroomingService) ([]models.MoeGroomingService, error) {
	sort.Slice(serviceList, func(i, j int) bool {
		if serviceList[i].MaxDuration != serviceList[j].MaxDuration {
			return serviceList[i].MaxDuration < serviceList[j].MaxDuration
		}
		return serviceList[i].ID > serviceList[j].ID
	})
	return serviceList, nil
}

func (s *serviceRepository) getLocationOverrideInfo(ctx context.Context, companyId int64, businessIds, serviceIds []int64) (map[int64][]do.BusinessOverrideRule, error) {
	locationOverrideList := make([]models.MoeGroomingServiceLocation, 0)
	if err := s.db.WithContext(ctx).Where(gormx.Query(models.ServiceLocationWhereOpt{
		CompanyID:    proto.Int64(companyId),
		ServiceIDIn:  serviceIds,
		BusinessIDIn: businessIds,
		IsDeleted:    proto.Bool(false),
	})).Find(&locationOverrideList).Error; err != nil {
		return nil, errors.WithStack(err)
	}

	locationOverrideRuleByServiceId := make(map[int64][]do.BusinessOverrideRule)
	for _, locationOverride := range locationOverrideList {
		locationOverrideRuleByServiceId[int64(locationOverride.ServiceID)] = append(locationOverrideRuleByServiceId[int64(locationOverride.ServiceID)], do.BusinessOverrideRule{
			BusinessId: int64(locationOverride.BusinessID),
			Price:      locationOverride.Price,
			TaxID:      locationOverride.GetTaxID(),
			Duration:   locationOverride.Duration,
		})
	}

	return locationOverrideRuleByServiceId, nil
}

func (s *serviceRepository) getPetOverrideInfo(ctx context.Context, companyId int64, serviceIdWithPetIds map[int64][]int64) (map[int64][]do.PetOverrideRule, error) {
	petIdList := lo.Uniq(
		lo.FlatMap(
			lo.Values(serviceIdWithPetIds), func(petIdList []int64, _ int) []int64 {
				return lo.Filter(petIdList, func(petId int64, _ int) bool {
					return petId > 0
				})
			}))

	petOverrideList := make([]models.MoeGroomingCustomerService, 0)

	if len(petIdList) > 0 {
		whereOpt := models.MoeGroomingCustomerServicesWhereOpt{
			CompanyId: proto.Int64(companyId),
			Status:    proto.Int32(constant.NormalStatus),
			PetIdIn:   petIdList,
		}
		if err := s.db.WithContext(ctx).Where(gormx.Query(whereOpt)).Find(&petOverrideList).Error; err != nil {
			return nil, errors.WithStack(err)
		}

	}
	petOverrideRecordByServiceId := lo.GroupBy(petOverrideList, func(petOverride models.MoeGroomingCustomerService) int64 {
		return int64(petOverride.ServiceID)
	})

	petOverrideRuleByServiceId := make(map[int64][]do.PetOverrideRule)
	for serviceId, petOverrideList := range petOverrideRecordByServiceId {
		petOverrideRuleByPetId := make(map[int64]do.PetOverrideRule)
		for _, petOverride := range petOverrideList {
			petOverrideRule := do.PetOverrideRule{PetID: int64(petOverride.PetID)}
			if rule, ok := petOverrideRuleByPetId[int64(petOverride.PetID)]; ok {
				petOverrideRule = rule
			}
			if petOverrideRule.Price == nil {
				petOverrideRule.Price = petOverride.GetSavedPrice()
			}
			if petOverrideRule.Duration == nil {
				petOverrideRule.Duration = petOverride.GetSavedTime()
			}
			petOverrideRuleByPetId[int64(petOverride.PetID)] = petOverrideRule
		}
		petOverrideRuleByServiceId[serviceId] = lo.Values(petOverrideRuleByPetId)
	}

	return petOverrideRuleByServiceId, nil
}

func extractServiceIdList(serviceModelList []models.MoeGroomingService) []int64 {
	serviceIdList := make([]int64, 0, len(serviceModelList))
	for _, serviceModel := range serviceModelList {
		serviceIdList = append(serviceIdList, int64(serviceModel.ID))
	}
	return serviceIdList
}

func NewServiceRepository(db *gorm.DB) ServiceRepository {
	return &serviceRepository{db: db}
}

func (s *serviceRepository) UpdateAllServiceByCompanyId(ctx context.Context, companyID int64, updateOpt *do.ServiceUpdateOpt) error {
	serviceUpdateOpt := updateOpt.ToModel()
	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// update service
		if err := tx.Model(&models.MoeGroomingService{}).Where(gormx.Query(models.ServiceWhereOpt{
			CompanyID: proto.Int64(companyID),
		})).Updates(gormx.Update(&serviceUpdateOpt)).Error; err != nil {
			zlog.Error(ctx, "failed to update all service by company_id", zap.Error(err))
			return err
		}
		return nil
	}); err != nil {
		zlog.Error(ctx, "failed to update all service by company_id", zap.Error(err))
		return err
	}
	return nil
}
