package repository

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"gorm.io/gormx"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_evaluation_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository EvaluationRepository
type EvaluationRepository interface {
	AddNewEvaluation(ctx context.Context, tx *gorm.DB, companyId int64, do *do.EvaluationDO) (int64, error)
	UpdateEvaluation(ctx context.Context, tx *gorm.DB, companyId int64, evaluationId int64, updateOpt *do.EvaluationDO) error
	GetEvaluationList(ctx context.Context, companyId int64) ([]*do.EvaluationDO, error)
	GetEvaluationListByFilter(ctx context.Context, filter *do.EvaluationModelFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []*do.EvaluationDO, error)
	GetEvaluationDetail(ctx context.Context, evaluationId int64) (*do.EvaluationDO, error)
	GetEvaluationListByIds(ctx context.Context, evaluationIds []int64) ([]*do.EvaluationDO, error)
	DeleteEvaluation(ctx context.Context, tx *gorm.DB, companyId int64, evaluationId int64) error
}

type evaluationRepository struct {
	db *gorm.DB
}

func (e evaluationRepository) GetEvaluationListByIds(ctx context.Context, evaluationIds []int64) ([]*do.EvaluationDO, error) {
	if evaluationIds == nil {
		return []*do.EvaluationDO{}, nil
	}
	var evaluations []*models.Evaluation
	// query include deleted evaluations
	var db = e.db.Unscoped()
	if err := db.WithContext(ctx).Where(gormx.Query(models.EvaluationWhereOpt{
		IDs: &evaluationIds,
	})).Find(&evaluations).Error; err != nil {
		return nil, errors.WithStack(err)
	}
	return lo.Map(evaluations, func(e *models.Evaluation, _ int) *do.EvaluationDO {
		return converter.ConvertEvaluationDBModelToDO(e)
	}), nil
}

func (e evaluationRepository) AddNewEvaluation(ctx context.Context, tx *gorm.DB, companyId int64, evaluationDo *do.EvaluationDO) (int64, error) {
	evaluationDBModel := converter.ConvertEvaluationDOToDBModel(evaluationDo)
	evaluationDBModel.CompanyID = companyId

	if evaluationDBModel.AllowedStaffList == nil {
		evaluationDBModel.AllowedStaffList = []int64{}
	}

	if err := tx.WithContext(ctx).Create(&evaluationDBModel).Error; err != nil {
		return 0, errors.WithStack(err)
	}
	return evaluationDBModel.ID, nil
}

func (e evaluationRepository) UpdateEvaluation(ctx context.Context, tx *gorm.DB, companyId int64, evaluationId int64, evaluationDo *do.EvaluationDO) error {
	updateOpt := converter.ConvertEvaluationDOToUpdateOpt(evaluationDo)
	now := time.Now()
	updateOpt.UpdatedAt = &now

	whereOpt := models.EvaluationWhereOpt{ID: &evaluationId}
	if companyId != 0 {
		whereOpt.CompanyId = proto.Int64(companyId)
	}
	if err := tx.WithContext(ctx).Model(&models.Evaluation{}).Where(gormx.Query(whereOpt)).Updates(gormx.Update(updateOpt)).Error; err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (e evaluationRepository) GetEvaluationList(ctx context.Context, companyId int64) ([]*do.EvaluationDO, error) {
	var evaluations []*models.Evaluation
	if err := e.db.WithContext(ctx).Where(gormx.Query(models.EvaluationWhereOpt{
		CompanyId: &companyId,
	})).Find(&evaluations).Error; err != nil {
		return nil, errors.WithStack(err)
	}
	return lo.Map(evaluations, func(e *models.Evaluation, _ int) *do.EvaluationDO {
		return converter.ConvertEvaluationDBModelToDO(e)
	}), nil
}

func (e evaluationRepository) GetEvaluationListByFilter(ctx context.Context, filter *do.EvaluationModelFilter, paginationRequest *utilsV2.PaginationRequest) (int64, []*do.EvaluationDO, error) {
	var evaluations []*models.Evaluation

	whereOpt := models.EvaluationWhereOpt{}
	if filter.IsResettable != nil {
		whereOpt.IsResettable = filter.IsResettable
	}
	if len(filter.CompanyIds) > 0 {
		whereOpt.CompanyIds = filter.CompanyIds
	}
	if len(filter.Ids) > 0 {
		whereOpt.IDs = &filter.Ids
	}

	if err := e.db.WithContext(ctx).Scopes(Paginate(paginationRequest)).Where(gormx.Query(whereOpt)).Find(&evaluations).Error; err != nil {
		return 0, nil, errors.WithStack(err)
	}

	var total int64
	if err := e.db.WithContext(ctx).Where(gormx.Query(whereOpt)).Model(&models.Evaluation{}).Count(&total).Error; err != nil {
		return 0, nil, errors.WithStack(err)
	}

	return total, lo.Map(evaluations, func(e *models.Evaluation, _ int) *do.EvaluationDO {
		return converter.ConvertEvaluationDBModelToDO(e)
	}), nil
}

func (e evaluationRepository) GetEvaluationDetail(ctx context.Context, evaluationId int64) (*do.EvaluationDO, error) {
	var evaluation models.Evaluation
	if err := e.db.WithContext(ctx).Where(gormx.Query(models.EvaluationWhereOpt{
		ID: &evaluationId,
	})).First(&evaluation).Error; err != nil {
		return nil, errors.WithStack(err)
	}
	return converter.ConvertEvaluationDBModelToDO(&evaluation), nil
}

func (e evaluationRepository) DeleteEvaluation(ctx context.Context, tx *gorm.DB, companyId int64, evaluationId int64) error {
	whereOpt := models.EvaluationWhereOpt{ID: &evaluationId}
	if companyId != 0 {
		whereOpt.CompanyId = proto.Int64(companyId)
	}

	now := time.Now()
	updateOpt := models.EvaluationUpdateOpt{
		DeletedAt: &now,
	}

	if err := tx.WithContext(ctx).Model(&models.Evaluation{}).Where(gormx.Query(whereOpt)).Updates(gormx.Update(updateOpt)).Error; err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func NewEvaluationRepository(db *gorm.DB) EvaluationRepository {
	return &evaluationRepository{db: db}
}
