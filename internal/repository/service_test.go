package repository

import (
	"context"
	"database/sql/driver"
	"reflect"
	"regexp"
	"testing"

	sqlmock "github.com/DATA-DOG/go-sqlmock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"github.com/MoeGolibrary/go-lib/gorm"

	_ "github.com/MoeGolibrary/go-lib/gorm/serializer"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func Test_serviceRepository_GetService(t *testing.T) {
	type mockFunc func(sqlMock sqlmock.Sqlmock)
	type args struct {
		ctx       context.Context
		serviceId int64
	}
	tests := []struct {
		name     string
		mockFunc mockFunc
		args     args
		want     *models.MoeGroomingService
		wantErr  error
	}{
		{
			name: "Test_serviceRepository_GetService_Normal",
			mockFunc: func(sqlMock sqlmock.Sqlmock) {
				sqlMock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `moe_grooming_service` WHERE `moe_grooming_service`.`id` = ? ORDER BY `moe_grooming_service`.`id` LIMIT ?")).
					WithArgs(1, 1).
					WillReturnRows(sqlMock.NewRows([]string{"id", "name"}).AddRow(1, "test"))
			},
			args: args{
				ctx:       context.TODO(),
				serviceId: 1,
			},
			want: &models.MoeGroomingService{
				ID:   1,
				Name: "test",
			},
			wantErr: nil,
		},
		{
			name: "Test_serviceRepository_GetService_Not_Found",
			mockFunc: func(sqlMock sqlmock.Sqlmock) {
				sqlMock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `moe_grooming_service` WHERE `moe_grooming_service`.`id` = ? ORDER BY `moe_grooming_service`.`id` LIMIT ?")).
					WithArgs(1, 1).
					WillReturnRows(sqlMock.NewRows([]string{"id", "name"}))
			},
			args: args{
				ctx:       context.TODO(),
				serviceId: 1,
			},
			want:    nil,
			wantErr: ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, sqlMock := gorm.MockMySQL(t)
			s := &serviceRepository{
				db: db,
			}
			tt.mockFunc(sqlMock)
			got, err := s.GetService(tt.args.ctx, tt.args.serviceId)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetService() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetService() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestServiceRepository_filterServiceByPetCodes(t *testing.T) {
	serviceList := []models.MoeGroomingService{
		{
			ID:   1,
			Name: "no pet code setting",
		},
		{
			ID:   2,
			Name: "white list with all pet codes",
		},
		{
			ID:   3,
			Name: "white list with some pet codes",
		},
		{
			ID:   4,
			Name: "white list with some pet codes",
		},
		{
			ID:   5,
			Name: "black list with some pet codes",
		},
		{
			ID:   6,
			Name: "black list with some pet codes",
		},
	}

	mockDB, sqlMock := gorm.MockMySQL(t)

	rows := sqlmock.NewRows([]string{"service_id", "is_whitelist", "is_all_pet_code", "pet_code_list"}).AddRows(
		[]driver.Value{1, nil, nil, nil},
		[]driver.Value{2, true, true, nil},
		[]driver.Value{3, true, false, "[11,12,13]"},
		[]driver.Value{4, true, false, "[24,25]"},
		[]driver.Value{5, false, false, "[11, 24]"},
		[]driver.Value{6, false, false, "[32]"},
	)
	sqlMock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `moe_service_pet_code_filter` WHERE `service_id` IN (?,?,?,?,?,?)")).WithArgs(1, 2, 3, 4, 5, 6).WillReturnRows(rows)

	s := &serviceRepository{
		db: mockDB,
	}

	expect := []models.MoeGroomingService{
		{
			ID:   1,
			Name: "no pet code setting",
		},
		{
			ID:   2,
			Name: "white list with all pet codes",
		},
		{
			ID:   3,
			Name: "white list with some pet codes",
		},
		{
			ID:   6,
			Name: "black list with some pet codes",
		},
	}

	filteredServiceList, err := s.filterServiceByPetCodes(context.Background(), do.PetFilter{
		PetCodeIds: []int64{11, 12, 13, 31},
	}, serviceList)

	assert.NoError(t, err)
	assert.Equal(t, expect, filteredServiceList)

}

func TestServiceRepository_filterServiceByPetCodes_emptyPetCode(t *testing.T) {
	serviceList := []models.MoeGroomingService{
		{
			ID:   1,
			Name: "no pet code setting",
		},
		{
			ID:   2,
			Name: "white list with all pet codes",
		},
		{
			ID:   3,
			Name: "white list with some pet codes",
		},
		{
			ID:   4,
			Name: "white list with some pet codes",
		},
		{
			ID:   5,
			Name: "black list with some pet codes",
		},
		{
			ID:   6,
			Name: "black list with some pet codes",
		},
	}

	mockDB, sqlMock := gorm.MockMySQL(t)

	rows := sqlmock.NewRows([]string{"service_id", "is_whitelist", "is_all_pet_code", "pet_code_list"}).AddRows(
		[]driver.Value{1, nil, nil, nil},
		[]driver.Value{2, true, true, nil},
		[]driver.Value{3, true, false, "[11,12,13]"},
		[]driver.Value{4, true, false, "[24,25]"},
		[]driver.Value{5, false, false, "[11, 24]"},
		[]driver.Value{6, false, false, "[12]"},
	)
	sqlMock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `moe_service_pet_code_filter` WHERE `service_id` IN (?,?,?,?,?,?)")).WithArgs(1, 2, 3, 4, 5, 6).WillReturnRows(rows)

	s := &serviceRepository{
		db: mockDB,
	}

	expect := []models.MoeGroomingService{
		{
			ID:   1,
			Name: "no pet code setting",
		},
		{
			ID:   2,
			Name: "white list with all pet codes",
		},
		{
			ID:   5,
			Name: "black list with some pet codes",
		},
		{
			ID:   6,
			Name: "black list with some pet codes",
		},
	}

	filteredServiceList, err := s.filterServiceByPetCodes(context.Background(), do.PetFilter{}, serviceList)
	assert.NoError(t, err)
	assert.Equal(t, expect, filteredServiceList)

}
