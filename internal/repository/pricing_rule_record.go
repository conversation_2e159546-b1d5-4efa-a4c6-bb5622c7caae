package repository

import (
	"context"
	"strings"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"gorm.io/gormx"

	offeringModelsV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_pricing_rule_record_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository PricingRuleRecordRepository
type PricingRuleRecordRepository interface {
	Upsert(ctx context.Context, pricingRuleRecordDo *do.PricingRuleRecordDO) (int64, error)
	GetById(ctx context.Context, companyId int64, pricingRuleId int64) (*do.PricingRuleRecordDO, error)
	List(ctx context.Context, whereOpt *models.PricingRuleRecordWhereOpt, pagination *utilsV2.PaginationRequest) ([]*do.PricingRuleRecordDO, error)
	Count(ctx context.Context, whereOpt *models.PricingRuleRecordWhereOpt) (int32, error)
	Delete(ctx context.Context, companyId int64, pricingRuleRecordId int64, deleteBy int64) error
}

type pricingRuleRecordRepository struct {
	db *gorm.DB
}

func NewPricingRuleRecordRepository(db *gorm.DB) PricingRuleRecordRepository {
	return &pricingRuleRecordRepository{db: db}
}

func (p *pricingRuleRecordRepository) Upsert(ctx context.Context, pricingRuleRecordDo *do.PricingRuleRecordDO) (int64, error) {
	if pricingRuleRecordDo.ID == nil {
		po := doToPo(pricingRuleRecordDo)
		err := p.db.WithContext(ctx).Model(&models.PricingRuleRecord{}).Create(po)
		if err != nil {
			return po.ID, err.Error
		}
		// 设置 ID
		if pricingRuleRecordDo.ID == nil {
			id := po.ID
			pricingRuleRecordDo.ID = &id
		}
		return po.ID, nil
	} else {
		updateOpt := recordDoToUpdateOpt(pricingRuleRecordDo)

		err := p.db.WithContext(ctx).Model(&models.PricingRuleRecord{}).Where(gormx.Query(models.PricingRuleRecordWhereOpt{
			ID:        pricingRuleRecordDo.ID,
			CompanyID: pricingRuleRecordDo.CompanyID,
		})).Updates(gormx.Update(updateOpt))
		if err != nil {
			return *pricingRuleRecordDo.ID, err.Error
		}
		return *pricingRuleRecordDo.ID, nil
	}
}

func recordDoToUpdateOpt(do *do.PricingRuleRecordDO) *models.PricingRuleRecordUpdateOpt {
	return &models.PricingRuleRecordUpdateOpt{
		RuleName:                 &do.RuleName,
		AllBoardingApplicable:    &do.AllBoardingApplicable,
		SelectedBoardingServices: &do.SelectedBoardingServices,
		AllDaycareApplicable:     &do.AllDaycareApplicable,
		SelectedDaycareServices:  &do.SelectedDaycareServices,
		RuleApplyType:            do.RuleApplyType,
		NeedInSameLodging:        &do.NeedInSameLodging,
		RuleConfiguration:        do.RuleConfiguration,
		IsActive:                 &do.IsActive,
		UpdatedBy:                &do.UpdatedBy,
		UpdatedAt:                time.Now(),
		IsChargePerLodging:       &do.IsChargePerLodging,
		Source:                   do.Source,
		AllGroomingApplicable:    &do.AllGroomingApplicable,
		SelectedGroomingServices: &do.SelectedGroomingServices,
		AllAddonApplicable:       &do.AllAddonApplicable,
		SelectedAddonServices:    &do.SelectedAddonServices,
	}
}

func doToPo(do *do.PricingRuleRecordDO) *models.PricingRuleRecord {
	return &models.PricingRuleRecord{
		CompanyID:                do.CompanyID,
		RuleType:                 do.RuleType,
		RuleName:                 do.RuleName,
		IsActive:                 do.IsActive,
		AllBoardingApplicable:    do.AllBoardingApplicable,
		SelectedBoardingServices: do.SelectedBoardingServices,
		AllDaycareApplicable:     do.AllDaycareApplicable,
		SelectedDaycareServices:  do.SelectedDaycareServices,
		RuleApplyType:            do.RuleApplyType,
		NeedInSameLodging:        do.NeedInSameLodging,
		RuleConfiguration:        do.RuleConfiguration,
		UpdatedBy:                do.UpdatedBy,
		CreatedAt:                do.CreatedAt,
		//UpdatedAt:                lo.Ternary(do.UpdatedAt == nil, lo.ToPtr(time.Now()), do.UpdatedAt),
		UpdatedAt:                do.UpdatedAt,
		DeletedAt:                do.DeletedAt,
		IsChargePerLodging:       do.IsChargePerLodging,
		AllGroomingApplicable:    do.AllGroomingApplicable,
		SelectedGroomingServices: do.SelectedGroomingServices,
		AllAddonApplicable:       do.AllAddonApplicable,
		SelectedAddonServices:    do.SelectedAddonServices,
	}
}

func (p *pricingRuleRecordRepository) GetById(ctx context.Context, companyId int64, pricingRuleId int64) (*do.PricingRuleRecordDO, error) {
	var po models.PricingRuleRecord
	if err := p.db.WithContext(ctx).Model(&models.PricingRuleRecord{}).Unscoped().Where(gormx.Query(models.PricingRuleRecordWhereOpt{
		ID:        proto.Int64(pricingRuleId),
		CompanyID: companyId,
	})).First(&po).Error; err != nil {
		return nil, err
	}
	return poToDo(&po), nil
}

func poToDo(po *models.PricingRuleRecord) *do.PricingRuleRecordDO {
	return &do.PricingRuleRecordDO{
		ID:                       &po.ID,
		CompanyID:                po.CompanyID,
		RuleType:                 po.RuleType,
		RuleName:                 po.RuleName,
		IsActive:                 po.IsActive,
		AllBoardingApplicable:    po.AllBoardingApplicable,
		SelectedBoardingServices: po.SelectedBoardingServices,
		AllDaycareApplicable:     po.AllDaycareApplicable,
		SelectedDaycareServices:  po.SelectedDaycareServices,
		RuleApplyType:            po.RuleApplyType,
		NeedInSameLodging:        po.NeedInSameLodging,
		RuleConfiguration:        po.RuleConfiguration,
		UpdatedBy:                po.UpdatedBy,
		CreatedAt:                po.CreatedAt,
		UpdatedAt:                po.UpdatedAt,
		DeletedAt:                po.DeletedAt,
		IsChargePerLodging:       po.IsChargePerLodging,
		Source:                   po.Source.Enum(),
		AllGroomingApplicable:    po.AllGroomingApplicable,
		SelectedGroomingServices: po.SelectedGroomingServices,
		AllAddonApplicable:       po.AllAddonApplicable,
		SelectedAddonServices:    po.SelectedAddonServices,
	}
}

func (p *pricingRuleRecordRepository) List(ctx context.Context, whereOpt *models.PricingRuleRecordWhereOpt, pagination *utilsV2.PaginationRequest) ([]*do.PricingRuleRecordDO, error) {
	var pos []*models.PricingRuleRecord
	db := p.db.WithContext(ctx).Model(&models.PricingRuleRecord{})

	// 基本条件
	db = db.Where(gormx.Query(whereOpt))

	// 处理 CareTypes
	if len(whereOpt.CareTypes) > 0 {
		subConditions := make([]string, 0)
		for _, careType := range whereOpt.CareTypes {
			switch careType {
			case offeringModelsV1.ServiceItemType_BOARDING:
				subConditions = append(subConditions, "(all_boarding_applicable = true OR (all_boarding_applicable = false AND jsonb_array_length(selected_boarding_services) > 0))")
			case offeringModelsV1.ServiceItemType_DAYCARE:
				subConditions = append(subConditions, "(all_daycare_applicable = true OR (all_daycare_applicable = false AND jsonb_array_length(selected_daycare_services) > 0))")
			}
		}
		if len(subConditions) > 0 {
			db = db.Where(strings.Join(subConditions, " OR "))
		}
	}

	if err := db.Order("updated_at desc").Scopes(Paginate(pagination)).Find(&pos).Error; err != nil {
		return nil, err
	}
	dos := make([]*do.PricingRuleRecordDO, 0, len(pos))
	for _, po := range pos {
		dos = append(dos, poToDo(po))
	}
	return dos, nil
}

func (p *pricingRuleRecordRepository) Count(ctx context.Context, whereOpt *models.PricingRuleRecordWhereOpt) (int32, error) {
	var count int64
	err := p.db.WithContext(ctx).Model(&models.PricingRuleRecord{}).Where(gormx.Query(whereOpt)).Count(&count).Error
	if err != nil {
		return int32(count), errors.WithStack(err)
	}
	return int32(count), nil
}

func (p *pricingRuleRecordRepository) Delete(ctx context.Context, companyId int64, pricingRuleRecordId int64, deleteBy int64) error {
	deleteOpt := &models.PricingRuleRecordDeleteOpt{
		UpdatedBy: deleteBy,
		DeletedAt: gorm.DeletedAt{Valid: true, Time: time.Now()},
	}
	return p.db.WithContext(ctx).Model(&models.PricingRuleRecord{}).Where(gormx.Query(models.PricingRuleRecordWhereOpt{
		ID:        proto.Int64(pricingRuleRecordId),
		CompanyID: companyId,
	})).Updates(gormx.Update(deleteOpt)).Error
}
