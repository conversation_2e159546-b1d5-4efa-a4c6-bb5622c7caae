package repository

import (
	"context"
	"errors"
	"time"

	"go.uber.org/zap"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/go-lib/zlog"
	offeringV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/constant"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_category_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository CategoryRepository
type CategoryRepository interface {
	GetCategory(ctx context.Context, id int64) (*models.MoeGroomingServiceCategory, error)
	GetCategoryWithFilter(ctx context.Context, companyId int64, filter do.CategoryQueryFilter) ([]*do.Category, error)
	GetCategoryByID(ctx context.Context, categoryID int64) (*do.Category, error)
	CreateCategories(ctx context.Context, categories []*do.Category) ([]*do.Category, error)
}

type categoryRepository struct {
	db *gorm.DB
}

func (c *categoryRepository) GetCategory(ctx context.Context, id int64) (*models.MoeGroomingServiceCategory, error) {
	var category models.MoeGroomingServiceCategory
	if err := c.db.WithContext(ctx).First(&category, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrRecordNotFound
		}
		return nil, err
	}
	return &category, nil
}

func NewCategoryRepository(db *gorm.DB) CategoryRepository {
	return &categoryRepository{db: db}
}

func (c *categoryRepository) GetCategoryWithFilter(ctx context.Context, companyId int64,
	filter do.CategoryQueryFilter) ([]*do.Category, error) {
	categoryList := make([]*models.MoeGroomingServiceCategory, 0)
	err := c.db.WithContext(ctx).Where(gormx.Query(models.ServiceCategoryWhereOpt{
		CompanyID:        proto.Int64(companyId),
		Status:           proto.Int32(constant.NormalStatus),
		IDList:           filter.CategoryIdList,
		ServiceItemTypes: filter.ServiceItemTypes,
		ServiceTypes:     filter.ServiceTypes,
	})).Order("sort desc").Find(&categoryList).Error
	if err != nil {
		return nil, err
	}
	categoryDOList := make([]*do.Category, 0, len(categoryList))
	for _, category := range categoryList {
		categoryDOList = append(categoryDOList, &do.Category{
			ID:        int64(category.ID),
			Name:      category.Name,
			Type:      offeringV1.ServiceType(category.Type),
			Sort:      category.Sort,
			CompanyID: category.CompanyID,
			ItemType:  offeringV1.ServiceItemType(*category.ServiceItemType),
		})
	}
	return categoryDOList, nil
}

func (c *categoryRepository) GetCategoryByID(ctx context.Context, categoryID int64) (*do.Category, error) {
	category := &models.MoeGroomingServiceCategory{}
	err := c.db.WithContext(ctx).First(category, categoryID).Error
	if err != nil {
		return nil, err
	}
	return &do.Category{
		ID:        int64(category.ID),
		Name:      category.Name,
		Type:      offeringV1.ServiceType(category.Type),
		Sort:      category.Sort,
		CompanyID: category.CompanyID,
		ItemType:  offeringV1.ServiceItemType(*category.ServiceItemType),
	}, nil
}

func (c *categoryRepository) CreateCategories(ctx context.Context, categories []*do.Category) ([]*do.Category, error) {
	entities := make([]*models.MoeGroomingServiceCategory, 0, len(categories))
	for _, c := range categories {
		entities = append(entities, &models.MoeGroomingServiceCategory{
			Name:            c.Name,
			Type:            int32(c.Type),
			Sort:            c.Sort,
			CreateTime:      time.Now().Unix(),
			UpdateTime:      time.Now().Unix(),
			CompanyID:       c.CompanyID,
			ServiceItemType: proto.Int32(int32(c.ItemType)),
		})
	}
	if err := c.db.WithContext(ctx).Model(models.MoeGroomingServiceCategory{}).Create(entities).Error; err != nil {
		zlog.Error(ctx, "CreateCategories err", zap.Error(err))
		return nil, err
	}
	created := make([]*do.Category, 0, len(entities))
	for _, e := range entities {
		created = append(created, &do.Category{
			ID:        int64(e.ID),
			Name:      e.Name,
			Type:      offeringV1.ServiceType(e.Type),
			Sort:      e.Sort,
			CompanyID: e.CompanyID,
			ItemType:  offeringV1.ServiceItemType(*e.ServiceItemType),
		})
	}
	return created, nil
}
