package repository

import (
	"context"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_auto_rollover_rule_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository AutoRolloverRuleRepository
type AutoRolloverRuleRepository interface {
	BatchGetAutoRolloverRules(ctx context.Context, serviceIds []int64) (map[int64]*do.AutoRolloverRule, error)
}

type autoRolloverRuleRepository struct {
	db *gorm.DB
}

func (a autoRolloverRuleRepository) BatchGetAutoRolloverRules(ctx context.Context, serviceIds []int64) (map[int64]*do.AutoRolloverRule, error) {
	if len(serviceIds) == 0 {
		return make(map[int64]*do.AutoRolloverRule), nil
	}
	autoRolloverRules := make([]*models.MoeAutoRollover, 0)
	if err := a.db.WithContext(ctx).Where(gormx.Query(
		models.AutoRolloverWhereOpt{ServiceIDIn: serviceIds},
	)).Find(&autoRolloverRules).Error; err != nil {
		return nil, errors.WithStack(err)
	}

	return lo.SliceToMap(autoRolloverRules, func(record *models.MoeAutoRollover) (int64, *do.AutoRolloverRule) {
		return record.ServiceID, converter.ConvertAutoRolloverRuleDBModelToDO(record)
	}), nil
}

func NewAutoRolloverRuleRepository(db *gorm.DB) AutoRolloverRuleRepository {
	return &autoRolloverRuleRepository{db: db}
}
