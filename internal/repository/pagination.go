package repository

import (
	"gorm.io/gorm"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
)

func Paginate(pagination *utilsV2.PaginationRequest) func(db *gorm.DB) *gorm.DB {
	if pagination == nil {
		return func(db *gorm.DB) *gorm.DB {
			return db
		}
	}
	return func(db *gorm.DB) *gorm.DB {
		page := *pagination.PageNum
		if page <= 0 {
			page = 1
		}

		pageSize := *pagination.PageSize
		switch {
		case pageSize > 1000:
			pageSize = 1000
		case pageSize <= 0:
			pageSize = 10
		}

		offset := (page - 1) * pageSize
		return db.Offset(int(offset)).Limit(int(pageSize))
	}
}
