package repository

import (
	"context"
	"errors"
	"time"

	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_discount_setting_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository DiscountSettingRepository
type DiscountSettingRepository interface {
	GetByCompanyId(ctx context.Context, companyId int64) (*do.DiscountSettingDO, error)
	Upsert(ctx context.Context, discountSettingDO *do.DiscountSettingDO) error
}

type discountSettingRepository struct {
	db *gorm.DB
}

func (d *discountSettingRepository) GetByCompanyId(ctx context.Context, companyId int64) (*do.DiscountSettingDO, error) {
	var po models.DiscountSetting
	if err := d.db.WithContext(ctx).Where(gormx.Query(models.DiscountSettingWhereOpt{
		CompanyID: companyId,
	})).First(&po).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return d.poToDo(&po), nil
}

func (d *discountSettingRepository) Upsert(ctx context.Context, discountSettingDO *do.DiscountSettingDO) error {
	if discountSettingDO.ID == nil {
		po := d.doToPo(discountSettingDO)
		return d.db.WithContext(ctx).Create(po).Error
	}

	updateOpt := d.doToUpdateOpt(discountSettingDO)
	return d.db.WithContext(ctx).Model(&models.DiscountSetting{}).Where(gormx.Query(models.DiscountSettingWhereOpt{
		ID:        discountSettingDO.ID,
		CompanyID: discountSettingDO.CompanyID,
	})).Updates(gormx.Update(updateOpt)).Error
}

func (d *discountSettingRepository) poToDo(po *models.DiscountSetting) *do.DiscountSettingDO {
	return &do.DiscountSettingDO{
		ID:            &po.ID,
		CompanyID:     po.CompanyID,
		ApplyBestOnly: po.ApplyBestOnly,
		ApplySequence: po.ApplySequence,
		UpdatedBy:     &po.UpdatedBy,
		CreatedAt:     po.CreatedAt,
		UpdatedAt:     po.UpdatedAt,
	}
}

func (d *discountSettingRepository) doToPo(do *do.DiscountSettingDO) *models.DiscountSetting {
	return &models.DiscountSetting{
		CompanyID:     do.CompanyID,
		ApplyBestOnly: do.ApplyBestOnly,
		ApplySequence: do.ApplySequence,
		UpdatedBy:     *do.UpdatedBy,
		CreatedAt:     do.CreatedAt,
		UpdatedAt:     do.UpdatedAt,
	}
}

func (d *discountSettingRepository) doToUpdateOpt(do *do.DiscountSettingDO) *models.DiscountSettingUpdateOpt {
	return &models.DiscountSettingUpdateOpt{
		ApplyBestOnly: &do.ApplyBestOnly,
		ApplySequence: do.ApplySequence,
		UpdatedBy:     do.UpdatedBy,
		UpdatedAt:     lo.ToPtr(time.Now()),
	}
}

func NewDiscountSettingRepository(db *gorm.DB) DiscountSettingRepository {
	return &discountSettingRepository{db: db}
}
