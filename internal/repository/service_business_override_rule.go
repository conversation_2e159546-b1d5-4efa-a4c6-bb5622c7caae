package repository

import (
	"context"

	"github.com/MoeGolibrary/go-lib/gorm"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_service_business_override_rule_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceBusinessOverrideRuleRepository
type ServiceBusinessOverrideRuleRepository interface {
	ListServiceBusinessOverrideRules(ctx context.Context, companyId int64, overrideConditions map[int64][]do.BusinessOverrideCondition) (map[int64][]do.BusinessOverrideRule, error)
}

type serviceBusinessOverrideRuleRepository struct {
	db *gorm.DB
}

func (s serviceBusinessOverrideRuleRepository) ListServiceBusinessOverrideRules(ctx context.Context, companyId int64, overrideConditions map[int64][]do.BusinessOverrideCondition) (map[int64][]do.BusinessOverrideRule, error) {
	values := make([][]interface{}, 0)
	for serviceId, cond := range overrideConditions {
		for _, c := range cond {
			values = append(values, []interface{}{serviceId, c.BusinessId})
		}
	}
	if len(values) == 0 {
		return make(map[int64][]do.BusinessOverrideRule), nil
	}

	var serviceStaffOverrideRules []models.MoeGroomingServiceLocation
	db := s.db.WithContext(ctx).Where("company_id = ?", companyId).Clauses(gorm.MultiIn{
		Columns: []string{"service_id", "business_id"},
		Values:  values,
	})

	if err := db.Find(&serviceStaffOverrideRules).Error; err != nil {
		return nil, err
	}

	result := make(map[int64][]do.BusinessOverrideRule)
	for _, rule := range serviceStaffOverrideRules {
		result[int64(rule.ServiceID)] = append(result[int64(rule.ServiceID)], converter.ConvertServiceBusinessOverrideRuleModelToDO(rule))
	}
	return result, nil
}

func NewServiceBusinessOverrideRuleRepository(db *gorm.DB) ServiceBusinessOverrideRuleRepository {
	return &serviceBusinessOverrideRuleRepository{db: db}
}
