package repository

import (
	"context"

	"github.com/MoeGolibrary/go-lib/gorm"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_service_staff_override_rule_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceStaffOverrideRuleRepository
type ServiceStaffOverrideRuleRepository interface {
	BatchCreateStaffOverrideRule(ctx context.Context, companyId, serviceId int64, staffOverrideRuleList []do.StaffOverrideRule) (int64, error)
	GetStaffOverrideRules(ctx context.Context, companyId int64, businessId *int64, serviceIds []int64) (map[int64][]do.StaffOverrideRule, error)
	GetStaffOverrideRulesByServiceId(ctx context.Context, serviceId int64) ([]do.StaffOverrideRule, error)
	ListStaffOverrideRules(ctx context.Context, companyId int64, overrideConditions map[int64][]do.StaffOverrideCondition) (map[int64][]do.StaffOverrideRule, error)
	UpdateServiceStaffOverrideRules(ctx context.Context, companyId, serviceId int64, staffOverrideRuleList []do.StaffOverrideRule) (int64, error)
	RemoveServiceStaffOverrideRules(ctx context.Context, companyId, serviceId int64) error
	RemoveServiceStaffOverrideRulesByStaffIds(ctx context.Context, companyId int64, staffIds []int64) error
}

type serviceStaffOverrideRuleRepository struct {
	db *gorm.DB
}

func (s serviceStaffOverrideRuleRepository) ListStaffOverrideRules(ctx context.Context, companyId int64, overrideConditions map[int64][]do.StaffOverrideCondition) (map[int64][]do.StaffOverrideRule, error) {
	values := make([][]interface{}, 0)
	for serviceId, cond := range overrideConditions {
		for _, c := range cond {
			values = append(values, []interface{}{serviceId, c.BusinessId, c.StaffId})
		}
	}
	if len(values) == 0 {
		return make(map[int64][]do.StaffOverrideRule), nil
	}

	var serviceStaffOverrideRules []models.ServiceStaffOverrideRule
	db := s.db.WithContext(ctx).Where("company_id = ?", companyId).Clauses(gorm.MultiIn{
		Columns: []string{"service_id", "business_id", "staff_id"},
		Values:  values,
	})

	if err := db.Find(&serviceStaffOverrideRules).Error; err != nil {
		return nil, err
	}

	result := make(map[int64][]do.StaffOverrideRule)
	for _, rule := range serviceStaffOverrideRules {
		result[rule.ServiceID] = append(result[rule.ServiceID], converter.ConvertServiceStaffOverrideRuleModelToDO(rule))
	}
	return result, nil
}

func NewServiceStaffOverrideRuleRepository(db *gorm.DB) ServiceStaffOverrideRuleRepository {
	return &serviceStaffOverrideRuleRepository{db: db}
}

func (s serviceStaffOverrideRuleRepository) BatchCreateStaffOverrideRule(ctx context.Context, companyId, serviceId int64, staffOverrideRuleList []do.StaffOverrideRule) (int64, error) {
	serviceStaffOverrideRules := converter.ConvertServiceStaffOverrideRuleDoToModel(companyId, serviceId, staffOverrideRuleList)

	result := s.db.WithContext(ctx).Create(&serviceStaffOverrideRules)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (s serviceStaffOverrideRuleRepository) GetStaffOverrideRules(ctx context.Context, companyId int64, businessId *int64, serviceIds []int64) (map[int64][]do.StaffOverrideRule, error) {
	if len(serviceIds) == 0 {
		return make(map[int64][]do.StaffOverrideRule), nil
	}
	var serviceStaffOverrideRules []models.ServiceStaffOverrideRule
	db := s.db.WithContext(ctx).Where("company_id = ?", companyId).Where("service_id IN ?", serviceIds)
	if businessId != nil {
		db.Where("business_id = ?", *businessId)
	}

	if err := db.Find(&serviceStaffOverrideRules).Error; err != nil {
		return nil, err
	}

	result := make(map[int64][]do.StaffOverrideRule)
	for _, rule := range serviceStaffOverrideRules {
		result[rule.ServiceID] = append(result[rule.ServiceID], converter.ConvertServiceStaffOverrideRuleModelToDO(rule))
	}
	return result, nil
}

func (s serviceStaffOverrideRuleRepository) GetStaffOverrideRulesByServiceId(ctx context.Context, serviceId int64) ([]do.StaffOverrideRule, error) {
	if serviceId == 0 {
		return []do.StaffOverrideRule{}, nil
	}

	var serviceStaffOverrideRules []models.ServiceStaffOverrideRule
	if err := s.db.WithContext(ctx).Where("service_id = ?", serviceId).Find(&serviceStaffOverrideRules).Error; err != nil {
		return nil, err
	}

	result := make([]do.StaffOverrideRule, 0)
	for _, rule := range serviceStaffOverrideRules {
		result = append(result, converter.ConvertServiceStaffOverrideRuleModelToDO(rule))
	}
	return result, nil
}

func (s serviceStaffOverrideRuleRepository) UpdateServiceStaffOverrideRules(ctx context.Context, companyId, serviceId int64, staffOverrideRules []do.StaffOverrideRule) (int64, error) {
	serviceStaffOverrideRules := converter.ConvertServiceStaffOverrideRuleDoToModel(companyId, serviceId, staffOverrideRules)

	toDelete := make([][]interface{}, 0)
	toCreate := make([]models.ServiceStaffOverrideRule, 0)
	for i, cond := range serviceStaffOverrideRules {
		toDelete = append(toDelete, []interface{}{cond.BusinessID, cond.StaffID})
		// if both duration and price are 0, delete only
		if cond.Duration != nil || cond.Price != nil {
			toCreate = append(toCreate, serviceStaffOverrideRules[i])
		}
	}

	if len(toDelete) == 0 {
		return 0, nil
	}

	result := s.db.WithContext(ctx).
		Where("company_id = ? AND service_id = ?", companyId, serviceId).
		Clauses(gorm.MultiIn{
			Columns: []string{"business_id", "staff_id"},
			Values:  toDelete,
		}).
		Delete(&models.ServiceStaffOverrideRule{})
	if result.Error != nil {
		return 0, result.Error
	}

	if len(toCreate) == 0 {
		return result.RowsAffected, nil
	}

	result = s.db.WithContext(ctx).Create(&toCreate)
	if result.Error != nil {
		return 0, result.Error
	}
	return result.RowsAffected, nil
}

func (s serviceStaffOverrideRuleRepository) RemoveServiceStaffOverrideRules(ctx context.Context, companyId, serviceId int64) error {
	return s.db.WithContext(ctx).Where("company_id = ? AND service_id = ?", companyId, serviceId).Delete(&models.ServiceStaffOverrideRule{}).Error
}

func (s serviceStaffOverrideRuleRepository) RemoveServiceStaffOverrideRulesByStaffIds(ctx context.Context, companyId int64, staffIds []int64) error {
	return s.db.WithContext(ctx).Where("company_id = ? AND staff_id in ?", companyId, staffIds).Delete(&models.ServiceStaffOverrideRule{}).Error
}
