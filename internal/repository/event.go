package repository

import (
	"context"

	"gorm.io/gorm"

	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

type EventRepository interface {
	Insert(ctx context.Context, event *models.Event) error
	Update(ctx context.Context, event *models.Event) error
	SelectByReference(ctx context.Context, messageType eventbuspb.EventType, referenceID string) ([]*models.Event, error)
	SelectByStatus(ctx context.Context, status []models.EventStatus) ([]*models.Event, error)
}

func NewEventRepository(db *gorm.DB) EventRepository {
	return &eventRepository{db}
}

type eventRepository struct {
	db *gorm.DB
}

func (r *eventRepository) Insert(ctx context.Context, event *models.Event) error {
	return r.db.WithContext(ctx).Omit("id").Create(event).Error
}

func (r *eventRepository) Update(ctx context.Context, event *models.Event) error {
	return r.db.WithContext(ctx).Model(event).Updates(event).Error
}

func (r *eventRepository) SelectByReference(ctx context.Context, messageType eventbuspb.EventType, referenceID string) ([]*models.Event, error) {
	events := make([]*models.Event, 0)
	if err := r.db.WithContext(ctx).Model(&models.Event{}).
		Where("message_type = ?", messageType.String()).
		Where("reference_id = ?", referenceID).
		Where("status = ?", models.StatusPending).
		Find(&events).Error; err != nil {
		return nil, err
	}
	return events, nil
}

func (r *eventRepository) SelectByStatus(ctx context.Context, status []models.EventStatus) ([]*models.Event, error) {
	events := make([]*models.Event, 0)
	if err := r.db.WithContext(ctx).Model(&models.Event{}).
		Where("status IN ?", status).Find(&events).Error; err != nil {
		return nil, err
	}
	return events, nil
}
