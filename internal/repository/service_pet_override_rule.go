package repository

import (
	"context"

	"github.com/MoeGolibrary/go-lib/gorm"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_service_pet_override_rule_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServicePetOverrideRuleRepository
type ServicePetOverrideRuleRepository interface {
	ListServicePetOverrideRules(ctx context.Context, companyId int64, overrideConditions map[int64][]do.PetOverrideCondition) (map[int64][]do.PetOverrideRule, error)
}

type servicePetOverrideRuleRepository struct {
	db *gorm.DB
}

func (s servicePetOverrideRuleRepository) ListServicePetOverrideRules(ctx context.Context, companyId int64, overrideConditions map[int64][]do.PetOverrideCondition) (map[int64][]do.PetOverrideRule, error) {
	values := make([][]interface{}, 0)
	for serviceId, cond := range overrideConditions {
		for _, c := range cond {
			values = append(values, []interface{}{serviceId, c.PetId})
		}
	}
	if len(values) == 0 {
		return make(map[int64][]do.PetOverrideRule), nil
	}

	var serviceStaffOverrideRules []models.MoeGroomingCustomerService
	db := s.db.WithContext(ctx).Where("company_id = ?", companyId).Where("status = 1").Clauses(gorm.MultiIn{
		Columns: []string{"service_id", "pet_id"},
		Values:  values,
	})

	if err := db.Find(&serviceStaffOverrideRules).Error; err != nil {
		return nil, err
	}

	result := make(map[int64][]do.PetOverrideRule)
	for _, rule := range serviceStaffOverrideRules {
		result[int64(rule.ServiceID)] = append(result[int64(rule.ServiceID)], converter.ConvertServicePetOverrideRuleModelToDO(rule))
	}
	return result, nil
}

func NewServicePetOverrideRuleRepository(db *gorm.DB) ServicePetOverrideRuleRepository {
	return &servicePetOverrideRuleRepository{db: db}
}
