package repository

import (
	"context"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/samber/lo"
	"gorm.io/gorm"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_evaluation_pet_breed_filter_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository EvaluationPetBreedFilterRepository
type EvaluationPetBreedFilterRepository interface {
	BatchCreateFilter(context.Context, *gorm.DB, int64, []do.PetBreedFilter) error
	BatchUpdateFilter(context.Context, *gorm.DB, int64, []do.PetBreedFilter) error
	ListByEvaluationID(context.Context, int64) ([]models.EvaluationPetBreedFilter, error)
	ListByEvaluationIDs(context.Context, []int64) ([]models.EvaluationPetBreedFilter, error)
}

type evaluationPetBreedFilterRepository struct {
	db *gorm.DB
}

func (e evaluationPetBreedFilterRepository) ListByEvaluationIDs(ctx context.Context, evaluationIDs []int64) ([]models.EvaluationPetBreedFilter, error) {
	if len(evaluationIDs) == 0 {
		return nil, nil
	}
	var filters []models.EvaluationPetBreedFilter
	err := e.db.WithContext(ctx).Table(models.TableNameEvaluationPetBreedFilter).Where("evaluation_id in ?", evaluationIDs).Find(&filters).Error
	if err != nil {
		return nil, err
	}
	return filters, nil
}

func (e evaluationPetBreedFilterRepository) ListByEvaluationID(ctx context.Context, i int64) ([]models.EvaluationPetBreedFilter, error) {
	var filters []models.EvaluationPetBreedFilter
	err := e.db.WithContext(ctx).Table(models.TableNameEvaluationPetBreedFilter).Where("evaluation_id = ?", i).Find(&filters).Error
	if err != nil {
		return nil, err
	}
	return filters, nil
}

func (e evaluationPetBreedFilterRepository) BatchUpdateFilter(
	ctx context.Context, tx *gorm.DB, evaluationID int64, filters []do.PetBreedFilter) error {
	err := tx.WithContext(ctx).Delete(&models.EvaluationPetBreedFilter{}).Where("evaluation_id = ?", evaluationID).Error
	if err != nil {
		return err
	}
	breedFilters := lo.Map(filters, func(filter do.PetBreedFilter, _ int) models.EvaluationPetBreedFilter {
		return models.EvaluationPetBreedFilter{
			EvaluationID: evaluationID,
			PetType:      filter.PetType,
			IsAllBreed:   filter.IsAllBreed,
			BreedNames:   filter.BreedNames,
		}
	})
	err = tx.WithContext(ctx).Model(&models.EvaluationPetBreedFilter{}).Create(&breedFilters).Error
	if err != nil {
		return err
	}
	return nil
}

func (e evaluationPetBreedFilterRepository) BatchCreateFilter(
	ctx context.Context, tx *gorm.DB, evaluationID int64, filters []do.PetBreedFilter) error {
	breedFilters := lo.Map(filters, func(filter do.PetBreedFilter, _ int) models.EvaluationPetBreedFilter {
		return models.EvaluationPetBreedFilter{
			EvaluationID: evaluationID,
			PetType:      filter.PetType,
			IsAllBreed:   filter.IsAllBreed,
			BreedNames:   filter.BreedNames,
		}
	})
	err := tx.WithContext(ctx).Model(&models.EvaluationPetBreedFilter{}).Create(&breedFilters).Error
	if err != nil {
		return err
	}
	return nil
}

func NewEvaluationPetBreedFilterRepository(db *gorm.DB) EvaluationPetBreedFilterRepository {
	return &evaluationPetBreedFilterRepository{db: db}
}
