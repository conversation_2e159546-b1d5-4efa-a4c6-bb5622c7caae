package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gormx"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_playgroup_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository PlaygroupRepository
type PlaygroupRepository interface {
	CreatePlaygroup(ctx context.Context, playgroup *models.Playgroup) (int64, error)
	GetMaxSortPlaygroupByCompanyID(ctx context.Context, companyID int64) (*models.Playgroup, error)
	GetPlaygroupByID(ctx context.Context, where *models.PlaygroupWhereOpt) (*models.Playgroup, error)
	GetPlaygroupByName(ctx context.Context, where *models.PlaygroupWhereOpt) (*models.Playgroup, error)
	ListPlaygroup(ctx context.Context, where *models.PlaygroupWhereOpt, pagination *utilsV2.PaginationRequest) ([]*models.Playgroup, int32, error)
	UpdatePlaygroup(ctx context.Context, where *models.PlaygroupWhereOpt, updateOpt *models.PlaygroupUpdateOpt) error
	DeletePlaygroup(ctx context.Context, whereOpt *models.PlaygroupWhereOpt, deletedBy int64) error
	BatchUpdatePlaygroupSort(ctx context.Context, companyID int64, updates map[int64]int32, operatorID int64) error
}

type playgroupRepository struct {
	db *gorm.DB
}

func NewPlaygroupRepository() PlaygroupRepository {
	return &playgroupRepository{db: resource.GetOfferingDB()}
}

func (r *playgroupRepository) GetPlaygroupByID(ctx context.Context, where *models.PlaygroupWhereOpt) (*models.Playgroup, error) {
	playgroup := &models.Playgroup{}
	err := r.db.WithContext(ctx).Where(gormx.Query(where)).First(playgroup).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, errors.WithStack(err)
	}
	return playgroup, nil
}

func (r *playgroupRepository) GetPlaygroupByName(ctx context.Context, where *models.PlaygroupWhereOpt) (*models.Playgroup, error) {
	playgroup := &models.Playgroup{}
	err := r.db.WithContext(ctx).Where(gormx.Query(where)).First(playgroup).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, errors.WithStack(err)
	}
	return playgroup, nil
}

func (r *playgroupRepository) CreatePlaygroup(ctx context.Context, playgroup *models.Playgroup) (int64, error) {
	err := r.db.WithContext(ctx).Create(playgroup).Error
	if err != nil {
		return 0, errors.WithStack(err)
	}
	return playgroup.ID, nil
}

func (r *playgroupRepository) GetMaxSortPlaygroupByCompanyID(ctx context.Context, companyID int64) (*models.Playgroup, error) {
	playgroup := &models.Playgroup{}
	err := r.db.WithContext(ctx).Where("company_id = ?", companyID).Order("sort DESC").First(playgroup).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, errors.WithStack(err)
	}
	return playgroup, nil
}

func (r *playgroupRepository) ListPlaygroup(ctx context.Context, whereOpt *models.PlaygroupWhereOpt, pagination *utilsV2.PaginationRequest) ([]*models.Playgroup, int32, error) {
	db := r.db.WithContext(ctx)

	// 默认不包含已删除记录
	includeDeleted := false
	if whereOpt != nil && whereOpt.IncludeDeleted != nil {
		includeDeleted = *whereOpt.IncludeDeleted
	}

	// 构建基础查询
	query := db.Model(&models.Playgroup{}).Where(gormx.Query(whereOpt))
	if includeDeleted {
		query = query.Unscoped()
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, errors.WithStack(err)
	}

	// 查询数据，重用相同的查询条件
	if pagination != nil {
		if pagination.PageNum == nil {
			pagination.PageNum = lo.ToPtr(int32(1))
		}
		if pagination.PageSize == nil {
			pagination.PageSize = lo.ToPtr(int32(1000))
		}
	}
	playgroups := make([]*models.Playgroup, 0)
	if err := query.Scopes(Paginate(pagination)).
		Find(&playgroups).Error; err != nil {
		return nil, 0, errors.WithStack(err)
	}

	return playgroups, int32(total), nil
}

func (r *playgroupRepository) UpdatePlaygroup(ctx context.Context, where *models.PlaygroupWhereOpt, updateOpt *models.PlaygroupUpdateOpt) error {
	err := r.db.WithContext(ctx).Model(&models.Playgroup{}).Where(gormx.Query(where)).Updates(gormx.Update(updateOpt)).Error
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (r *playgroupRepository) DeletePlaygroup(ctx context.Context, whereOpt *models.PlaygroupWhereOpt, deletedBy int64) error {
	now := time.Now()
	err := r.db.WithContext(ctx).Model(&models.Playgroup{}).Where(gormx.Query(whereOpt)).Updates(gormx.Update(&models.PlaygroupDeleteOpt{
		DeletedBy: &deletedBy,
		UpdatedAt: &now,
		DeletedAt: gorm.DeletedAt{Valid: true, Time: now},
	})).Error
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (r *playgroupRepository) BatchUpdatePlaygroupSort(ctx context.Context, companyID int64, updates map[int64]int32, operatorID int64) error {
	if len(updates) == 0 {
		return nil
	}

	// 构建 CASE 语句
	var cases string
	var ids []int64
	for id, sort := range updates {
		if cases != "" {
			cases += " "
		}
		cases += fmt.Sprintf("WHEN id = %d THEN %d", id, sort)
		ids = append(ids, id)
	}

	// 构建完整的更新语句
	err := r.db.WithContext(ctx).
		Model(&models.Playgroup{}).
		Where("company_id = ? AND id IN ?", companyID, ids).
		Updates(map[string]interface{}{
			"sort":       gorm.Expr(fmt.Sprintf("CASE %s END", cases)),
			"updated_at": time.Now(),
			"updated_by": operatorID,
		}).Error
	if err != nil {
		return errors.WithStack(err)
	}
	return nil
}
