package repository

import (
	"context"

	"github.com/pkg/errors"
	"gorm.io/gorm/clause"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/go-lib/gorm"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_vaccine_requirement_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository VaccineRequirementRepository
type VaccineRequirementRepository interface {
	// BatchCreateVaccineRequirement adds a vaccine requirement
	BatchCreateVaccineRequirement(ctx context.Context, tx *gorm.DB, records []*models.VaccineRequirement) error
	// RemoveVaccineRequirement removes a vaccine requirement
	RemoveVaccineRequirement(ctx context.Context, tx *gorm.DB, opt *models.VaccineRequirementDeleteOpt) error
	// ListVaccineRequirements lists all vaccine requirements with filters
	ListVaccineRequirements(ctx context.Context, tx *gorm.DB, opt *models.VaccineRequirementWhereOpt, paginationRequest *utilsV2.PaginationRequest) (int64, []*models.VaccineRequirement, error)
}

type vaccineRequirement struct {
	db *gorm.DB
}

func (v vaccineRequirement) BatchCreateVaccineRequirement(ctx context.Context, tx *gorm.DB, records []*models.VaccineRequirement) error {
	if len(records) == 0 {
		return nil
	}
	if tx == nil {
		tx = v.db.WithContext(ctx)
	}

	if err := tx.WithContext(ctx).Create(records).Clauses(clause.OnConflict{DoNothing: true}).Error; err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (v vaccineRequirement) RemoveVaccineRequirement(ctx context.Context, tx *gorm.DB, opt *models.VaccineRequirementDeleteOpt) error {
	if tx == nil {
		tx = v.db.WithContext(ctx)
	}

	if err := tx.WithContext(ctx).Where(gormx.Query(opt)).Delete(&models.VaccineRequirement{}).Error; err != nil {
		return errors.WithStack(err)
	}
	return nil
}

func (v vaccineRequirement) ListVaccineRequirements(ctx context.Context, tx *gorm.DB, opt *models.VaccineRequirementWhereOpt, paginationRequest *utilsV2.PaginationRequest) (int64, []*models.VaccineRequirement, error) {
	if tx == nil {
		tx = v.db.WithContext(ctx)
	}
	var vaccineRequirements []*models.VaccineRequirement
	if err := tx.WithContext(ctx).Scopes(Paginate(paginationRequest)).Where(gormx.Query(opt)).Find(&vaccineRequirements).Error; err != nil {
		return 0, nil, errors.WithStack(err)
	}

	var total int64
	if err := tx.WithContext(ctx).Where(gormx.Query(opt)).Model(&models.VaccineRequirement{}).Count(&total).Error; err != nil {
		return 0, nil, errors.WithStack(err)
	}

	return total, vaccineRequirements, nil
}

func NewVaccineRequirement() VaccineRequirementRepository {
	return &vaccineRequirement{
		db: resource.GetOfferingDB(),
	}
}
