package repository

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/utils"
)

type GroupClassSessionRepository interface {
	BatchInsert(ctx context.Context, sessions []*do.GroupClassSessionDO) ([]*do.GroupClassSessionDO, error)
	Get(ctx context.Context, id int64) (*models.GroupClassSession, error)
	Update(ctx context.Context, session *do.GroupClassSessionDO) error
	List(ctx context.Context, loc *time.Location, filter *do.GroupClassSessionFilter,
		page *utilsV2.PaginationRequest) ([]*do.GroupClassSessionDO, *utilsV2.PaginationResponse, error)
	Delete(ctx context.Context, filter *do.GroupClassSessionFilter) error

	GetLast(ctx context.Context, instanceID int64) (*models.GroupClassSession, error)
}

func NewGroupClassSessionRepository(db *gorm.DB) GroupClassSessionRepository {
	return &groupClassSessionRepository{
		db: db,
	}
}

type groupClassSessionRepository struct {
	db *gorm.DB
}

func (r *groupClassSessionRepository) BatchInsert(ctx context.Context,
	sessions []*do.GroupClassSessionDO) ([]*do.GroupClassSessionDO, error) {
	if len(sessions) == 0 {
		return nil, nil
	}
	pos, err := converter.ConvertGroupClassSessionDOListToPO(sessions)
	if err != nil {
		return nil, err
	}
	if err := r.db.WithContext(ctx).Model(&models.GroupClassSession{}).
		Omit("id").
		CreateInBatches(pos, 50).Error; err != nil {
		return nil, err
	}
	return converter.ConvertGroupClassSessionPOListToDO(pos, sessions[0].StartTime.Location().String())
}

func (r *groupClassSessionRepository) Get(ctx context.Context, id int64) (*models.GroupClassSession, error) {
	var s models.GroupClassSession
	if err := r.db.WithContext(ctx).Model(&models.GroupClassSession{}).Where("id = ?", id).First(&s).Error; err != nil {
		return nil, err
	}
	return &s, nil
}

func (r *groupClassSessionRepository) Update(ctx context.Context, session *do.GroupClassSessionDO) error {
	po, err := converter.ConvertGroupClassSessionDOToPO(session)
	if err != nil {
		return fmt.Errorf("failed to convert: %v", err)
	}
	return r.db.WithContext(ctx).Model(&models.GroupClassSession{}).
		Where("id = ?", session.ID).
		Updates(po).Error
}

func (r *groupClassSessionRepository) List(ctx context.Context, loc *time.Location,
	filter *do.GroupClassSessionFilter,
	page *utilsV2.PaginationRequest) ([]*do.GroupClassSessionDO, *utilsV2.PaginationResponse, error) {
	var (
		pos   []*models.GroupClassSession
		total int64
	)
	if err := r.db.WithContext(ctx).Model(&models.GroupClassSession{}).
		Scopes(filter.Apply).
		Count(&total).
		Scopes(utils.BuildPageScopes(page)).
		Order("start_time").
		Find(&pos).Error; err != nil {
		return nil, nil, err
	}

	dos, err := converter.ConvertGroupClassSessionPOListToDO(pos, loc.String())
	if err != nil {
		return nil, nil, fmt.Errorf("failed to convert: %v", err)
	}
	return dos, &utilsV2.PaginationResponse{
		Total:    int32(total),
		PageSize: page.GetPageSize(),
		PageNum:  page.GetPageNum(),
	}, nil
}

func (r *groupClassSessionRepository) Delete(ctx context.Context,
	filter *do.GroupClassSessionFilter) error {
	return r.db.WithContext(ctx).Scopes(filter.Apply).Delete(&models.GroupClassSession{}).Error
}

func (r *groupClassSessionRepository) GetLast(ctx context.Context, instanceID int64) (*models.GroupClassSession, error) {
	var po models.GroupClassSession
	if err := r.db.WithContext(ctx).Model(&models.GroupClassSession{}).
		Where("instance_id = ?", instanceID).
		Order("start_time DESC").
		Limit(1).
		Find(&po).Error; err != nil {
		return nil, err
	}
	return &po, nil
}
