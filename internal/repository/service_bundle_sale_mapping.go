package repository

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_service_bundle_sale_mapping_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceBundleSaleMappingRepository
type ServiceBundleSaleMappingRepository interface {
	// Delete deletes service bundle sale records for a parent service
	Delete(ctx context.Context, tx *gorm.DB, companyId int64, parentServiceId int64) error
	// BatchInsert inserts multiple service bundle sale records
	BatchInsert(ctx context.Context, tx *gorm.DB, companyId int64, parentServiceId int64, bundleServiceIds []int64) error
	// List list service bundle sale
	List(ctx context.Context, companyId int64, parentServiceIds []int64) ([]*models.ServiceBundleSaleMapping, error)
}

type serviceBundleSaleMappingRepository struct {
	db *gorm.DB
}

func (s serviceBundleSaleMappingRepository) Delete(ctx context.Context, tx *gorm.DB, companyId int64, parentServiceId int64) error {
	if tx == nil {
		tx = s.db.WithContext(ctx)
	}

	return tx.Where(gormx.Query(&models.ServiceBundleSaleMappingFilterWhereOpt{
		CompanyID: companyId,
		ServiceID: []int64{parentServiceId},
	})).Delete(&models.ServiceBundleSaleMapping{}).Error
}

func (s serviceBundleSaleMappingRepository) BatchInsert(ctx context.Context, tx *gorm.DB, companyId int64, parentServiceId int64, bundleServiceIds []int64) error {
	if tx == nil {
		tx = s.db.WithContext(ctx)
	}

	if len(bundleServiceIds) == 0 {
		return nil
	}

	records := make([]*models.ServiceBundleSaleMapping, 0, len(bundleServiceIds))
	for _, bundleServiceId := range bundleServiceIds {
		records = append(records, &models.ServiceBundleSaleMapping{
			CompanyID:       companyId,
			ServiceID:       parentServiceId,
			BundleServiceID: bundleServiceId,
		})
	}

	return tx.Create(&records).Error
}

func (s serviceBundleSaleMappingRepository) List(ctx context.Context, companyId int64, parentServiceIds []int64) ([]*models.ServiceBundleSaleMapping, error) {
	var records []*models.ServiceBundleSaleMapping
	if err := s.db.WithContext(ctx).Where(gormx.Query(&models.ServiceBundleSaleMappingFilterWhereOpt{
		CompanyID: companyId,
		ServiceID: parentServiceIds,
	})).Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

func NewServiceBundleSaleRepository(db *gorm.DB) ServiceBundleSaleMappingRepository {
	return &serviceBundleSaleMappingRepository{
		db: db,
	}
}
