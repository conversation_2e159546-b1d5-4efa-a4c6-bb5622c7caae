package repository

import (
	"context"
	"errors"
	"reflect"
	"regexp"
	"testing"

	sqlmock "github.com/DATA-DOG/go-sqlmock"

	"github.com/MoeGolibrary/go-lib/gorm"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

func TestCategoryRepository_GetCategory(t *testing.T) {
	type mockFunc func(sqlMock sqlmock.Sqlmock)
	type args struct {
		ctx        context.Context
		categoryId int64
	}
	tests := []struct {
		name     string
		mockFunc mockFunc
		args     args
		want     *models.MoeGroomingServiceCategory
		wantErr  error
	}{
		{
			name: "Test_serviceRepository_GetService_Normal",
			mockFunc: func(sqlMock sqlmock.Sqlmock) {
				sqlMock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `moe_grooming_service_category` WHERE `moe_grooming_service_category`.`id` = ? ORDER BY `moe_grooming_service_category`.`id` LIMIT ?")).
					WithArgs(1, 1).
					WillReturnRows(sqlMock.NewRows([]string{"id", "name"}).AddRow(1, "test"))
			},
			args: args{
				ctx:        context.TODO(),
				categoryId: 1,
			},
			want: &models.MoeGroomingServiceCategory{
				ID:   1,
				Name: "test",
			},
			wantErr: nil,
		},
		{
			name: "Test_serviceRepository_GetService_Not_Found",
			mockFunc: func(sqlMock sqlmock.Sqlmock) {
				sqlMock.ExpectQuery(regexp.QuoteMeta("SELECT * FROM `moe_grooming_service_category` WHERE `moe_grooming_service_category`.`id` = ? ORDER BY `moe_grooming_service_category`.`id` LIMIT ?")).
					WithArgs(1, 1).
					WillReturnRows(sqlMock.NewRows([]string{"id", "name"}))
			},
			args: args{
				ctx:        context.TODO(),
				categoryId: 1,
			},
			want:    nil,
			wantErr: ErrRecordNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			db, sqlMock := gorm.MockMySQL(t)
			s := &categoryRepository{
				db: db,
			}
			tt.mockFunc(sqlMock)
			got, err := s.GetCategory(tt.args.ctx, tt.args.categoryId)
			if !errors.Is(err, tt.wantErr) {
				t.Errorf("GetCategory() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetCategory() got = %v, want %v", got, tt.want)
			}
		})
	}
}
