package repository

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_service_pet_code_availability_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServicePetCodeAvailabilityRepository
type ServicePetCodeAvailabilityRepository interface {
	// Upsert upsert service pet code availability config
	Upsert(ctx context.Context, tx *gorm.DB, model *models.MoeServicePetCodeFilter) error
	// List list service pet code availability config
	List(ctx context.Context, opt *models.ServicePetCodeFilterWhereOpt) ([]*models.MoeServicePetCodeFilter, error)
}

type servicePetCodeAvailabilityRepository struct {
	db *gorm.DB
}

func (s servicePetCodeAvailabilityRepository) Upsert(ctx context.Context, tx *gorm.DB, model *models.MoeServicePetCodeFilter) error {
	if tx == nil {
		tx = s.db.WithContext(ctx)
	}

	return tx.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "service_id"}},
		DoUpdates: clause.AssignmentColumns([]string{"is_whitelist", "is_all_pet_code", "pet_code_list"}),
	}).Create(model).Error
}

func (s servicePetCodeAvailabilityRepository) List(ctx context.Context, opt *models.ServicePetCodeFilterWhereOpt) ([]*models.MoeServicePetCodeFilter, error) {
	var records []*models.MoeServicePetCodeFilter
	if err := s.db.WithContext(ctx).Where(gormx.Query(opt)).Find(&records).Error; err != nil {
		return nil, err
	}

	return records, nil
}

func NewServicePetCodeAvailabilityRepository(db *gorm.DB) ServicePetCodeAvailabilityRepository {
	return &servicePetCodeAvailabilityRepository{
		db: db,
	}
}
