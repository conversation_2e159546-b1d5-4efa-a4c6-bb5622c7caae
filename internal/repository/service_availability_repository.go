package repository

import (
	"context"
	"encoding/json"

	"github.com/samber/lo"
	"google.golang.org/protobuf/proto"
	"gorm.io/gorm"
	"gorm.io/gormx"

	customerModelV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"

	"github.com/MoeGolibrary/moego-svc-offering/internal/constant"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_service_availability_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository ServiceAvailabilityRepository
type ServiceAvailabilityRepository interface {
	RemovePetTypeFilter(ctx context.Context, companyId int64, petType customerModelV1.PetType) error
	RemovePetBreedFilter(ctx context.Context, companyId int64, petBreed string) error
	RemovePetSizeFilter(ctx context.Context, companyId int64, petSizeId int64) error
	RemovePetCoatTypeFilter(ctx context.Context, companyId int64, petCoatTypeId int64) error
	RemoveServiceFilter(ctx context.Context, companyId int64, serviceId int64) error
	RemoveLodgingFilter(ctx context.Context, companyId int64, lodgingTypeId int64) error
}

type serviceAvailabilityRepository struct {
	db *gorm.DB
}

func (s serviceAvailabilityRepository) RemovePetTypeFilter(ctx context.Context, companyId int64, petType customerModelV1.PetType) error {
	return s.db.WithContext(ctx).Where(gormx.Query(models.BreedBindingWhereOpt{
		CompanyID: proto.Int64(companyId),
		PetTypeId: proto.Int32(int32(petType)),
	})).Delete(models.MoeGroomingServiceBreedBinding{}).Error
}

func (s serviceAvailabilityRepository) RemovePetBreedFilter(ctx context.Context, companyId int64, petBreed string) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		petBreedFilterRecords := make([]models.MoeGroomingServiceBreedBinding, 0)
		if err := tx.Where(gormx.Query(models.BreedBindingWhereOpt{
			CompanyID: proto.Int64(companyId),
			IsAll:     proto.Bool(false),
			Status:    proto.Int32(int32(constant.NormalStatus)),
		})).Find(&petBreedFilterRecords).Error; err != nil {
			return err
		}

		for _, petBreedFilterRecord := range petBreedFilterRecords {
			breedingNameList := petBreedFilterRecord.GetBreedingNameList()
			if lo.Contains(breedingNameList, petBreed) {
				breedingNameList = lo.Filter(breedingNameList, func(breedingName string, _ int) bool {
					return breedingName != petBreed
				})
				breedingNameListByte, _ := json.Marshal(breedingNameList)
				if err := tx.Model(&models.MoeGroomingServiceBreedBinding{}).Where(gormx.Query(models.BreedBindingWhereOpt{
					Id: proto.Int32(petBreedFilterRecord.ID),
				})).Updates(gormx.Update(models.BreedBindingUpdateOpt{
					BreedNameList: proto.String(string(breedingNameListByte)),
				})).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})
}

func (s serviceAvailabilityRepository) RemovePetSizeFilter(ctx context.Context, companyId int64, petSizeId int64) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		services := make([]models.MoeGroomingService, 0)
		if err := tx.Where(gormx.Query(models.ServiceWhereOpt{
			CompanyID:     proto.Int64(companyId),
			Status:        proto.Int32(int32(constant.NormalStatus)),
			PetSizeFilter: proto.Bool(true),
		})).Find(&services).Error; err != nil {
			return err
		}

		for _, service := range services {
			customizedPetSizeIds := service.GetCustomizedPetSizeList()
			if lo.Contains(customizedPetSizeIds, petSizeId) {
				customizedPetSizeIds = lo.Filter(customizedPetSizeIds, func(sizeId int64, _ int) bool {
					return sizeId != petSizeId
				})

				customizedPetSizeIdsByte, _ := json.Marshal(customizedPetSizeIds)
				if err := tx.Model(&models.MoeGroomingService{}).Where(gormx.Query(models.ServiceWhereOpt{
					Id: proto.Int64(int64(service.ID)),
				})).Updates(gormx.Update(models.ServiceUpdateOpt{
					AllowedPetSizeList: proto.String(string(customizedPetSizeIdsByte)),
				})).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})
}

func (s serviceAvailabilityRepository) RemovePetCoatTypeFilter(ctx context.Context, companyId int64, petCoatTypeId int64) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		petCoatTypeFilterRecords := make([]models.MoeGroomingServiceCoatBinding, 0)
		if err := tx.Where(gormx.Query(models.CoatBindingWhereOpt{
			CompanyID: proto.Int64(companyId),
		})).Find(&petCoatTypeFilterRecords).Error; err != nil {
			return err
		}

		for _, petCoatTypeFilterRecord := range petCoatTypeFilterRecords {
			coatTypeList := petCoatTypeFilterRecord.GetCoatIDList()
			if lo.Contains(coatTypeList, petCoatTypeId) {
				coatTypeList = lo.Filter(coatTypeList, func(coatTypeId int64, _ int) bool {
					return coatTypeId != petCoatTypeId
				})
				coatTypeListByte, _ := json.Marshal(coatTypeList)
				if err := tx.Model(&models.MoeGroomingServiceCoatBinding{}).Where(gormx.Query(models.CoatBindingWhereOpt{
					Id: proto.Int64(petCoatTypeFilterRecord.ID),
				})).Updates(gormx.Update(models.CoatBindingUpdateOpt{
					CoatIdList: proto.String(string(coatTypeListByte)),
				})).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})
}

func (s serviceAvailabilityRepository) RemoveServiceFilter(ctx context.Context, companyId int64, serviceId int64) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		addonApplicableServiceRecords := make([]models.MoeGroomingAddonApplicableService, 0)
		if err := tx.Where(gormx.Query(models.MoeGroomingAddonApplicableServiceWhereOpt{
			CompanyID:               proto.Int64(companyId),
			AvailableForAllServices: proto.Bool(false),
		})).Find(&addonApplicableServiceRecords).Error; err != nil {
			return err
		}

		for _, addonApplicableServiceRecord := range addonApplicableServiceRecords {
			allowedServiceIdList := addonApplicableServiceRecord.GetAllowedServiceList()
			if lo.Contains(allowedServiceIdList, serviceId) {
				allowedServiceIdList = lo.Filter(allowedServiceIdList, func(id int64, _ int) bool {
					return id != serviceId
				})
				availableServiceIdListByte, _ := json.Marshal(allowedServiceIdList)
				if err := tx.Model(&models.MoeGroomingAddonApplicableService{}).Where(gormx.Query(models.MoeGroomingAddonApplicableServiceWhereOpt{
					Id: proto.Int64(addonApplicableServiceRecord.ID),
				})).Updates(gormx.Update(models.MoeGroomingAddonApplicableServiceUpdateOpt{
					AvailableServiceIDList: proto.String(string(availableServiceIdListByte)),
				})).Error; err != nil {
					return err
				}
			}
		}

		return nil

	})
}

func (s serviceAvailabilityRepository) RemoveLodgingFilter(ctx context.Context, companyId int64, lodgingTypeId int64) error {
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		services := make([]models.MoeGroomingService, 0)
		if err := tx.Where(gormx.Query(models.ServiceWhereOpt{
			CompanyID:     proto.Int64(companyId),
			Status:        proto.Int32(int32(constant.NormalStatus)),
			LodgingFilter: proto.Bool(true),
		})).Find(&services).Error; err != nil {
			return err
		}

		for _, service := range services {
			customizedLodgingTypeIds := service.GetCustomizedLodgingList()
			if lo.Contains(customizedLodgingTypeIds, lodgingTypeId) {
				customizedLodgingTypeIds = lo.Filter(customizedLodgingTypeIds, func(id int64, _ int) bool {
					return id != lodgingTypeId
				})
				customizedLodgingTypeIdsByte, _ := json.Marshal(customizedLodgingTypeIds)
				if err := tx.Model(&models.MoeGroomingService{}).Where(gormx.Query(models.ServiceWhereOpt{
					Id: proto.Int64(int64(service.ID)),
				})).Updates(gormx.Update(models.ServiceUpdateOpt{
					AllowedLodgingList: proto.String(string(customizedLodgingTypeIdsByte)),
				})).Error; err != nil {
					return err
				}
			}
		}

		return nil
	})
}

func NewServiceAvailabilityRepository(db *gorm.DB) ServiceAvailabilityRepository {
	return &serviceAvailabilityRepository{
		db: db,
	}
}
