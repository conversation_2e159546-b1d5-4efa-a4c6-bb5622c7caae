package repository

import "gorm.io/gorm"

type TransactionManager interface {
	Tx(fn func(tx Transaction) error) error
}

func NewTransactionManager(db *gorm.DB) TransactionManager {
	return &transactionManager{db: db}
}

type transactionManager struct{ db *gorm.DB }

func (m *transactionManager) Tx(fn func(tx Transaction) error) error {
	return m.db.Transaction(func(tx *gorm.DB) error {
		return fn(newTransaction(tx))
	})
}

type Transaction interface {
	GroupClassInstance() GroupClassInstanceRepository
	GroupClassSession() GroupClassSessionRepository
	Event() EventRepository
}

type transaction struct {
	groupClassInstanceRepository GroupClassInstanceRepository
	groupClassSessionRepository  GroupClassSessionRepository
	eventRepository              EventRepository
}

func newTransaction(tx *gorm.DB) Transaction {
	return &transaction{
		groupClassInstanceRepository: NewGroupClassInstanceRepository(tx),
		groupClassSessionRepository:  NewGroupClassSessionRepository(tx),
		eventRepository:              NewEventRepository(tx),
	}
}

func (t *transaction) GroupClassInstance() GroupClassInstanceRepository {
	return t.groupClassInstanceRepository
}

func (t *transaction) GroupClassSession() GroupClassSessionRepository {
	return t.groupClassSessionRepository
}

func (t *transaction) Event() EventRepository {
	return t.eventRepository
}
