package repository

import (
	"context"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"gorm.io/gorm"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_evaluation_location_override_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository EvaluationLocationOverrideRepository
type EvaluationLocationOverrideRepository interface {
	ListByEvaluationIDs(ctx context.Context, evaluationIDs []int64) ([]*models.EvaluationLocationOverride, error)
}

type evaluationLocationOverrideRepository struct {
	db *gorm.DB
}

func (e evaluationLocationOverrideRepository) ListByEvaluationIDs(ctx context.Context, evaluationIDs []int64) ([]*models.EvaluationLocationOverride, error) {
	if len(evaluationIDs) == 0 {
		return nil, nil
	}
	var overrides []*models.EvaluationLocationOverride
	err := e.db.WithContext(ctx).Where("evaluation_id IN ?", evaluationIDs).Find(&overrides).Error
	if err != nil {
		return nil, err
	}
	return overrides, nil
}

func NewEvaluationLocationOverrideRepository(db *gorm.DB) EvaluationLocationOverrideRepository {
	return &evaluationLocationOverrideRepository{
		db: db,
	}
}
