package repository

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gormx"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
)

//go:generate mockgen -package mocks -destination ../mocks/mock_lodging_unit_repository.go github.com/MoeGolibrary/moego-svc-offering/internal/repository LodgingUnitRepository
type LodgingUnitRepository interface {
	WithTx(tx *gorm.DB) LodgingUnitRepository

	BatchAdd(ctx context.Context, dos []*do.LodgingUnitDO) ([]*do.LodgingUnitDO, error)
	Update(ctx context.Context, whereOpt *models.LodgingUnitWhereOpt, updateOpt *do.LodgingUnitUpdateOpt) (*do.LodgingUnitDO, error)
	BatchUpdateById(ctx context.Context, companyId *int64, updateOpts []*do.LodgingUnitUpdateByIDOpt) ([]*do.LodgingUnitDO, error)
	List(ctx context.Context, whereOpt *models.LodgingUnitWhereOpt) ([]*models.LodgingUnit, error)
	MGet(ctx context.Context, idList []int64) ([]*do.LodgingUnitDO, error)
	BatchDelete(ctx context.Context, ids []int64, companyId *int64, deletedBy *int64) error
	IsExist(ctx context.Context, companyID *int64, whereOpt *do.LodgingUnitWhereOpt) (bool, error)
	GetMaxSort(ctx context.Context, whereOpt *do.LodgingUnitWhereOpt) (int32, error)
}

type lodgingUnitRepository struct {
	db *gorm.DB
}

func (l lodgingUnitRepository) WithTx(tx *gorm.DB) LodgingUnitRepository {
	return &lodgingUnitRepository{db: tx}
}

func (l lodgingUnitRepository) BatchAdd(ctx context.Context, dos []*do.LodgingUnitDO) ([]*do.LodgingUnitDO, error) {
	pos := converter.ConvertLodgingUnitDOListToPO(dos)
	if len(pos) > 0 {
		if err := l.db.WithContext(ctx).Create(pos).Error; err != nil {
			return nil, errors.WithStack(err)
		}
	}
	return converter.ConvertLodgingUnitPOListToDO(pos), nil
}

func (l lodgingUnitRepository) Update(ctx context.Context, whereOpt *models.LodgingUnitWhereOpt, updateOpt *do.LodgingUnitUpdateOpt) (*do.LodgingUnitDO, error) {
	if whereOpt == nil || (whereOpt.ID == nil && whereOpt.IDIn == nil && whereOpt.CompanyID == nil) {
		return nil, errors.WithStack(errors.New("missing where condition"))
	}
	update := converter.ConvertLodgingUnitUpdateOptDoToModel(updateOpt)
	update.UpdatedAt = lo.ToPtr(time.Now())
	result := models.LodgingUnit{}
	err := l.db.WithContext(ctx).Model(&result).Clauses(clause.Returning{}).Where(gormx.Query(whereOpt)).Updates(gormx.Update(update)).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return converter.ConvertLodgingUnitPOToDO(&result), nil
}

func (l lodgingUnitRepository) BatchUpdateById(
	ctx context.Context,
	companyId *int64,
	updateOpts []*do.LodgingUnitUpdateByIDOpt,
) ([]*do.LodgingUnitDO, error) {
	if len(updateOpts) == 0 {
		return nil, nil
	}
	results := make([]*do.LodgingUnitDO, 0)

	err := l.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {

		for _, updateOpt := range updateOpts {
			whereOpt := &models.LodgingUnitWhereOpt{
				ID:        &updateOpt.ID,
				CompanyID: companyId,
			}
			result, err := l.WithTx(tx).
				Update(ctx, whereOpt, converter.ConvertLodgingUnitUpdateByIdToUpdateOpt(updateOpt))
			if err != nil {
				return errors.WithStack(err)
			}
			results = append(results, result)
		}

		return nil
	})
	if err != nil {
		return nil, errors.WithStack(err)
	}

	return results, nil
}

func (l lodgingUnitRepository) List(ctx context.Context, where *models.LodgingUnitWhereOpt) ([]*models.LodgingUnit, error) {
	result := make([]*models.LodgingUnit, 0)
	err := l.db.WithContext(ctx).Where(gormx.Query(where)).Order("sort").Find(&result).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return result, nil
}

func (l lodgingUnitRepository) MGet(ctx context.Context, idList []int64) ([]*do.LodgingUnitDO, error) {
	if len(idList) == 0 {
		return make([]*do.LodgingUnitDO, 0), nil
	}
	where := models.LodgingUnitWhereOpt{IDIn: &idList}
	result := make([]*models.LodgingUnit, 0)
	err := l.db.WithContext(ctx).Unscoped().Where(gormx.Query(where)).Find(&result).Error
	if err != nil {
		return nil, errors.WithStack(err)
	}
	return converter.ConvertLodgingUnitPOListToDO(result), nil
}

func (l lodgingUnitRepository) BatchDelete(ctx context.Context, ids []int64, companyId *int64, deletedBy *int64) error {
	if len(ids) == 0 {
		return nil
	}
	where := &models.LodgingUnitWhereOpt{
		IDIn:      &ids,
		CompanyID: companyId,
	}
	update := &models.LodgingUnitUpdateOpt{
		DeletedAt: &gorm.DeletedAt{Valid: true, Time: time.Now()},
		DeletedBy: deletedBy,
	}
	err := l.db.WithContext(ctx).Model(&models.LodgingUnit{}).Where(gormx.Query(where)).Updates(gormx.Update(update)).Error
	return errors.WithStack(err)
}

func (l lodgingUnitRepository) IsExist(ctx context.Context, companyID *int64, whereOpt *do.LodgingUnitWhereOpt) (bool, error) {
	where := converter.ConvertLodgingUnitWhereOptDoToModel(whereOpt)
	where.CompanyID = companyID
	result := make([]*models.LodgingUnit, 0)
	err := l.db.WithContext(ctx).Where(gormx.Query(where)).Limit(1).Find(&result).Error
	if err != nil {
		return false, errors.WithStack(err)
	}
	return len(result) > 0, nil
}

func (l lodgingUnitRepository) GetMaxSort(ctx context.Context, whereOpt *do.LodgingUnitWhereOpt) (int32, error) {
	result := models.LodgingUnit{}
	where := converter.ConvertLodgingUnitWhereOptDoToModel(whereOpt)
	tx := l.db.WithContext(ctx).Model(&models.LodgingUnit{}).Where(gormx.Query(where)).Order("sort desc").Limit(1).Find(&result)
	if tx.Error != nil {
		return 0, errors.WithStack(tx.Error)
	}
	if tx.RowsAffected == 0 {
		return 0, nil
	}
	return result.Sort, nil
}

func NewLodgingUnitRepository(db *gorm.DB) LodgingUnitRepository {
	return &lodgingUnitRepository{db: db}
}
