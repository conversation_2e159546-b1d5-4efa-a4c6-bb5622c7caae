package repository

import (
	"context"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/pkg/errors"
	"github.com/samber/lo"
	"gorm.io/gorm"
	"gorm.io/gormx"
	"sort"
	"strings"
	"time"
	"unicode"
)

type CustomizeCareTypeRepository interface {
	List(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, filter *offeringsvcpb.WhiteListFilter) ([]*do.CustomizeCareTypeDO, error)
	Update(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, updateOpt *models.CustomizeCareTypeUpdateOpt) error
	IsNameDuplicate(ctx context.Context, companyId int64, name string, serviceItemType offeringpb.ServiceItemType, filter *offeringsvcpb.WhiteListFilter) (bool, error)
}

type customizeCareTypeRepository struct {
	db *gorm.DB
}

func NewCustomizeCareTypeRepository(db *gorm.DB) CustomizeCareTypeRepository {
	return &customizeCareTypeRepository{
		db: db,
	}
}

func (c customizeCareTypeRepository) Update(ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, updateOpt *models.CustomizeCareTypeUpdateOpt) error {
	if whereOpt == nil || updateOpt == nil {
		return errors.New("whereOpt and updateOpt cannot be nil")
	}

	db := c.db.WithContext(ctx)
	whereQuery := gormx.Query(whereOpt)

	// 尝试直接更新（影响行数为0表示不存在）
	result := db.Model(&models.CustomizeCareType{}).
		Where(whereQuery).
		Updates(gormx.Update(updateOpt))

	if err := result.Error; err != nil {
		// 唯一键冲突特殊处理
		if errors.Is(err, gorm.ErrDuplicatedKey) {
			return errors.Wrap(err, "duplicate key violates unique constraint")
		}
		return errors.Wrap(err, "failed to update customize care types")
	}

	// 更新成功（影响行数>0）
	if result.RowsAffected > 0 {
		return nil
	}

	// 不存在则创建新记录
	record := toCustomizeCareType(*updateOpt.Name, *whereOpt.CompanyID, whereOpt.ServiceItemType, *updateOpt.UpdatedBy, time.Now())

	if err := db.Create(record).Error; err != nil {
		return errors.Wrap(err, "failed to create customize care types")
	}

	return nil
}

func (c customizeCareTypeRepository) List(
	ctx context.Context, whereOpt *models.CustomizeCareTypeWhereOpt, filter *offeringsvcpb.WhiteListFilter) ([]*do.CustomizeCareTypeDO, error) {
	if whereOpt.CompanyID == nil {
		return nil, errors.New("companyId cannot be nil")
	}
	// build default records
	records := buildDefaultCareTypeDoList(*whereOpt.CompanyID, filter)

	// override records
	query := c.db.WithContext(ctx).Where("company_id = ?", *whereOpt.CompanyID).Order("sort ASC")

	overrideRecords := make([]*models.CustomizeCareType, 0)
	if err := query.Find(&overrideRecords).Error; err != nil {
		return nil, errors.WithStack(err)
	}

	overrideMap := make(map[offeringpb.ServiceItemType]string)
	for _, o := range overrideRecords {
		overrideMap[o.ServiceItemType] = o.Name
	}
	for _, record := range records {
		if name, ok := overrideMap[record.ServiceItemType]; ok {
			record.Name = name
		}
	}

	sort.Slice(records, func(i, j int) bool {
		return records[i].Sort < records[j].Sort
	})

	return records, nil
}

func buildDefaultCareTypeDoList(companyId int64, filter *offeringsvcpb.WhiteListFilter) []*do.CustomizeCareTypeDO {
	records := make([]*do.CustomizeCareTypeDO, 0)

	// Grooming
	name := FormatEnumString(offeringpb.ServiceItemType_name[int32(offeringpb.ServiceItemType_GROOMING)])
	records = append(records, toCustomizeCareTypeDo(name, companyId, offeringpb.ServiceItemType_GROOMING))

	// is in Boarding/Daycare/Evaluation white list
	if filter.GetIsAllowBoardingAndDaycare() {
		for _, serviceType := range initBoardingAndDaycareTypes {
			name := FormatEnumString(offeringpb.ServiceItemType_name[int32(serviceType)])
			records = append(records, toCustomizeCareTypeDo(name, companyId, serviceType))
		}
	}

	// is in GroupClass white list
	if filter.GetIsAllowGroupClass() {
		name := FormatEnumString(offeringpb.ServiceItemType_name[int32(offeringpb.ServiceItemType_GROUP_CLASS)])
		records = append(records, toCustomizeCareTypeDo(name, companyId, offeringpb.ServiceItemType_GROUP_CLASS))
	}

	// is in DogWalking white list
	if filter.GetIsAllowDogWalking() {
		name := FormatEnumString(offeringpb.ServiceItemType_name[int32(offeringpb.ServiceItemType_DOG_WALKING)])
		records = append(records, toCustomizeCareTypeDo(name, companyId, offeringpb.ServiceItemType_DOG_WALKING))
	}
	return records
}

func toCustomizeCareTypeDo(name string, companyId int64, serviceItemType offeringpb.ServiceItemType) *do.CustomizeCareTypeDO {
	return &do.CustomizeCareTypeDO{
		Name:            name,
		CompanyID:       companyId,
		ServiceItemType: serviceItemType,
		Sort:            InitSort(serviceItemType),
	}
}

func toCustomizeCareType(
	name string, companyId int64, serviceItemType offeringpb.ServiceItemType, staffId int64, recordTime time.Time) *models.CustomizeCareType {
	return &models.CustomizeCareType{
		Name:            FormatEnumString(name),
		CompanyID:       companyId,
		ServiceItemType: serviceItemType,
		CreatedAt:       &recordTime,
		UpdatedAt:       &recordTime,
		UpdatedBy:       staffId,
		Sort:            InitSort(serviceItemType),
	}
}

var initBoardingAndDaycareTypes = []offeringpb.ServiceItemType{
	offeringpb.ServiceItemType_BOARDING,
	offeringpb.ServiceItemType_DAYCARE,
	offeringpb.ServiceItemType_EVALUATION,
}

var serviceItemTypeSortMap = map[offeringpb.ServiceItemType]int32{
	offeringpb.ServiceItemType_BOARDING:    1,
	offeringpb.ServiceItemType_DAYCARE:     2,
	offeringpb.ServiceItemType_GROOMING:    3,
	offeringpb.ServiceItemType_DOG_WALKING: 4,
	offeringpb.ServiceItemType_GROUP_CLASS: 5,
	offeringpb.ServiceItemType_EVALUATION:  6,
}

func InitSort(itemType offeringpb.ServiceItemType) int32 {
	if initSort, ok := serviceItemTypeSortMap[itemType]; ok {
		return initSort
	}
	return 0
}

// IsNameDuplicate 检查名称是否重复
func (c customizeCareTypeRepository) IsNameDuplicate(
	ctx context.Context, companyId int64, name string, serviceItemType offeringpb.ServiceItemType, filter *offeringsvcpb.WhiteListFilter) (bool, error) {

	trimmedName := strings.TrimSpace(name)
	if trimmedName == "" {
		return false, errors.New("name cannot be empty")
	}

	var existing []do.CustomizeCareTypeDO
	list, err := c.List(ctx, &models.CustomizeCareTypeWhereOpt{CompanyID: &companyId}, filter)
	if err != nil {
		return false, errors.Wrap(err, "failed to list customize care types")
	}

	list = lo.Filter(list, func(item *do.CustomizeCareTypeDO, _ int) bool {
		return item.ServiceItemType != serviceItemType
	})

	for _, item := range list {
		if strings.EqualFold(item.Name, trimmedName) {
			existing = append(existing, *item)
		}
	}

	return len(existing) > 0, nil
}

func FormatEnumString(s string) string {
	parts := strings.Split(s, "_")
	for i, part := range parts {
		if len(part) == 0 {
			continue
		}
		parts[i] = strings.ToLower(part)
	}
	if len(parts) == 0 {
		return ""
	}
	runes := []rune(parts[0])
	runes[0] = unicode.ToUpper(runes[0])
	parts[0] = string(runes)
	return strings.Join(parts, " ")
}
