package repository

import (
	"context"

	"gorm.io/gorm"

	utilspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"

	"github.com/MoeGolibrary/moego-svc-offering/internal/converter"
	"github.com/MoeGolibrary/moego-svc-offering/internal/do"
	"github.com/MoeGolibrary/moego-svc-offering/internal/models"
	"github.com/MoeGolibrary/moego-svc-offering/internal/utils"
)

type GroupClassInstanceRepository interface {
	Insert(ctx context.Context, do *do.GroupClassInstanceDO) (*do.GroupClassInstanceDO, error)
	Get(ctx context.Context, id int64) (*do.GroupClassInstanceDO, error)
	Update(ctx context.Context, instance *do.GroupClassInstanceDO) error
	List(ctx context.Context, filter *do.GroupClassInstanceFilter,
		pagination *utilspb.PaginationRequest) ([]*do.GroupClassInstanceDO, *utilspb.PaginationResponse, error)
	Delete(ctx context.Context, id int64) error
	CountGroupByStatus(ctx context.Context,
		filter *do.GroupClassInstanceFilter) ([]*models.GroupClassInstanceCount, error)
	CountGroupByServiceID(ctx context.Context,
		filter *do.GroupClassInstanceFilter) ([]*models.GroupClassInstanceCount, error)
}

func NewGroupClassInstanceRepository(db *gorm.DB) GroupClassInstanceRepository {
	return &groupClassInstanceRepository{
		db: db,
	}
}

type groupClassInstanceRepository struct {
	db *gorm.DB
}

func (r *groupClassInstanceRepository) Insert(ctx context.Context,
	do *do.GroupClassInstanceDO) (*do.GroupClassInstanceDO, error) {
	po, err := converter.ConvertGroupClassInstanceDOToPO(do)
	if err != nil {
		return nil, err
	}
	if err := r.db.WithContext(ctx).Omit("id").Create(po).Error; err != nil {
		return nil, err
	}
	return converter.ConvertGroupClassInstancePOToDO(po)
}

func (r *groupClassInstanceRepository) Get(ctx context.Context,
	id int64) (*do.GroupClassInstanceDO, error) {
	var s models.GroupClassInstance
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&s).Error; err != nil {
		return nil, err
	}
	return converter.ConvertGroupClassInstancePOToDO(&s)
}

func (r *groupClassInstanceRepository) Update(ctx context.Context, instance *do.GroupClassInstanceDO) error {
	po, err := converter.ConvertGroupClassInstanceDOToPO(instance)
	if err != nil {
		return err
	}
	return r.db.WithContext(ctx).Model(&models.GroupClassInstance{}).
		Where("id = ?", instance.ID).
		Updates(po).Error
}

func (r *groupClassInstanceRepository) CountGroupByStatus(ctx context.Context,
	filter *do.GroupClassInstanceFilter) ([]*models.GroupClassInstanceCount, error) {
	db := r.db.WithContext(ctx).Model(&models.GroupClassInstance{}).Scopes(filter.Apply)

	var results []*models.GroupClassInstanceCount
	if err := db.Select("status, COUNT(*) as count").
		Group("status").
		Scan(&results).Error; err != nil {
		return nil, err
	}
	return results, nil
}

func (r *groupClassInstanceRepository) CountGroupByServiceID(ctx context.Context,
	filter *do.GroupClassInstanceFilter) ([]*models.GroupClassInstanceCount, error) {
	db := r.db.WithContext(ctx).Model(&models.GroupClassInstance{}).Scopes(filter.Apply)

	var results []*models.GroupClassInstanceCount
	if err := db.Select("service_id, COUNT(*) as count").
		Group("service_id").
		Scan(&results).Error; err != nil {
		return nil, err
	}

	return results, nil
}

func (r *groupClassInstanceRepository) List(ctx context.Context, filter *do.GroupClassInstanceFilter,
	page *utilspb.PaginationRequest) ([]*do.GroupClassInstanceDO, *utilspb.PaginationResponse, error) {
	db := r.db.WithContext(ctx).Model(&models.GroupClassInstance{}).
		Scopes(filter.Apply)

	var (
		results []*models.GroupClassInstance
		count   int64
	)

	if err := db.
		Count(&count).
		Order("start_time").
		Scopes(utils.BuildPageScopes(page)).
		Find(&results).Error; err != nil {
		return nil, nil, err
	}

	dos, err := converter.ConvertGroupClassInstancePOListToDO(results)
	if err != nil {
		return nil, nil, err
	}
	return dos, &utilspb.PaginationResponse{
		Total:    int32(count),
		PageSize: page.GetPageSize(),
		PageNum:  page.GetPageNum(),
	}, nil
}

func (r *groupClassInstanceRepository) Delete(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).
		Where("id = ?", id).Delete(&models.GroupClassInstance{}).Error
}
