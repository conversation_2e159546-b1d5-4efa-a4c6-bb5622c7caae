package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	_ "github.com/MoeGolibrary/go-lib/gorm/serializer"
	"github.com/MoeGolibrary/go-lib/server"
	"github.com/MoeGolibrary/go-lib/zlog"
	offeringV1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	offeringV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v2"
	"github.com/MoeGolibrary/moego-svc-offering/config"
	"github.com/MoeGolibrary/moego-svc-offering/internal/controller"
	controllerV2 "github.com/MoeGolibrary/moego-svc-offering/internal/controller/v2"
	"github.com/MoeGolibrary/moego-svc-offering/internal/resource"
)

func main() {
	config.Init()

	resource.InitTimezone()
	// 初始化日志
	zlog.InitLogger(zlog.NewConfig())
	// 初始化数据库
	resource.InitDB(config.GetConfig())
	// 初始化 grpc client
	resource.InitClient()

	controllers := controller.InitControllers()
	controllersV2 := controllerV2.InitControllersV2()

	s := server.NewDefaultServer()
	s.RegisterService(&offeringV1.ServiceManagementService_ServiceDesc, controller.NewOfferingService())
	s.RegisterService(&offeringV1.LodgingTypeService_ServiceDesc, controller.NewLodgingTypeService())
	s.RegisterService(&offeringV1.LodgingUnitService_ServiceDesc, controller.NewLodgingUnitService())
	s.RegisterService(&offeringV1.EvaluationService_ServiceDesc, controller.NewEvaluationService())
	s.RegisterService(&offeringV1.AutoRolloverRuleService_ServiceDesc, controller.NewAutoRolloverRuleService())
	s.RegisterService(&offeringV1.PricingRuleService_ServiceDesc, controller.NewPricingRuleService())
	s.RegisterService(&offeringV1.ServiceStaffOverrideRuleService_ServiceDesc, controller.NewServiceStaffOverrideRuleService())
	s.RegisterService(&offeringV1.GroupClassService_ServiceDesc, controllers.GroupClassController)
	s.RegisterService(&offeringV1.ServiceVaccineRequirementService_ServiceDesc, controllers.VaccineRequirementController)
	s.RegisterService(&offeringV2.PricingRuleService_ServiceDesc, controllersV2.PricingRuleController)
	s.RegisterService(&offeringV1.PlaygroupService_ServiceDesc, controllers.PlaygroupController)
	s.RegisterService(&offeringV1.MessageDeliverService_ServiceDesc, controllers.MessageDeliverController)
	s.RegisterService(&offeringV1.CustomizeCareTypeService_ServiceDesc, controllers.CustomizeCareTypeController)

	s.Start()
	log.Println("server started")

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c

	s.Stop()
	log.Println("server stopped")
}
