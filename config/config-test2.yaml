port: 9090
db:
  offering_db:
    dsn: postgresql://${secret.datasource.default.postgres.moego_offering.username}:${secret.datasource.default.postgres.moego_offering.password}@${secret.datasource.default.postgres.url.master}:${secret.datasource.default.postgres.port}/moego_offering?sslmode=disable
    max_open_connections: 100
    max_idle_connections: 10
  grooming_db:
    dsn: ${secret.datasource.grooming.mysql.username}:${secret.datasource.grooming.mysql.password}@tcp(${secret.datasource.grooming.mysql.url}:${secret.datasource.grooming.mysql.port})/moe_grooming
    max_open_connections: 100
    max_idle_connections: 10
debug: false
jaeger_url: http://jaeger-collector.jaeger.svc.cluster.local:14268/api/traces
event_bus:
  enable: true
  group_class:
    brokers:
      - ${secret.mq.kafka.broker_url_0}
      - ${secret.mq.kafka.broker_url_1}
      - ${secret.mq.kafka.broker_url_2}
    producer:
      topic: moego.offering
    credential:
      region: ${secret.aws.region}
      access_key_id: ${secret.aws.access_key_id}
      secret_access_key: ${secret.aws.secret_access_key}

secrets:
  - name: "moego/testing/datasource"
    prefix: "secret.datasource.default."
  - name: "moego/testing/datasource/grooming"
    prefix: "secret.datasource.grooming."
  - name: "mq"
    prefix: "secret.mq."
