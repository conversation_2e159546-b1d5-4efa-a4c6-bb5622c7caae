package config

import (
	"context"
	"sync"
	"time"

	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/conf"
	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
)

var (
	once sync.Once
)

var config *Config

type Config struct {
	Port      int    `yaml:"port"`
	Debug     bool   `yaml:"debug"`
	JaegerURL string `yaml:"jaeger_url"`
	DB        struct {
		OfferingDB DBConfig `yaml:"offering_db"`
		GroomingDB DBConfig `yaml:"grooming_db"`
	} `yaml:"db"`
	EventBus struct {
		Enable     *bool            `yaml:"enable"`
		GroupClass *eventbus.Config `yaml:"group_class"`
	} `yaml:"event_bus"`
}

type DBConfig struct {
	DSN                   string        `yaml:"dsn"`
	MaxOpenConnections    int           `yaml:"max_open_connections"`
	MaxIdleConnections    int           `yaml:"max_idle_connections"`
	MaxConnectionLifetime time.Duration `yaml:"max_connection_lifetime"`
}

func GetConfig() *Config {
	return config
}

func GetPort() int {
	return config.Port
}

// Init 读取配置文件
func Init() {
	once.Do(func() {
		config = &Config{}
		loader := conf.GetDefaultLoader()
		if err := loader.InitializeConfig(config); err != nil {
			zlog.Fatal(context.Background(),
				"initialize config failed",
				zap.Error(err))
		}
	})
}
