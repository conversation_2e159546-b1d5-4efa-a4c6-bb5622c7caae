port: 9090
db:
  offering_db:
    dsn: postgresql://moego_developer_240310_eff7a0dc:<EMAIL>:40132/moego_offering?sslmode=disable
    max_open_connections: 100
    max_idle_connections: 10
  grooming_db:
    dsn: moego_developer_240310_eff7a0dc:G0MxI7NM_jX_f7Ky73vnrwej97xg1tly@tcp(mysql.t2.moego.dev:40107)/moe_grooming
    max_open_connections: 100
    max_idle_connections: 10
debug: false
event_bus:
  enable: false
  group_class:
    brokers:
      - kafka.kafka.svc.cluster.local:9092
    producer:
      topic: moego.offering
jaeger_url: http://jaeger-collector.jaeger.svc.cluster.local:14268/api/traces